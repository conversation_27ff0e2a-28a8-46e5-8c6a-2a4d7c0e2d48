<?php
require_once 'includes/header.php';
require_once 'includes/video_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if course ID is provided
if (!isset($_GET['course_id']) || !is_numeric($_GET['course_id'])) {
    Utilities::setFlashMessage('error', 'Invalid course ID.');
    Utilities::redirect('courses.php');
}

$courseId = $_GET['course_id'];

// Fetch course data
$courseQuery = "SELECT * FROM courses WHERE id = ?";
$courseStmt = $conn->prepare($courseQuery);
$courseStmt->bind_param("i", $courseId);
$courseStmt->execute();
$courseResult = $courseStmt->get_result();

if ($courseResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Course not found.');
    Utilities::redirect('courses.php');
}

$course = $courseResult->fetch_assoc();

// Handle video deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $videoId = $_GET['delete'];

    // Delete the video
    $deleteQuery = "DELETE FROM course_videos WHERE id = ? AND course_id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("ii", $videoId, $courseId);

    if ($stmt->execute()) {
        Utilities::setFlashMessage('success', 'Video deleted successfully.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to delete video.');
    }

    Utilities::redirect('course_videos.php?course_id=' . $courseId);
}

// Handle form submission for adding a new video
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $title = Utilities::sanitizeInput($_POST['title'] ?? '');
    $description = Utilities::sanitizeInput($_POST['description'] ?? '');
    $videoUrl = Utilities::sanitizeInput($_POST['video_url'] ?? '');
    $thumbnailUrl = Utilities::sanitizeInput($_POST['thumbnail_url'] ?? '');
    $durationMinutes = intval($_POST['duration_minutes'] ?? 0);
    $weekNumber = intval($_POST['week_number'] ?? 1);
    $sequenceNumber = intval($_POST['sequence_number'] ?? 1);
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate inputs
    $errors = [];

    if (empty($title)) {
        $errors[] = 'Video title is required.';
    }

    if (empty($videoUrl)) {
        $errors[] = 'Video URL is required.';
    }

    if ($weekNumber < 1 || $weekNumber > $course['duration_weeks']) {
        $errors[] = 'Week number must be between 1 and ' . $course['duration_weeks'] . '.';
    }

    if ($sequenceNumber < 1) {
        $errors[] = 'Sequence number must be at least 1.';
    }

    // Check if the week and sequence combination already exists
    $checkQuery = "SELECT id FROM course_videos WHERE course_id = ? AND week_number = ? AND sequence_number = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("iii", $courseId, $weekNumber, $sequenceNumber);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows > 0) {
        $errors[] = 'A video with this week and sequence number already exists.';
    }

    // If no errors, insert the video
    if (empty($errors)) {
        $insertQuery = "INSERT INTO course_videos (course_id, title, description, video_url, thumbnail_url, duration_minutes, week_number, sequence_number, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("issssiiis", $courseId, $title, $description, $videoUrl, $thumbnailUrl, $durationMinutes, $weekNumber, $sequenceNumber, $isActive);

        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Video added successfully.');
            Utilities::redirect('course_videos.php?course_id=' . $courseId);
        } else {
            $errors[] = 'Failed to add video: ' . $conn->error;
        }
    }
}

// Get all videos for this course
$query = "SELECT * FROM course_videos WHERE course_id = ? ORDER BY week_number, sequence_number";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $courseId);
$stmt->execute();
$result = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Course Videos: <?php echo htmlspecialchars($course['title']); ?></h1>
    <a href="courses.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Courses
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Videos</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Week</th>
                                <th>Seq</th>
                                <th>Title</th>
                                <th>Duration</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($result && $result->num_rows > 0): ?>
                                <?php while ($video = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $video['week_number']; ?></td>
                                        <td><?php echo $video['sequence_number']; ?></td>
                                        <td><?php echo htmlspecialchars($video['title']); ?></td>
                                        <td><?php echo $video['duration_minutes'] ? $video['duration_minutes'] . ' min' : 'N/A'; ?></td>
                                        <td>
                                            <?php if ($video['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="video_edit.php?id=<?php echo $video['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a href="course_videos.php?course_id=<?php echo $courseId; ?>&delete=<?php echo $video['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this video?')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">No videos found for this course</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Add New Video</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="mb-3">
                        <label for="title" class="form-label">Video Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="video_url" class="form-label">Video URL (Vimeo Share Link)</label>
                        <input type="url" class="form-control" id="video_url" name="video_url"
                               placeholder="https://vimeo.com/123456789" required>
                        <div class="form-text">Paste a Vimeo share link (e.g., https://vimeo.com/123456789)</div>
                    </div>

                    <div id="vimeoPreview" class="mb-3 d-none">
                        <label class="form-label">Video Preview</label>
                        <div class="ratio ratio-16x9 mb-2">
                            <iframe id="vimeoFrame" src="" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                        </div>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> Valid Vimeo video detected
                        </div>
                    </div>

                    <div id="vimeoError" class="mb-3 d-none">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <span id="vimeoErrorMessage">Invalid Vimeo URL. Please enter a valid Vimeo share link.</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="thumbnail_url" class="form-label">Thumbnail URL (Optional)</label>
                        <input type="url" class="form-control" id="thumbnail_url" name="thumbnail_url"
                               placeholder="Leave empty to use Vimeo's thumbnail">
                        <div class="form-text">Leave empty to use the Vimeo video's thumbnail</div>
                    </div>

                    <div class="mb-3">
                        <label for="duration_minutes" class="form-label">Duration (Minutes)</label>
                        <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="0">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="week_number" class="form-label">Week Number</label>
                            <input type="number" class="form-control" id="week_number" name="week_number" min="1" max="<?php echo $course['duration_weeks']; ?>" value="1" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="sequence_number" class="form-label">Sequence Number</label>
                            <input type="number" class="form-control" id="sequence_number" name="sequence_number" min="1" value="1" required>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Add Video
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const videoUrlInput = document.getElementById('video_url');
    const vimeoPreview = document.getElementById('vimeoPreview');
    const vimeoFrame = document.getElementById('vimeoFrame');
    const vimeoError = document.getElementById('vimeoError');
    const vimeoErrorMessage = document.getElementById('vimeoErrorMessage');

    // Function to extract Vimeo ID from URL
    function extractVimeoId(url) {
        if (!url) return null;

        const patterns = [
            /vimeo\.com\/([0-9]+)/,
            /player\.vimeo\.com\/video\/([0-9]+)/,
            /vimeo\.com\/([0-9]+)\/[a-zA-Z0-9]+/,
            /player\.vimeo\.com\/video\/([0-9]+)\?h=[a-zA-Z0-9]+/
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }

        return null;
    }

    // Function to validate and preview Vimeo URL
    function validateVimeoUrl() {
        const url = videoUrlInput.value.trim();
        const videoId = extractVimeoId(url);

        // Hide both preview and error initially
        vimeoPreview.classList.add('d-none');
        vimeoError.classList.add('d-none');

        if (!url) {
            return; // Empty input, do nothing
        }

        if (videoId) {
            // Valid Vimeo URL
            const embedUrl = `https://player.vimeo.com/video/${videoId}`;
            vimeoFrame.src = embedUrl;
            vimeoPreview.classList.remove('d-none');
        } else {
            // Invalid URL
            vimeoErrorMessage.textContent = 'Invalid Vimeo URL. Please enter a valid Vimeo share link.';
            vimeoError.classList.remove('d-none');
        }
    }

    // Add event listener for input changes
    videoUrlInput.addEventListener('input', validateVimeoUrl);
    videoUrlInput.addEventListener('paste', function() {
        // Short delay to allow paste to complete
        setTimeout(validateVimeoUrl, 100);
    });

    // Initial validation if URL is already present
    validateVimeoUrl();
});
</script>
