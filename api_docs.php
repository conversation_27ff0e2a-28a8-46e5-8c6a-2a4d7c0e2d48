<?php
require_once 'includes/header.php';

// Check if user has admin privileges
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('warning', 'You do not have permission to access the API documentation.');
    Utilities::redirect('index.php');
}

// Base URL for API endpoints
$baseUrl = str_replace('/admin', '/admin/api', APP_URL);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>API Documentation</h1>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i> This documentation provides details on the available API endpoints for the KFT Fitness mobile app.
</div>

<!-- Authentication -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">Authentication</h5>
    </div>
    <div class="card-body">
        <p>All API endpoints except for login require authentication using a Bearer token.</p>
        
        <h6 class="mt-4">Login</h6>
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th width="150">Endpoint</th>
                        <td><code><?php echo $baseUrl; ?>/login.php</code></td>
                    </tr>
                    <tr>
                        <th>Method</th>
                        <td><span class="badge bg-success">POST</span></td>
                    </tr>
                    <tr>
                        <th>Request Body</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "username": "string",
  "password": "string"
}</code></pre>
                        </td>
                    </tr>
                    <tr>
                        <th>Response</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": 1,
    "username": "johndoe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30,
    "height": 175,
    "weight": 70,
    "is_premium": true,
    "is_active": true
  },
  "token": "string",
  "expires_at": "2023-12-31 23:59:59"
}</code></pre>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h6 class="mt-4">Authentication Header</h6>
        <p>For all other endpoints, include the following header:</p>
        <pre class="bg-light p-3 rounded"><code>Authorization: Bearer {token}</code></pre>
    </div>
</div>

<!-- User Profile -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">User Profile</h5>
    </div>
    <div class="card-body">
        <h6>Get User Profile</h6>
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th width="150">Endpoint</th>
                        <td><code><?php echo $baseUrl; ?>/profile.php</code></td>
                    </tr>
                    <tr>
                        <th>Method</th>
                        <td><span class="badge bg-primary">GET</span></td>
                    </tr>
                    <tr>
                        <th>Response</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "user": {
    "id": 1,
    "username": "johndoe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30,
    "height": 175,
    "weight": 70,
    "is_premium": true,
    "is_active": true,
    "created_at": "2023-01-01 12:00:00"
  },
  "bmi_records": [
    {
      "id": 1,
      "weight": 70,
      "bmi": 22.86,
      "recorded_at": "2023-01-01 12:00:00"
    }
  ],
  "streak_days": [
    "2023-01-01",
    "2023-01-02"
  ],
  "current_streak": 2,
  "session_access": {
    "workout": "full",
    "nutrition": "full",
    "progress": "view",
    "premium": "none"
  }
}</code></pre>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h6 class="mt-4">Update User Profile</h6>
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th width="150">Endpoint</th>
                        <td><code><?php echo $baseUrl; ?>/profile.php</code></td>
                    </tr>
                    <tr>
                        <th>Method</th>
                        <td><span class="badge bg-warning text-dark">PUT</span></td>
                    </tr>
                    <tr>
                        <th>Request Body</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "name": "string",
  "email": "string",
  "age": number,
  "height": number,
  "weight": number
}</code></pre>
                        </td>
                    </tr>
                    <tr>
                        <th>Response</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "success": true,
  "message": "Profile updated successfully"
}</code></pre>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Water Reminder -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">Water Reminder</h5>
    </div>
    <div class="card-body">
        <h6>Get Water Reminder Settings</h6>
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th width="150">Endpoint</th>
                        <td><code><?php echo $baseUrl; ?>/water_reminder.php</code></td>
                    </tr>
                    <tr>
                        <th>Method</th>
                        <td><span class="badge bg-primary">GET</span></td>
                    </tr>
                    <tr>
                        <th>Response</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "id": 1,
  "user_id": 1,
  "interval_hours": 2,
  "start_time": "08:00:00",
  "end_time": "22:00:00",
  "is_active": true,
  "created_at": "2023-01-01 12:00:00",
  "updated_at": "2023-01-01 12:00:00"
}</code></pre>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h6 class="mt-4">Update Water Reminder Settings</h6>
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th width="150">Endpoint</th>
                        <td><code><?php echo $baseUrl; ?>/water_reminder.php</code></td>
                    </tr>
                    <tr>
                        <th>Method</th>
                        <td><span class="badge bg-warning text-dark">PUT</span></td>
                    </tr>
                    <tr>
                        <th>Request Body</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "interval_hours": number,
  "start_time": "string",
  "end_time": "string",
  "is_active": boolean
}</code></pre>
                        </td>
                    </tr>
                    <tr>
                        <th>Response</th>
                        <td>
<pre class="bg-light p-3 rounded"><code>{
  "success": true,
  "message": "Water reminder settings updated successfully",
  "reminder": {
    "id": 1,
    "user_id": 1,
    "interval_hours": 2,
    "start_time": "08:00:00",
    "end_time": "22:00:00",
    "is_active": true,
    "created_at": "2023-01-01 12:00:00",
    "updated_at": "2023-01-01 12:00:00"
  }
}</code></pre>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Error Responses -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">Error Responses</h5>
    </div>
    <div class="card-body">
        <p>All API endpoints return the following format for errors:</p>
        <pre class="bg-light p-3 rounded"><code>{
  "error": "Error message description"
}</code></pre>
        
        <h6 class="mt-4">Common HTTP Status Codes</h6>
        <ul>
            <li><strong>200 OK</strong> - Request successful</li>
            <li><strong>400 Bad Request</strong> - Invalid request parameters</li>
            <li><strong>401 Unauthorized</strong> - Missing or invalid authentication token</li>
            <li><strong>403 Forbidden</strong> - Authenticated but not authorized to access the resource</li>
            <li><strong>404 Not Found</strong> - Resource not found</li>
            <li><strong>405 Method Not Allowed</strong> - HTTP method not supported for the endpoint</li>
            <li><strong>500 Internal Server Error</strong> - Server-side error</li>
        </ul>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
