<?php
// Test Vimeo API Connection
header('Content-Type: application/json');

// Your Vimeo credentials
$clientId = 'eb6edcd564c33510af4f3a09a8c40aa7d43b2b87';
$clientSecret = 'KoOnKaeyxukkGZIdKTf4FMi5hoxK7a7S/rdlMwszUL0C2y2ClgaYpV4gdKN/42gHTpINyapIU9/wsPRexb+kbqr7qv8s5t1S+bMAO2RP3EGpGYR41gPL7cM4NKGZfyHR';

// Your actual Personal Access Token
$accessToken = 'e181f678c92844bd2be36504f316fabd';

$videoId = '1087487482';
$privateHash = 'ae75b6e329'; // Private video hash

function testVimeoConnection($accessToken, $videoId) {
    $url = "https://api.vimeo.com/videos/{$videoId}";

    $headers = [
        "Authorization: Bearer {$accessToken}",
        "Content-Type: application/json",
        "Accept: application/vnd.vimeo.*+json;version=3.4"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

function testVideoPrivacySettings($accessToken, $videoId) {
    $result = testVimeoConnection($accessToken, $videoId);

    if ($result['http_code'] === 200 && $result['response']) {
        $data = json_decode($result['response'], true);

        return [
            'success' => true,
            'video_title' => $data['name'] ?? 'Unknown',
            'privacy_view' => $data['privacy']['view'] ?? 'unknown',
            'privacy_embed' => $data['privacy']['embed'] ?? 'unknown',
            'embed_domains' => $data['privacy']['embed_domains'] ?? [],
            'status' => $data['status'] ?? 'unknown',
            'duration' => $data['duration'] ?? 0,
            'embed_html' => $data['embed']['html'] ?? null
        ];
    } else {
        return [
            'success' => false,
            'error' => $result['error'],
            'http_code' => $result['http_code'],
            'response' => $result['response']
        ];
    }
}

function testPrivateVideoAccess($videoId, $privateHash) {
    // Test if the private video URL works
    $privateUrl = "https://vimeo.com/{$videoId}/{$privateHash}";
    $embedUrl = "https://player.vimeo.com/video/{$videoId}?h={$privateHash}";

    return [
        'private_url' => $privateUrl,
        'embed_url' => $embedUrl,
        'video_id' => $videoId,
        'private_hash' => $privateHash
    ];
}

// Test the connection
echo "Testing Vimeo API Connection...\n\n";

if ($accessToken === 'PASTE_YOUR_PERSONAL_ACCESS_TOKEN_HERE') {
    echo json_encode([
        'success' => false,
        'message' => 'Please update the access token in this file',
        'instructions' => [
            '1. Go to https://developer.vimeo.com/apps',
            '2. Find your app with Client ID: ' . $clientId,
            '3. Go to Authentication tab',
            '4. Generate a Personal Access Token with scopes: public, private, video_files, interact',
            '5. Replace PASTE_YOUR_PERSONAL_ACCESS_TOKEN_HERE with your token',
            '6. Run this test again'
        ]
    ], JSON_PRETTY_PRINT);
} else {
    $result = testVideoPrivacySettings($accessToken, $videoId);
    $privateVideoInfo = testPrivateVideoAccess($videoId, $privateHash);

    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Vimeo API connection successful!',
            'video_info' => [
                'title' => $result['video_title'],
                'privacy_view' => $result['privacy_view'],
                'privacy_embed' => $result['privacy_embed'],
                'embed_domains' => $result['embed_domains'],
                'status' => $result['status'],
                'duration' => $result['duration'] . ' seconds'
            ],
            'private_video_info' => $privateVideoInfo,
            'recommendations' => [
                'privacy_view' => $result['privacy_view'] === 'anybody' ?
                    'Consider changing to "password" or "unlisted" for better security' :
                    'Privacy setting looks good',
                'privacy_embed' => $result['privacy_embed'] === 'public' ?
                    'Consider changing to "whitelist" and add your domains' :
                    'Embed privacy setting looks good'
            ]
        ], JSON_PRETTY_PRINT);
    } else {
        // API failed, but let's still provide private video info
        echo json_encode([
            'success' => false,
            'message' => 'Vimeo API connection failed - This is likely a private video',
            'private_video_info' => $privateVideoInfo,
            'error_details' => [
                'http_code' => $result['http_code'],
                'error' => $result['error'],
                'response' => $result['response']
            ],
            'explanation' => [
                'The video URL contains a private hash (ae75b6e329)',
                'Private videos cannot be accessed via the public API',
                'The video should still work with the correct embed URL',
                'Use the embed_url provided in private_video_info'
            ],
            'troubleshooting' => [
                'This is normal for private Vimeo videos',
                'Use the private embed URL for playback',
                'Ensure domain restrictions allow your server IP',
                'Test the embed URL directly in a browser'
            ]
        ], JSON_PRETTY_PRINT);
    }
}
?>
