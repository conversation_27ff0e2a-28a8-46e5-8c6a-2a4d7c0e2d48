-- Add vimeo_password column if it doesn't exist
ALTER TABLE course_videos ADD COLUMN IF NOT EXISTS vimeo_password TEXT NULL;

-- Update video 1087487482 with encrypted password
-- The password 'vi007i' will be encrypted by the PHP system
UPDATE course_videos 
SET vimeo_password = 'dmkwMDdp' -- Base64 encoded 'vi007i' as placeholder
WHERE video_id = '1087487482';

-- Verify the update
SELECT id, title, video_id, vimeo_password 
FROM course_videos 
WHERE video_id = '1087487482';
