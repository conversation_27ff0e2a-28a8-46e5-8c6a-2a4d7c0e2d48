<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';
require_once 'includes/audit_logger.php';

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to assign users to staff members.');
    Utilities::redirect('index.php');
    exit;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Utilities::setFlashMessage('error', 'Invalid request method.');
    Utilities::redirect('users.php');
    exit;
}

// Check if required parameters are provided
if (!isset($_POST['assigned_staff_id']) || !is_numeric($_POST['assigned_staff_id']) ||
    !isset($_POST['user_ids']) || !is_array($_POST['user_ids']) || empty($_POST['user_ids'])) {
    Utilities::setFlashMessage('error', 'Missing required parameters.');
    Utilities::redirect('users.php');
    exit;
}

$staffId = (int)$_POST['assigned_staff_id'];
$userIds = array_map('intval', $_POST['user_ids']);

// Verify staff exists and is a staff member
$staffQuery = "SELECT id, name FROM admin_users WHERE id = ? AND role = 'staff'";
$staffStmt = $conn->prepare($staffQuery);
$staffStmt->bind_param("i", $staffId);
$staffStmt->execute();
$staffResult = $staffStmt->get_result();

if ($staffResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Staff member not found.');
    Utilities::redirect('users.php');
    exit;
}

$staffInfo = $staffResult->fetch_assoc();

// Begin transaction
$conn->begin_transaction();

try {
    // Update users to assign them to the selected staff member
    $updateQuery = "UPDATE users SET assigned_staff_id = ? WHERE id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")";
    $stmt = $conn->prepare($updateQuery);

    // Create parameter types string (all integers)
    $types = str_repeat('i', count($userIds) + 1);

    // Create parameters array with staff ID as first parameter
    $params = array_merge([$types, $staffId], $userIds);

    // Call bind_param with the unpacked parameters array
    call_user_func_array([$stmt, 'bind_param'], $params);

    $stmt->execute();
    $affectedRows = $stmt->affected_rows;

    // Log the activity in audit logs
    $details = "Assigned $affectedRows users to staff member " . $staffInfo['name'];
    logStaffActivity($conn, $auth->getUserId(), 'bulk_assign_staff', null, $details);

    // Commit transaction
    $conn->commit();

    Utilities::setFlashMessage('success', "$affectedRows users have been assigned to " . htmlspecialchars($staffInfo['name']));
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    Utilities::setFlashMessage('error', 'Failed to assign users: ' . $e->getMessage());
}

// Redirect back to users page
Utilities::redirect('users.php');
?>
