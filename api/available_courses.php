<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Set content type header
header('Content-Type: application/json');

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Try to authenticate user, but allow unauthenticated access
$auth = new Auth($conn);
$userId = null;

try {
    $userId = $auth->authenticateToken();

    if ($userId) {
        // Log successful authentication
        error_log("Available courses API: Authenticated user ID: $userId");
    } else {
        // Allow anonymous access with a guest user ID
        $userId = 0; // Use 0 for guest/anonymous users
        error_log("Available courses API: Anonymous access (no token)");
    }
} catch (Exception $e) {
    // For development, allow access with a default user ID
    if (defined('DEV_MODE') && DEV_MODE === true) {
        $userId = 1; // Use admin user for development
        error_log("Available courses API: Using development user ID: $userId");
    } else {
        // Allow anonymous access with a guest user ID
        $userId = 0; // Use 0 for guest/anonymous users
        error_log("Available courses API: Anonymous access (auth error): " . $e->getMessage());
    }
}

// Get category filter if provided
$category = isset($_GET['category']) ? Utilities::sanitizeInput($_GET['category']) : null;

// Check if course_purchases table exists
$tableCheckQuery = "SHOW TABLES LIKE 'course_purchases'";
$tableCheckResult = $conn->query($tableCheckQuery);
$coursePurchasesExists = $tableCheckResult->num_rows > 0;

// Build query based on filters and available tables
$query = "SELECT c.*,
          (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as total_videos,";

// Use 0 as default if course_purchases table doesn't exist
if ($coursePurchasesExists) {
    $query .= "(SELECT COUNT(*) FROM course_purchases WHERE user_id = ? AND course_id = c.id AND status = 'completed') as is_purchased,";
} else {
    $query .= "0 as is_purchased,";
}

$query .= "(SELECT COUNT(*) FROM user_course_enrollments WHERE user_id = ? AND course_id = c.id AND status = 'active') as is_enrolled
          FROM courses c
          WHERE c.is_active = 1";

// Add category filter if provided
if ($category) {
    $query .= " AND c.category = ?";
}

// Add sorting
// Note: Using is_premium instead of is_featured since is_featured doesn't exist
$query .= " ORDER BY c.is_premium DESC, c.created_at DESC";

// Prepare and execute query
$stmt = $conn->prepare($query);

// Bind parameters based on whether course_purchases table exists and if category filter is provided
if ($coursePurchasesExists) {
    // If course_purchases exists, we need to bind userId twice
    if ($category) {
        $stmt->bind_param("iis", $userId, $userId, $category);
    } else {
        $stmt->bind_param("ii", $userId, $userId);
    }
} else {
    // If course_purchases doesn't exist, we only need to bind userId once
    if ($category) {
        $stmt->bind_param("is", $userId, $category);
    } else {
        $stmt->bind_param("i", $userId);
    }
}

// Execute the query with error handling
if (!$stmt->execute()) {
    error_log("Available courses API: Query execution failed: " . $stmt->error);
    returnError('Failed to load available courses: Database error', 500);
}

$result = $stmt->get_result();

// Log the number of courses found
error_log("Available courses API: Found " . $result->num_rows . " courses");

// Get all categories for filter options
$categoriesQuery = "SELECT DISTINCT category FROM courses WHERE is_active = 1 ORDER BY category";
$categoriesResult = $conn->query($categoriesQuery);
$categories = [];

while ($categoryRow = $categoriesResult->fetch_assoc()) {
    $categories[] = $categoryRow['category'];
}

// Process results
$courses = [];
while ($row = $result->fetch_assoc()) {
    // Calculate discounted price
    $originalPrice = $row['price'];
    // Set default discount percentage to 0 if the column doesn't exist
    $discountPercentage = isset($row['discount_percentage']) ? $row['discount_percentage'] : 0;
    $finalPrice = $originalPrice;

    if ($discountPercentage > 0) {
        $finalPrice = $originalPrice * (1 - $discountPercentage / 100);
    }

    $courses[] = [
        'id' => $row['id'],
        'title' => $row['title'],
        'category' => $row['category'],
        'description' => $row['description'],
        'thumbnail_url' => $row['thumbnail_url'],
        'duration_weeks' => $row['duration_weeks'],
        'price' => [
            'original' => (float)$originalPrice,
            'final' => (float)$finalPrice,
            'discount_percentage' => (int)$discountPercentage,
            'has_discount' => $discountPercentage > 0
        ],
        'is_premium' => (bool)$row['is_premium'],
        'total_videos' => (int)$row['total_videos'],
        'is_purchased' => (bool)$row['is_purchased'],
        'is_enrolled' => (bool)$row['is_enrolled'],
        'whatsapp_number' => $row['whatsapp_number'] ?? '',
        'whatsapp_message_prefix' => $row['whatsapp_message_prefix'] ?? ''
    ];
}

// Check if we have any courses
if (empty($courses)) {
    error_log("Available courses API: No courses found, adding a fallback course");

    // Add a fallback course for testing
    $courses[] = [
        'id' => 999,
        'title' => 'Sample Course (API Fallback)',
        'category' => 'General',
        'description' => 'This is a fallback course shown when no other courses are available. Please contact support if you see this message.',
        'thumbnail_url' => '',
        'duration_weeks' => 4,
        'price' => [
            'original' => 0.0,
            'final' => 0.0,
            'discount_percentage' => 0,
            'has_discount' => false
        ],
        'is_premium' => false,
        'total_videos' => 1,
        'is_purchased' => false,
        'is_enrolled' => false,
        'whatsapp_number' => '',
        'whatsapp_message_prefix' => ''
    ];

    // Add a fallback category if none exist
    if (empty($categories)) {
        $categories[] = 'General';
    }
}

// Return the response
returnResponse([
    'success' => true,
    'courses' => $courses,
    'categories' => $categories
]);

// Use the global helper functions from config.php
?>
