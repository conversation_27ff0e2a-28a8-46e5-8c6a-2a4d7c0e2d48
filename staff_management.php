<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access the Staff Management page.');
    Utilities::redirect('index.php');
}

// Check if permissions_locked column exists
$checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'permissions_locked'";
$checkColumnResult = $conn->query($checkColumnQuery);
$permissionsLockedExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

// Check if is_active column exists
$checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'is_active'";
$checkColumnResult = $conn->query($checkColumnQuery);
$isActiveColumnExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

// If the is_active column doesn't exist, show a message
if (!$isActiveColumnExists) {
    Utilities::setFlashMessage('warning', 'The is_active column does not exist in the admin_users table. Some functionality may be limited. <a href="alter_admin_users_add_is_active.php">Click here to add it</a>.');
}

// Get all staff members
if ($permissionsLockedExists) {
    $staffQuery = "SELECT a.*,
                  (SELECT COUNT(*) FROM users WHERE assigned_staff_id = a.id) as user_count,
                  (SELECT name FROM admin_users WHERE id = a.parent_admin_id) as parent_name,
                  IFNULL(a.permissions_locked, 0) as permissions_locked
                  FROM admin_users a
                  WHERE a.role = 'staff'
                  ORDER BY a.name";
} else {
    $staffQuery = "SELECT a.*,
                  (SELECT COUNT(*) FROM users WHERE assigned_staff_id = a.id) as user_count,
                  (SELECT name FROM admin_users WHERE id = a.parent_admin_id) as parent_name,
                  0 as permissions_locked
                  FROM admin_users a
                  WHERE a.role = 'staff'
                  ORDER BY a.name";
}
$staffResult = $conn->query($staffQuery);
$staffMembers = [];
if ($staffResult && $staffResult->num_rows > 0) {
    while ($staff = $staffResult->fetch_assoc()) {
        $staffMembers[] = $staff;
    }
}

// Handle staff activation/deactivation and override actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $staffId = (int)$_GET['id'];

    if ($action === 'activate') {
        // Check if is_active column exists
        if (!$isActiveColumnExists) {
            Utilities::setFlashMessage('warning', 'Cannot activate staff account: The is_active column does not exist in the admin_users table. <a href="alter_admin_users_add_is_active.php">Click here to add it</a>.');
        } else {
            $updateQuery = "UPDATE admin_users SET is_active = 1 WHERE id = ? AND role = 'staff'";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("i", $staffId);
            if ($stmt->execute()) {
                Utilities::setFlashMessage('success', 'Staff account activated successfully.');

                // Log the activity
                if (function_exists('logStaffActivity')) {
                    $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                    $staffStmt = $conn->prepare($staffQuery);
                    $staffStmt->bind_param("i", $staffId);
                    $staffStmt->execute();
                    $staffName = $staffStmt->get_result()->fetch_assoc()['name'] ?? 'Unknown';

                    $details = "Activated staff account: " . $staffName;
                    logStaffActivity($conn, $auth->getUserId(), 'staff_activate', null, $details);
                }
            } else {
                Utilities::setFlashMessage('danger', 'Failed to activate staff account.');
            }
        }
    } elseif ($action === 'deactivate') {
        // Check if is_active column exists
        if (!$isActiveColumnExists) {
            Utilities::setFlashMessage('warning', 'Cannot deactivate staff account: The is_active column does not exist in the admin_users table. <a href="alter_admin_users_add_is_active.php">Click here to add it</a>.');
        } else {
            $updateQuery = "UPDATE admin_users SET is_active = 0 WHERE id = ? AND role = 'staff'";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("i", $staffId);
            if ($stmt->execute()) {
                Utilities::setFlashMessage('success', 'Staff account deactivated successfully.');

                // Log the activity
                if (function_exists('logStaffActivity')) {
                    $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                    $staffStmt = $conn->prepare($staffQuery);
                    $staffStmt->bind_param("i", $staffId);
                    $staffStmt->execute();
                    $staffName = $staffStmt->get_result()->fetch_assoc()['name'] ?? 'Unknown';

                    $details = "Deactivated staff account: " . $staffName;
                    logStaffActivity($conn, $auth->getUserId(), 'staff_deactivate', null, $details);
                }
            } else {
                Utilities::setFlashMessage('danger', 'Failed to deactivate staff account.');
            }
        }
    } elseif ($action === 'override_lock' && $permissionsLockedExists) {
        // Override staff actions by locking their permissions
        $updateQuery = "UPDATE admin_users SET permissions_locked = 1 WHERE id = ? AND role = 'staff'";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("i", $staffId);
        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Staff permissions have been locked. They cannot make changes until unlocked.');

            // Log the activity
            if (function_exists('logStaffActivity')) {
                $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                $staffStmt = $conn->prepare($staffQuery);
                $staffStmt->bind_param("i", $staffId);
                $staffStmt->execute();
                $staffName = $staffStmt->get_result()->fetch_assoc()['name'] ?? 'Unknown';

                $details = "Locked permissions for staff: " . $staffName;
                logStaffActivity($conn, $auth->getUserId(), 'staff_lock_permissions', null, $details);
            }
        } else {
            Utilities::setFlashMessage('danger', 'Failed to lock staff permissions.');
        }
    } elseif ($action === 'override_unlock' && $permissionsLockedExists) {
        // Remove the override lock on staff permissions
        $updateQuery = "UPDATE admin_users SET permissions_locked = 0 WHERE id = ? AND role = 'staff'";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("i", $staffId);
        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Staff permissions have been unlocked. They can now make changes again.');

            // Log the activity
            if (function_exists('logStaffActivity')) {
                $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                $staffStmt = $conn->prepare($staffQuery);
                $staffStmt->bind_param("i", $staffId);
                $staffStmt->execute();
                $staffName = $staffStmt->get_result()->fetch_assoc()['name'] ?? 'Unknown';

                $details = "Unlocked permissions for staff: " . $staffName;
                logStaffActivity($conn, $auth->getUserId(), 'staff_unlock_permissions', null, $details);
            }
        } else {
            Utilities::setFlashMessage('danger', 'Failed to unlock staff permissions.');
        }
    } elseif ($action === 'delete') {
        // Delete staff member
        $deleteQuery = "DELETE FROM admin_users WHERE id = ? AND role = 'staff'";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $staffId);
        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Staff account deleted successfully.');

            // Log the activity
            if (function_exists('logStaffActivity')) {
                $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                $staffStmt = $conn->prepare($staffQuery);
                $staffStmt->bind_param("i", $staffId);
                $staffStmt->execute();
                $staffName = $staffStmt->get_result()->fetch_assoc()['name'] ?? 'Unknown';

                $details = "Deleted staff account: " . $staffName;
                logStaffActivity($conn, $auth->getUserId(), 'staff_delete', null, $details);
            }
        } else {
            Utilities::setFlashMessage('danger', 'Failed to delete staff account.');
        }
        Utilities::redirect('staff_management.php');
    } elseif (($action === 'override_lock' || $action === 'override_unlock') && !$permissionsLockedExists) {
        // If the permissions_locked column doesn't exist yet, show a message
        Utilities::setFlashMessage('warning', 'The permissions_locked column does not exist in the admin_users table. Please run the alter_admin_users_add_permissions_locked.php script first.');
        Utilities::redirect('staff_management.php');
        exit;
    }

    Utilities::redirect('staff_management.php');
}

// Get performance metrics for each staff member
$staffPerformance = [];
foreach ($staffMembers as $staff) {
    // Get active users percentage
    $activeUsersQuery = "SELECT
                        COUNT(*) as total_users,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users
                        FROM users
                        WHERE assigned_staff_id = ?";
    $stmt = $conn->prepare($activeUsersQuery);
    $stmt->bind_param("i", $staff['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $userStats = $result->fetch_assoc();

    $activePercentage = 0;
    if ($userStats['total_users'] > 0) {
        $activePercentage = round(($userStats['active_users'] / $userStats['total_users']) * 100);
    }

    // Get premium users percentage
    $premiumUsersQuery = "SELECT
                         COUNT(*) as total_users,
                         SUM(CASE WHEN is_premium = 1 THEN 1 ELSE 0 END) as premium_users
                         FROM users
                         WHERE assigned_staff_id = ?";
    $stmt = $conn->prepare($premiumUsersQuery);
    $stmt->bind_param("i", $staff['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $premiumStats = $result->fetch_assoc();

    $premiumPercentage = 0;
    if ($premiumStats['total_users'] > 0) {
        $premiumPercentage = round(($premiumStats['premium_users'] / $premiumStats['total_users']) * 100);
    }

    // Get new users in last 30 days
    $newUsersQuery = "SELECT COUNT(*) as new_users
                     FROM users
                     WHERE assigned_staff_id = ?
                     AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
    $stmt = $conn->prepare($newUsersQuery);
    $stmt->bind_param("i", $staff['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $newUsers = $result->fetch_assoc()['new_users'];

    // Get workout completion rate - since there's no status column, we'll count all workouts as completed
    $workoutCompletionQuery = "SELECT
                              COUNT(*) as total_workouts,
                              COUNT(*) as completed_workouts
                              FROM workout_records
                              JOIN users ON workout_records.user_id = users.id
                              WHERE users.assigned_staff_id = ?";
    $stmt = $conn->prepare($workoutCompletionQuery);
    $stmt->bind_param("i", $staff['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $workoutStats = $result->fetch_assoc();

    $completionRate = 0;
    if ($workoutStats['total_workouts'] > 0) {
        $completionRate = round(($workoutStats['completed_workouts'] / $workoutStats['total_workouts']) * 100);
    }

    $staffPerformance[$staff['id']] = [
        'active_percentage' => $activePercentage,
        'premium_percentage' => $premiumPercentage,
        'new_users' => $newUsers,
        'completion_rate' => $completionRate
    ];
}

// Get all permissions for the permission management modal
$permissionsQuery = "SELECT id, name, slug, description FROM admin_permissions ORDER BY name";
$permissionsResult = $conn->query($permissionsQuery);
$permissions = [];
if ($permissionsResult && $permissionsResult->num_rows > 0) {
    while ($row = $permissionsResult->fetch_assoc()) {
        $permissions[] = $row;
    }
}
?>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}

.staff-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
}

.staff-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* Ensure dropdowns are always visible above table and not clipped */
.table-responsive { overflow: visible !important; }
.dropdown-menu { z-index: 1055 !important; }

/* Fix dropdown clipping in responsive tables */
.dropdown-fix {
    position: absolute !important;
    left: auto !important;
    right: 0 !important;
    top: 100% !important;
    transform: none !important;
    min-width: 220px;
    z-index: 2000 !important;
}

/* Include modern action menu */
@import url('assets/css/modern-action-menu.css');

/* Responsive table: overflow-x only on small screens */
@media (min-width: 992px) {
    .table-responsive {
        overflow: visible !important;
    }
}
@media (max-width: 991.98px) {
    .table-responsive {
        overflow-x: auto !important;
    }
}

.staff-green-btn, .btn-success, .badge.staff-green-btn {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.staff-green-btn:hover, .btn-success:hover, .badge.staff-green-btn:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>

<h1 class="mt-4">Staff Management</h1>
<ol class="breadcrumb mb-4">
    <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
    <li class="breadcrumb-item active">Staff Management</li>
</ol>

<?php Utilities::displayFlashMessages(); ?>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Staff Members</h5>
                    <a href="admin_add.php" class="btn btn-sm btn-success staff-green-btn">
                        <i class="fas fa-user-plus me-2"></i> Add New Staff
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Staff Name</th>
                                    <th>Email</th>
                                    <th>Reports To</th>
                                    <th>Assigned Users</th>
                                    <th>Actions</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($staffMembers)): ?>
                                    <?php foreach ($staffMembers as $staff): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle bg-dark text-white me-3">
                                                        <?php echo strtoupper(substr($staff['name'] ?? 'S', 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <div class="font-weight-bold"><?php echo htmlspecialchars($staff['name']); ?></div>
                                                        <div class="small text-muted">@<?php echo htmlspecialchars($staff['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($staff['email']); ?></td>
                                            <td><?php echo htmlspecialchars($staff['parent_name'] ?? 'N/A'); ?></td>
                                            <td>
                                                <a href="users.php?staff_id=<?php echo $staff['id']; ?>" class="badge staff-green-btn text-decoration-none">
                                                    <?php echo $staff['user_count']; ?> Users
                                                </a>
                                            </td>
                                            <td style="position: static !important;">
                                                <div class="action-menu-container staff-action-container">
                                                    <button type="button" class="action-btn clean-action-btn" aria-label="Staff Actions" title="Staff Actions">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="action-menu clean-action-menu">
                                                        <div class="action-menu-header">Staff Actions</div>
                                                        <a class="action-menu-item clean-menu-item" href="admin_edit.php?id=<?php echo $staff['id']; ?>">
                                                            <i class="fas fa-edit"></i> Edit Profile
                                                        </a>
                                                        <a class="action-menu-item clean-menu-item" href="#" data-bs-toggle="modal" data-bs-target="#permissionsModal" data-staff-id="<?php echo $staff['id']; ?>" data-staff-name="<?php echo htmlspecialchars($staff['name']); ?>">
                                                            <i class="fas fa-key"></i> Manage Permissions
                                                        </a>
                                                        <a class="action-menu-item clean-menu-item" href="#" data-bs-toggle="modal" data-bs-target="#reassignUsersModal" data-staff-id="<?php echo $staff['id']; ?>" data-staff-name="<?php echo htmlspecialchars($staff['name']); ?>">
                                                            <i class="fas fa-users"></i> Reassign Users
                                                        </a>
                                                        <a class="action-menu-item clean-menu-item" href="#" data-bs-toggle="modal" data-bs-target="#performanceModal" data-staff-id="<?php echo $staff['id']; ?>" data-staff-name="<?php echo htmlspecialchars($staff['name']); ?>">
                                                            <i class="fas fa-chart-line"></i> View Performance
                                                        </a>
                                                        <div class="action-menu-divider"></div>

                                                        <!-- Override Controls - Only show if permissions_locked column exists -->
                                                        <?php if ($permissionsLockedExists): ?>
                                                            <?php if (isset($staff['permissions_locked']) && $staff['permissions_locked']): ?>
                                                                <a class="action-menu-item clean-menu-item" href="staff_management.php?action=override_unlock&id=<?php echo $staff['id']; ?>">
                                                                    <i class="fas fa-unlock"></i> Remove Override Lock
                                                                </a>
                                                            <?php else: ?>
                                                                <a class="action-menu-item clean-menu-item" href="staff_management.php?action=override_lock&id=<?php echo $staff['id']; ?>" onclick="return confirm('Are you sure you want to lock this staff member\'s permissions? They will not be able to make changes until unlocked.');">
                                                                    <i class="fas fa-lock"></i> Override & Lock Permissions
                                                                </a>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <a class="action-menu-item clean-menu-item" href="alter_admin_users_add_permissions_locked.php">
                                                                <i class="fas fa-database"></i> Enable Override Controls
                                                            </a>
                                                        <?php endif; ?>

                                                        <div class="action-menu-divider"></div>

                                                        <!-- Activation Controls -->
                                                        <?php if (isset($staff['is_active']) && $staff['is_active']): ?>
                                                            <a class="action-menu-item clean-menu-item danger" href="staff_management.php?action=deactivate&id=<?php echo $staff['id']; ?>" onclick="return confirm('Are you sure you want to deactivate this staff account?');">
                                                                <i class="fas fa-ban"></i> Deactivate Account
                                                            </a>
                                                        <?php else: ?>
                                                            <a class="action-menu-item clean-menu-item" href="staff_management.php?action=activate&id=<?php echo $staff['id']; ?>">
                                                                <i class="fas fa-check"></i> Activate Account
                                                            </a>
                                                        <?php endif; ?>

                                                        <div class="action-menu-divider"></div>
                                                        <a class="action-menu-item clean-menu-item danger" href="staff_management.php?action=delete&id=<?php echo $staff['id']; ?>" onclick="return confirm('Are you sure you want to delete this staff member? This action cannot be undone.');">
                                                            <i class="fas fa-trash"></i> Delete Staff
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?php if (isset($staff['is_active']) && $staff['is_active']): ?>
                                                        <span class="badge staff-green-btn">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-light text-dark">Inactive</span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($permissionsLockedExists && isset($staff['permissions_locked']) && $staff['permissions_locked']): ?>
                                                    <div class="mt-1">
                                                        <span class="badge bg-light text-dark">
                                                            <i class="fas fa-lock me-1"></i> Override Active
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo !empty($staff['last_login']) ? Utilities::formatDate($staff['last_login']) : 'Never'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">No staff members found</div>
                                            <a href="admin_add.php" class="btn btn-sm btn-success staff-green-btn">
                                                <i class="fas fa-user-plus me-2"></i> Add New Staff
                                            </a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Modal -->
<div class="modal fade" id="performanceModal" tabindex="-1" aria-labelledby="performanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="performanceModalLabel">Staff Performance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="performanceContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Modal -->
<div class="modal fade" id="permissionsModal" tabindex="-1" aria-labelledby="permissionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="permissionsModalLabel">Manage Permissions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="permissionsForm" method="post" action="update_permissions.php">
                    <input type="hidden" name="staff_id" id="permissionsStaffId" value="">

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Permissions for <span id="permissionsStaffName"></span></h6>
                            <div>
                                <button type="button" class="btn btn-outline-dark" id="selectAllPermissions">Select All</button>
                                <button type="button" class="btn btn-outline-dark ms-2" id="deselectAllPermissions">Deselect All</button>
                            </div>
                        </div>

                        <div class="row" id="permissionsContainer">
                            <?php foreach ($permissions as $permission): ?>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input permission-checkbox" type="checkbox" name="permissions[]" value="<?php echo $permission['id']; ?>" id="permission_<?php echo $permission['id']; ?>">
                                    <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>">
                                        <?php echo htmlspecialchars($permission['name']); ?>
                                        <small class="d-block text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                    </label>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="savePermissions">Save Permissions</button>
            </div>
        </div>
    </div>
</div>

<!-- Reassign Users Modal -->
<div class="modal fade" id="reassignUsersModal" tabindex="-1" aria-labelledby="reassignUsersModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reassignUsersModalLabel">Reassign Users</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reassignUsersForm" method="post" action="reassign_users.php">
                    <input type="hidden" name="from_staff_id" id="fromStaffId" value="">

                    <div class="mb-3">
                        <p>Reassign users from <strong id="fromStaffName"></strong> to another staff member:</p>

                        <div class="form-group mb-3">
                            <label for="toStaffId" class="form-label">Select Target Staff</label>
                            <select class="form-select" id="toStaffId" name="to_staff_id" required>
                                <option value="">Select Staff Member</option>
                                <?php foreach ($staffMembers as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>"><?php echo htmlspecialchars($staff['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="reassignOption" class="form-label">Reassign Options</label>
                            <select class="form-select" id="reassignOption" name="reassign_option" required>
                                <option value="all">All Users</option>
                                <option value="active">Only Active Users</option>
                                <option value="inactive">Only Inactive Users</option>
                                <option value="premium">Only Premium Users</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-dark" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="saveReassignment">Reassign Users</button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
// Performance Modal
document.addEventListener('DOMContentLoaded', function() {
    const performanceModal = document.getElementById('performanceModal');
    if (performanceModal) {
        performanceModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const staffId = button.getAttribute('data-staff-id');
            const staffName = button.getAttribute('data-staff-name');

            const modalTitle = this.querySelector('.modal-title');
            const performanceContent = this.querySelector('#performanceContent');

            modalTitle.textContent = `Performance Report: ${staffName}`;

            // Get performance data for this staff
            const staffPerformance = <?php echo json_encode($staffPerformance); ?>;
            const performance = staffPerformance[staffId];

            if (performance) {
                let content = `
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <h6 class="card-title">User Activity</h6>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>Active Users</div>
                                    <div class="text-dark fw-bold">${performance.active_percentage}%</div>
                                </div>
                                <div class="progress mt-2" style="height: 8px;">
                                    <div class="progress-bar bg-dark" role="progressbar" style="width: ${performance.active_percentage}%" aria-valuenow="${performance.active_percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <h6 class="card-title">Premium Conversions</h6>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>Premium Users</div>
                                    <div class="text-dark fw-bold">${performance.premium_percentage}%</div>
                                </div>
                                <div class="progress mt-2" style="height: 8px;">
                                    <div class="progress-bar bg-dark" role="progressbar" style="width: ${performance.premium_percentage}%" aria-valuenow="${performance.premium_percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <h6 class="card-title">New Users (Last 30 Days)</h6>
                                <div class="d-flex justify-content-center align-items-center mt-3">
                                    <div class="display-4 fw-bold text-dark">${performance.new_users}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <h6 class="card-title">Workout Completion Rate</h6>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>Completed Workouts</div>
                                    <div class="text-dark fw-bold">${performance.completion_rate}%</div>
                                </div>
                                <div class="progress mt-2" style="height: 8px;">
                                    <div class="progress-bar bg-dark" role="progressbar" style="width: ${performance.completion_rate}%" aria-valuenow="${performance.completion_rate}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="users.php?staff_id=${staffId}" class="btn btn-dark">
                        <i class="fas fa-users me-2"></i> View All Users
                    </a>
                </div>
                `;

                performanceContent.innerHTML = content;
            } else {
                performanceContent.innerHTML = '<div class="alert alert-info">No performance data available for this staff member.</div>';
            }
        });
    }

    // Permissions Modal
    const permissionsModal = document.getElementById('permissionsModal');
    if (permissionsModal) {
        permissionsModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const staffId = button.getAttribute('data-staff-id');
            const staffName = button.getAttribute('data-staff-name');

            document.getElementById('permissionsStaffId').value = staffId;
            document.getElementById('permissionsStaffName').textContent = staffName;

            // Clear all checkboxes
            const checkboxes = document.querySelectorAll('.permission-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // Fetch current permissions for this staff
            fetch(`get_staff_permissions.php?staff_id=${staffId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.permissions) {
                        data.permissions.forEach(permissionId => {
                            const checkbox = document.getElementById(`permission_${permissionId}`);
                            if (checkbox) {
                                checkbox.checked = true;
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching permissions:', error);
                });
        });

        // Select/Deselect All Permissions
        document.getElementById('selectAllPermissions').addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.permission-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        });

        document.getElementById('deselectAllPermissions').addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.permission-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        });

        // Save Permissions
        document.getElementById('savePermissions').addEventListener('click', function() {
            document.getElementById('permissionsForm').submit();
        });
    }

    // Reassign Users Modal
    const reassignUsersModal = document.getElementById('reassignUsersModal');
    if (reassignUsersModal) {
        reassignUsersModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const staffId = button.getAttribute('data-staff-id');
            const staffName = button.getAttribute('data-staff-name');

            document.getElementById('fromStaffId').value = staffId;
            document.getElementById('fromStaffName').textContent = staffName;

            // Remove the current staff from the target dropdown
            const targetDropdown = document.getElementById('toStaffId');
            for (let i = 0; i < targetDropdown.options.length; i++) {
                if (targetDropdown.options[i].value === staffId) {
                    targetDropdown.options[i].disabled = true;
                } else {
                    targetDropdown.options[i].disabled = false;
                }
            }
        });

        // Save Reassignment
        document.getElementById('saveReassignment').addEventListener('click', function() {
            const fromStaffId = document.getElementById('fromStaffId').value;
            const toStaffId = document.getElementById('toStaffId').value;

            if (!toStaffId) {
                alert('Please select a target staff member.');
                return;
            }

            if (fromStaffId === toStaffId) {
                alert('Cannot reassign to the same staff member.');
                return;
            }

            document.getElementById('reassignUsersForm').submit();
        });
    }
});
</script>

<!-- Modern Action Menu Script -->
<script src="assets/js/modern-action-menu.js"></script>

<!-- Clean CSS for Staff Management Action Menus -->
<style>
/* Clean action menu container */
.staff-action-container {
    position: relative;
    display: inline-block;
}

/* Clean action button */
.clean-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
    padding: 0;
    position: relative;
}

.clean-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #495057;
    transform: scale(1.05);
}

.clean-action-btn:active {
    transform: scale(0.95);
}

/* Clean action menu */
.clean-action-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 220px;
    max-width: 280px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.16);
    z-index: 9999;
    overflow: hidden;
    display: none;
    border: 1px solid rgba(0, 0, 0, 0.08);
    margin-top: 8px;
}

.clean-action-menu.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

/* Clean menu items */
.clean-menu-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    color: #212529;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    position: relative;
}

.clean-menu-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.clean-menu-item:active {
    background-color: #f0f0f0;
}

.clean-menu-item i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
    font-size: 14px;
    transition: transform 0.2s ease;
}

.clean-menu-item:hover i {
    transform: translateX(2px);
}

/* Danger items */
.clean-menu-item.danger {
    color: #dc3545;
}

.clean-menu-item.danger:hover {
    background-color: rgba(220, 53, 69, 0.05);
    color: #dc3545;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- Improved JavaScript for Staff Management Action Menus -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all action buttons
    const actionButtons = document.querySelectorAll('.clean-action-btn');

    // Add click handler to each button
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Get the menu container and menu
            const container = this.closest('.staff-action-container');
            const menu = container.querySelector('.clean-action-menu');

            // Toggle menu visibility
            if (menu.classList.contains('show')) {
                menu.classList.remove('show');
            } else {
                // Close any other open menus
                document.querySelectorAll('.clean-action-menu.show').forEach(openMenu => {
                    openMenu.classList.remove('show');
                });

                // Show this menu
                menu.classList.add('show');

                // Position the menu
                positionMenu(this, menu);
            }
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.staff-action-container')) {
            document.querySelectorAll('.clean-action-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Function to position the menu
    function positionMenu(button, menu) {
        // Get button position
        const buttonRect = button.getBoundingClientRect();
        const containerRect = button.closest('.staff-action-container').getBoundingClientRect();

        // Get table row
        const row = button.closest('tr');

        // Check if menu would go off the bottom of the viewport
        const viewportHeight = window.innerHeight;
        const menuHeight = menu.offsetHeight || 200;

        if (buttonRect.bottom + menuHeight + 10 > viewportHeight) {
            // Position above the button
            menu.style.top = 'auto';
            menu.style.bottom = '100%';
            menu.style.marginTop = '0';
            menu.style.marginBottom = '8px';
        } else {
            // Position below the button
            menu.style.top = '100%';
            menu.style.bottom = 'auto';
            menu.style.marginTop = '8px';
            menu.style.marginBottom = '0';
        }

        // Check if menu would go off the right edge of the viewport
        const viewportWidth = window.innerWidth;
        const menuWidth = menu.offsetWidth || 220;

        if (containerRect.right + menuWidth > viewportWidth) {
            // Align with the right edge of the container
            menu.style.right = '0';
            menu.style.left = 'auto';
        } else {
            // Align with the left edge of the container
            menu.style.left = '0';
            menu.style.right = 'auto';
        }
    }

    // Add click handlers to menu items
    document.querySelectorAll('.clean-menu-item').forEach(item => {
        item.addEventListener('click', function(e) {
            // For modal triggers
            if (this.hasAttribute('data-bs-toggle') && this.hasAttribute('data-bs-target')) {
                e.preventDefault();

                const targetId = this.getAttribute('data-bs-target');
                const modalElement = document.querySelector(targetId);

                if (modalElement) {
                    const modal = new bootstrap.Modal(modalElement);

                    // Set data attributes for the modal
                    if (this.hasAttribute('data-staff-id')) {
                        const event = new CustomEvent('show.bs.modal', {
                            relatedTarget: this
                        });
                        modalElement.dispatchEvent(event);
                    }

                    modal.show();
                }

                // Close the menu
                const menu = this.closest('.clean-action-menu');
                if (menu) {
                    menu.classList.remove('show');
                }
            }
        });
    });
});
</script>
