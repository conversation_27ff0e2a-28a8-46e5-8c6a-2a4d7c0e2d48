<?php
// Minimal JWT implementation for PHP (HS256 only)
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function generate_jwt($payload, $secret) {
    $header = ['alg' => 'HS256', 'typ' => 'JWT'];
    $segments = [];
    $segments[] = base64url_encode(json_encode($header));
    $segments[] = base64url_encode(json_encode($payload));
    $signing_input = implode('.', $segments);
    $signature = hash_hmac('sha256', $signing_input, $secret, true);
    $segments[] = base64url_encode($signature);
    return implode('.', $segments);
}

function validate_jwt($jwt, $secret) {
    $parts = explode('.', $jwt);
    if (count($parts) !== 3) {
        error_log("JWT validation failed: Invalid number of parts (" . count($parts) . ")");
        return false;
    }

    list($header64, $payload64, $sig64) = $parts;
    $signing_input = $header64 . '.' . $payload64;
    $expected_sig = base64url_encode(hash_hmac('sha256', $signing_input, $secret, true));

    if (!hash_equals($expected_sig, $sig64)) {
        error_log("JWT validation failed: Invalid signature");
        return false;
    }

    $payload = json_decode(base64_decode(strtr($payload64, '-_', '+/')), true);
    if (!$payload) {
        error_log("JWT validation failed: Invalid payload");
        return false;
    }

    // Enhanced expiration check with logging
    if (isset($payload['exp'])) {
        $current_time = time();
        $exp_time = $payload['exp'];

        error_log("JWT expiration check: current=" . $current_time . ", exp=" . $exp_time . ", diff=" . ($exp_time - $current_time));

        if ($exp_time < $current_time) {
            error_log("JWT validation failed: Token expired (exp: " . date('Y-m-d H:i:s', $exp_time) . ", now: " . date('Y-m-d H:i:s', $current_time) . ")");
            return false;
        }
    }

    error_log("JWT validation successful for user_id: " . ($payload['user_id'] ?? 'unknown'));
    return $payload;
}