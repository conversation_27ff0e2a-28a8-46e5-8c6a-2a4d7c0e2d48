/* PWA Install FAB Button Styles */
.pwa-install-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1050;
    animation: fadeInUp 0.3s ease-out;
}

.pwa-install-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(39, 174, 96, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 48px;
    min-width: 48px;
}

.pwa-install-button:hover {
    background: #219150;
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(39, 174, 96, 0.4);
}

.pwa-install-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 15px rgba(39, 174, 96, 0.3);
}

.pwa-install-button i {
    font-size: 16px;
}

.pwa-install-text {
    font-family: 'Inter', sans-serif;
}

/* Responsive behavior */
@media (max-width: 768px) {
    .pwa-install-fab {
        bottom: 15px;
        right: 15px;
    }
    
    .pwa-install-button {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .pwa-install-text {
        display: none;
    }
    
    .pwa-install-button {
        border-radius: 50%;
        width: 48px;
        height: 48px;
        padding: 0;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .pwa-install-fab {
        bottom: 10px;
        right: 10px;
    }
    
    .pwa-install-button {
        width: 44px;
        height: 44px;
        min-height: 44px;
        min-width: 44px;
    }
}

/* PWA Install Modal Styles */
.pwa-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1060;
    animation: fadeIn 0.3s ease-out;
}

.pwa-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    animation: slideInUp 0.3s ease-out;
}

.pwa-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.pwa-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.pwa-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.pwa-modal-close:hover {
    background: #f5f5f5;
    color: #666;
}

.pwa-modal-body {
    padding: 0 24px 20px;
}

.pwa-app-info {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.pwa-app-icon {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    flex-shrink: 0;
}

.pwa-app-details h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.pwa-app-details p {
    margin: 0 0 16px 0;
    color: #666;
    line-height: 1.5;
}

.pwa-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pwa-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #555;
    font-size: 14px;
}

.pwa-features li i {
    color: #27ae60;
    font-size: 12px;
}

.pwa-modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.pwa-modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pwa-modal-footer .btn-primary {
    background: #27ae60;
    color: white;
}

.pwa-modal-footer .btn-primary:hover {
    background: #219150;
}

.pwa-modal-footer .btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.pwa-modal-footer .btn-secondary:hover {
    background: #e9ecef;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Hide FAB when modal is open */
.pwa-modal-open .pwa-install-fab {
    display: none;
}

/* Accessibility improvements */
.pwa-install-button:focus {
    outline: 2px solid #27ae60;
    outline-offset: 2px;
}

.pwa-modal-close:focus {
    outline: 2px solid #27ae60;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .pwa-install-button {
        border: 2px solid white;
    }
    
    .pwa-modal-content {
        border: 2px solid #333;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .pwa-install-fab,
    .pwa-modal,
    .pwa-modal-content,
    .pwa-install-button {
        animation: none;
        transition: none;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .pwa-modal-content {
        background: #2d3748;
        color: white;
    }
    
    .pwa-modal-header {
        border-bottom-color: #4a5568;
    }
    
    .pwa-modal-footer {
        border-top-color: #4a5568;
    }
    
    .pwa-modal-header h3,
    .pwa-app-details h4 {
        color: white;
    }
    
    .pwa-app-details p,
    .pwa-features li {
        color: #cbd5e0;
    }
    
    .pwa-modal-close {
        color: #a0aec0;
    }
    
    .pwa-modal-close:hover {
        background: #4a5568;
        color: white;
    }
}
