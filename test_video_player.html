<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vimeo Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-test {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            margin: 15px 0;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 4px;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-info p {
            margin: 5px 0;
            color: #666;
        }
        .url-display {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Vimeo Video Player Test</h1>
        <p>Testing different Vimeo embed configurations for video ID: <strong>1087487482</strong></p>

        <!-- Test 1: Basic Embed -->
        <div class="video-test">
            <div class="test-info">
                <h3>Test 1: Basic Vimeo Embed</h3>
                <p>Standard Vimeo player embed without any special parameters</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status1">Loading...</div>
        </div>

        <!-- Test 2: Privacy Settings Optimized -->
        <div class="video-test">
            <div class="test-info">
                <h3>Test 2: Privacy Optimized Embed</h3>
                <p>Embed with privacy and tracking disabled</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status2">Loading...</div>
        </div>

        <!-- Test 3: With Domain Parameter -->
        <div class="video-test">
            <div class="test-info">
                <h3>Test 3: Domain Specified Embed</h3>
                <p>Embed with domain parameter for whitelist verification</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&domain=com.kft.fitness</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&domain=com.kft.fitness" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status3">Loading...</div>
        </div>

        <!-- Test 4: With Referrer -->
        <div class="video-test">
            <div class="test-info">
                <h3>Test 4: Referrer Specified Embed</h3>
                <p>Embed with referrer parameter</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&referrer=https://e7f0-2409-40c0-1028-c574-198e-55f1-fe67-96e.ngrok-free.app</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&referrer=https://e7f0-2409-40c0-1028-c574-198e-55f1-fe67-96e.ngrok-free.app" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status4">Loading...</div>
        </div>

        <!-- Instructions -->
        <div class="video-test">
            <div class="test-info">
                <h3>🔧 Troubleshooting Instructions</h3>
                <p><strong>If videos don't play, follow these steps:</strong></p>
                <ol>
                    <li><strong>Check Vimeo Privacy Settings:</strong>
                        <ul>
                            <li>Go to your video on Vimeo.com</li>
                            <li>Click "Settings" → "Privacy"</li>
                            <li>Set "Who can watch" to "Anyone" or "Only people with the link"</li>
                            <li>Set "Where can this be embedded" to "Anywhere" or "Specific domains"</li>
                        </ul>
                    </li>
                    <li><strong>Add Domain to Whitelist:</strong>
                        <ul>
                            <li>If using "Specific domains", add: <code>e7f0-2409-40c0-1028-c574-198e-55f1-fe67-96e.ngrok-free.app</code></li>
                            <li>Also add: <code>com.kft.fitness</code> for mobile app</li>
                        </ul>
                    </li>
                    <li><strong>Check Video Status:</strong>
                        <ul>
                            <li>Ensure video is fully processed and available</li>
                            <li>Check if video is public or unlisted (not private)</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Monitor iframe load events
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('iframe');
            
            iframes.forEach((iframe, index) => {
                const statusElement = document.getElementById(`status${index + 1}`);
                
                iframe.addEventListener('load', function() {
                    statusElement.textContent = '✅ Iframe loaded successfully';
                    statusElement.className = 'status success';
                });
                
                iframe.addEventListener('error', function() {
                    statusElement.textContent = '❌ Failed to load iframe';
                    statusElement.className = 'status error';
                });
                
                // Set timeout to check if video loads
                setTimeout(() => {
                    if (statusElement.textContent === 'Loading...') {
                        statusElement.textContent = '⚠️ Taking longer than expected to load';
                        statusElement.className = 'status warning';
                    }
                }, 5000);
            });
        });

        // Listen for messages from Vimeo player
        window.addEventListener('message', function(event) {
            if (event.origin !== 'https://player.vimeo.com') return;
            
            console.log('Vimeo player message:', event.data);
            
            // Handle different Vimeo player events
            if (event.data && event.data.event) {
                switch (event.data.event) {
                    case 'ready':
                        console.log('✅ Vimeo player ready');
                        break;
                    case 'error':
                        console.error('❌ Vimeo player error:', event.data);
                        break;
                    case 'loaded':
                        console.log('✅ Vimeo video loaded');
                        break;
                }
            }
        });
    </script>
</body>
</html>
