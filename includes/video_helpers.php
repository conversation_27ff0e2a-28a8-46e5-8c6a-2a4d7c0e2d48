<?php
/**
 * Helper functions for video management
 */

/**
 * Extract Vimeo video ID from various Vimeo URL formats
 *
 * @param string $url The Vimeo URL
 * @return string|null The Vimeo video ID or null if not a valid Vimeo URL
 */
function extractVimeoId($url) {
    if (empty($url)) {
        return null;
    }

    // Regular expression to match Vimeo URLs and extract the video ID
    $patterns = [
        // Standard Vimeo URL: https://vimeo.com/123456789
        '/vimeo\.com\/([0-9]+)/',

        // Player URL: https://player.vimeo.com/video/123456789
        '/player\.vimeo\.com\/video\/([0-9]+)/',

        // Private link: https://vimeo.com/123456789/abcdef1234
        '/vimeo\.com\/([0-9]+)\/[a-zA-Z0-9]+/',

        // Embedded URL: https://player.vimeo.com/video/123456789?h=abcdef1234
        '/player\.vimeo\.com\/video\/([0-9]+)\?h=[a-zA-Z0-9]+/'
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }

    return null;
}

/**
 * Extract private hash from Vimeo private URLs
 *
 * @param string $url The Vimeo URL
 * @return string|null The private hash or null if not a private URL
 */
function extractVimeoPrivateHash($url) {
    if (empty($url)) {
        return null;
    }

    // Pattern for private Vimeo URLs: https://vimeo.com/123456789/abcdef1234
    if (preg_match('/vimeo\.com\/[0-9]+\/([a-zA-Z0-9]+)/', $url, $matches)) {
        return $matches[1];
    }

    // Pattern for embed URLs with hash: https://player.vimeo.com/video/123456789?h=abcdef1234
    if (preg_match('/player\.vimeo\.com\/video\/[0-9]+\?h=([a-zA-Z0-9]+)/', $url, $matches)) {
        return $matches[1];
    }

    return null;
}

/**
 * Check if a URL is a valid Vimeo URL
 *
 * @param string $url The URL to check
 * @return bool Whether the URL is a valid Vimeo URL
 */
function isVimeoUrl($url) {
    return extractVimeoId($url) !== null;
}

/**
 * Convert a Vimeo URL to an embeddable URL with additional parameters
 *
 * @param string $url The Vimeo URL
 * @param array $params Optional parameters for the embed URL
 * @return string|null The embeddable URL or null if not a valid Vimeo URL
 */
function getVimeoEmbedUrl($url, $params = []) {
    $videoId = extractVimeoId($url);

    if ($videoId) {
        // Check if this is a private video with a hash
        $privateHash = extractVimeoPrivateHash($url);

        // Default parameters for better playback experience
        $defaultParams = [
            'autoplay' => 0,
            'title' => 0,
            'byline' => 0,
            'portrait' => 0,
            'responsive' => 1,
            'dnt' => 1, // Do Not Track
        ];

        // If it's a private video, add the hash parameter
        if ($privateHash) {
            $defaultParams['h'] = $privateHash;
        }

        // Merge default parameters with any provided parameters
        $finalParams = array_merge($defaultParams, $params);

        // Build the query string
        $queryString = http_build_query($finalParams);

        // Return the embed URL with parameters
        return "https://player.vimeo.com/video/{$videoId}?{$queryString}";
    }

    return null;
}

/**
 * Get Vimeo video thumbnail URL
 *
 * Note: This is a simplified version. In a production environment,
 * you would use Vimeo's API to get the actual thumbnail URL.
 *
 * @param string $url The Vimeo URL
 * @return string|null The thumbnail URL or null if not a valid Vimeo URL
 */
function getVimeoThumbnailUrl($url) {
    $videoId = extractVimeoId($url);

    if ($videoId) {
        // This is a placeholder. In a real implementation, you would use Vimeo's API
        // to get the actual thumbnail URL for the video.
        return "https://vumbnail.com/{$videoId}.jpg";
    }

    return null;
}

/**
 * Determine if a URL is a video URL (currently only checks for Vimeo)
 *
 * @param string $url The URL to check
 * @return bool Whether the URL is a video URL
 */
function isVideoUrl($url) {
    return isVimeoUrl($url);
}

/**
 * Get video provider from URL
 *
 * @param string $url The video URL
 * @return string The video provider ('vimeo', 'youtube', 'unknown')
 */
function getVideoProvider($url) {
    if (isVimeoUrl($url)) {
        return 'vimeo';
    }

    // Add support for other providers here

    return 'unknown';
}
