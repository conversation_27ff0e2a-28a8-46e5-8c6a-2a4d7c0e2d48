<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $clientId = $input['client_id'] ?? '';
    $clientSecret = $input['client_secret'] ?? '';
    $grantType = $input['grant_type'] ?? 'client_credentials';
    $scope = $input['scope'] ?? 'public private';
    
    // Validate required parameters
    if (empty($clientId) || empty($clientSecret)) {
        throw new Exception('Missing required Vimeo Pro credentials');
    }
    
    // Verify these are your actual Vimeo Pro credentials
    $expectedClientId = 'eb6edcd564c33510af4f3a09a8c40aa7d43b2b87';
    $expectedClientSecret = 'KoOnKaeyxukkGZIdKTf4FMi5hoxK7a7S/rdlMwszUL0C2y2ClgaYpV4gdKN/42gHTpINyapIU9/wsPRexb+kbqr7qv8s5t1S+bMAO2RP3EGpGYR41gPL7cM4NKGZfyHR';
    
    if ($clientId !== $expectedClientId || $clientSecret !== $expectedClientSecret) {
        throw new Exception('Invalid Vimeo Pro credentials');
    }
    
    // Check if we have a cached valid token
    $cachedToken = getCachedVimeoToken($pdo, $clientId);
    if ($cachedToken) {
        echo json_encode([
            'success' => true,
            'access_token' => $cachedToken['access_token'],
            'token_type' => 'bearer',
            'expires_in' => $cachedToken['expires_in'],
            'scope' => $cachedToken['scope'],
            'cached' => true,
            'message' => 'Using cached Vimeo Pro access token'
        ]);
        exit;
    }
    
    // Authenticate with Vimeo Pro API
    $vimeoToken = authenticateWithVimeoPro($clientId, $clientSecret, $grantType, $scope);
    
    if ($vimeoToken) {
        // Cache the token
        cacheVimeoToken($pdo, $clientId, $vimeoToken);
        
        echo json_encode([
            'success' => true,
            'access_token' => $vimeoToken['access_token'],
            'token_type' => $vimeoToken['token_type'],
            'expires_in' => $vimeoToken['expires_in'],
            'scope' => $vimeoToken['scope'],
            'cached' => false,
            'message' => 'Vimeo Pro authentication successful'
        ]);
    } else {
        throw new Exception('Failed to authenticate with Vimeo Pro API');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Authenticate with Vimeo Pro API using client credentials
 */
function authenticateWithVimeoPro($clientId, $clientSecret, $grantType, $scope) {
    $url = 'https://api.vimeo.com/oauth/authorize/client';
    
    $postData = [
        'grant_type' => $grantType,
        'scope' => $scope
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => http_build_query($postData),
        CURLOPT_HTTPHEADER => [
            'Authorization: Basic ' . base64_encode($clientId . ':' . $clientSecret),
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/vnd.vimeo.*+json;version=3.4',
            'User-Agent: KFT-Fitness-App/1.0 (Vimeo Pro Integration)'
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        error_log("Vimeo Pro authentication cURL error: " . $error);
        return false;
    }
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        
        if (isset($data['access_token'])) {
            return [
                'access_token' => $data['access_token'],
                'token_type' => $data['token_type'] ?? 'bearer',
                'expires_in' => $data['expires_in'] ?? 3600,
                'scope' => $data['scope'] ?? $scope
            ];
        }
    }
    
    error_log("Vimeo Pro authentication failed. HTTP Code: $httpCode, Response: $response");
    return false;
}

/**
 * Get cached Vimeo token if still valid
 */
function getCachedVimeoToken($pdo, $clientId) {
    try {
        $stmt = $pdo->prepare("
            SELECT access_token, expires_at, scope, TIMESTAMPDIFF(SECOND, NOW(), expires_at) as expires_in
            FROM vimeo_pro_tokens 
            WHERE client_id = ? AND expires_at > NOW()
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$clientId]);
        $token = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($token && $token['expires_in'] > 300) { // At least 5 minutes remaining
            return $token;
        }
        
        return null;
    } catch (Exception $e) {
        error_log("Failed to get cached Vimeo token: " . $e->getMessage());
        return null;
    }
}

/**
 * Cache Vimeo token for future use
 */
function cacheVimeoToken($pdo, $clientId, $tokenData) {
    try {
        // Calculate expiry time
        $expiresAt = date('Y-m-d H:i:s', time() + $tokenData['expires_in']);
        
        // Insert or update token
        $stmt = $pdo->prepare("
            INSERT INTO vimeo_pro_tokens (client_id, access_token, token_type, scope, expires_at, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            access_token = VALUES(access_token),
            token_type = VALUES(token_type),
            scope = VALUES(scope),
            expires_at = VALUES(expires_at),
            updated_at = NOW()
        ");
        
        $stmt->execute([
            $clientId,
            $tokenData['access_token'],
            $tokenData['token_type'],
            $tokenData['scope'],
            $expiresAt
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to cache Vimeo token: " . $e->getMessage());
        return false;
    }
}

/**
 * Create vimeo_pro_tokens table if it doesn't exist
 */
function createVimeoTokensTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS vimeo_pro_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id VARCHAR(255) NOT NULL,
            access_token TEXT NOT NULL,
            token_type VARCHAR(50) DEFAULT 'bearer',
            scope TEXT,
            expires_at DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_client (client_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        return true;
    } catch (Exception $e) {
        error_log("Failed to create vimeo_pro_tokens table: " . $e->getMessage());
        return false;
    }
}

// Ensure the table exists
createVimeoTokensTable($pdo);
?>
