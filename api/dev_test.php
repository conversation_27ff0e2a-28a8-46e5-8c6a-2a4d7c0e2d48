<?php
require_once 'config.php';

// This endpoint is for testing development mode features
// It will show different responses based on whether development mode is enabled

// Check if development mode is enabled
if (defined('DEV_MODE') && DEV_MODE === true) {
    // In development mode, we'll return more detailed information
    $response = [
        'message' => 'Development mode is enabled',
        'environment' => 'development',
        'php_version' => phpversion(),
        'memory_usage' => [
            'current' => memory_get_usage(true) / 1024 / 1024 . ' MB',
            'peak' => memory_get_peak_usage(true) / 1024 / 1024 . ' MB'
        ],
        'server_info' => [
            'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'protocol' => $_SERVER['SERVER_PROTOCOL'] ?? 'Unknown',
            'request_time' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']),
            'request_method' => $_SERVER['REQUEST_METHOD']
        ],
        'database' => [
            'host' => DB_HOST,
            'name' => DB_NAME
        ]
    ];
    
    // Intentionally trigger a notice to demonstrate error handling
    $undefinedVariable = $nonExistentVariable;
} else {
    // In production mode, we'll return minimal information
    $response = [
        'message' => 'Development mode is disabled',
        'environment' => 'production'
    ];
}

// Return the response
returnResponse($response);
