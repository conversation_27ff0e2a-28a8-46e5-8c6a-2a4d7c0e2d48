<?php
require_once 'includes/header.php';

// Fetch pending users
$db = new Database();
$conn = $db->getConnection();

// Check if status column exists in pending_users table
$checkStatusColumnQuery = "SHOW COLUMNS FROM pending_users LIKE 'status'";
$checkStatusColumnResult = $conn->query($checkStatusColumnQuery);

if ($checkStatusColumnResult->num_rows === 0) {
    // Add status column if it doesn't exist
    $alterTableQuery = "ALTER TABLE pending_users
                        ADD COLUMN status ENUM('pending', 'approved', 'not_interested') NOT NULL DEFAULT 'pending',
                        ADD COLUMN status_updated_at TIMESTAMP NULL DEFAULT NULL,
                        ADD COLUMN status_updated_by INT NULL DEFAULT NULL";
    $conn->query($alterTableQuery);
}

// Check if we should show all registrations or only pending ones
$showAll = isset($_GET['show_all']) && $_GET['show_all'] == '1';

// Get count of pending registrations
$pendingCountQuery = "SELECT COUNT(*) as count FROM pending_users WHERE status = 'pending'";
$pendingCountResult = $conn->query($pendingCountQuery);
$pendingCount = $pendingCountResult->fetch_assoc()['count'];

// Build the query based on the filter
if ($showAll) {
    $query = "SELECT * FROM pending_users ORDER BY created_at DESC";
} else {
    $query = "SELECT * FROM pending_users WHERE status = 'pending' ORDER BY created_at DESC";
}

$result = $conn->query($query);
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            Pending User Registrations
            <?php if ($pendingCount > 0): ?>
                <span class="badge bg-primary ms-2"><?= $pendingCount ?> pending</span>
            <?php endif; ?>
        </h2>
        <div>
            <?php if ($showAll): ?>
                <a href="pending_users.php" class="btn btn-outline-primary">
                    <i class="fas fa-filter me-1"></i> Show Pending Only
                </a>
            <?php else: ?>
                <a href="pending_users.php?show_all=1" class="btn btn-outline-primary">
                    <i class="fas fa-list me-1"></i> Show All Registrations
                </a>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($result->num_rows === 0): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <?php if ($showAll): ?>
                No user registrations found.
            <?php else: ?>
                No pending user registrations found. <a href="pending_users.php?show_all=1">View all registrations</a>
            <?php endif; ?>
        </div>
    <?php else: ?>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Name</th>
                <th>Phone Number</th>
                <th>Device ID</th>
                <th>Requested At</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php while ($row = $result->fetch_assoc()):
            $status = $row['status'] ?? 'pending';
            $statusBadge = '';
            $actionsDisabled = false;

            switch ($status) {
                case 'approved':
                    $statusBadge = '<span class="badge bg-success">Approved</span>';
                    $actionsDisabled = true;
                    break;
                case 'not_interested':
                    $statusBadge = '<span class="badge bg-secondary">Not Interested</span>';
                    $actionsDisabled = true;
                    break;
                default:
                    $statusBadge = '<span class="badge bg-warning">Pending</span>';
                    break;
            }
        ?>
            <tr<?= $actionsDisabled ? ' class="table-light"' : '' ?>>
                <td><?= htmlspecialchars($row['name'] ?: 'Not provided') ?></td>
                <td><?= htmlspecialchars($row['phone_number']) ?></td>
                <td>
                    <?php if (!empty($row['device_id'])): ?>
                        <code class="small"><?= htmlspecialchars(substr($row['device_id'], 0, 20)) ?><?= strlen($row['device_id']) > 20 ? '...' : '' ?></code>
                    <?php else: ?>
                        <span class="text-muted">Not provided</span>
                    <?php endif; ?>
                </td>
                <td><?= $row['created_at'] ?: 'Unknown' ?></td>
                <td>
                    <?= $statusBadge ?>
                    <?php if ($status !== 'pending' && !empty($row['status_updated_at'])): ?>
                        <div class="small text-muted mt-1">
                            <i class="fas fa-clock me-1"></i> <?= date('M d, Y g:i A', strtotime($row['status_updated_at'])) ?>
                        </div>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if ($actionsDisabled): ?>
                        <div class="d-flex gap-2">
                            <?php if ($status === 'approved'): ?>
                                <span class="text-success"><i class="fas fa-check-circle me-1"></i> Approved</span>
                            <?php else: ?>
                                <span class="text-secondary"><i class="fas fa-times-circle me-1"></i> Marked Not Interested</span>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="d-flex gap-2">
                            <a href="pending_users.php?approve=<?= $row['id'] ?>" class="btn btn-success btn-sm">Approve</a>
                            <a href="pending_users.php?not_interested=<?= $row['id'] ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times-circle me-1"></i> Not Interested
                            </a>
                        </div>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endwhile; ?>
        </tbody>
    </table>
    <?php endif; ?>
</div>

<?php
// Handle approve/reject actions
if (isset($_GET['approve'])) {
    $id = intval($_GET['approve']);
    // Get user info
    $stmt = $conn->prepare("SELECT name, phone_number, status FROM pending_users WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($name, $phone_number, $status);
    if ($stmt->fetch()) {
        $stmt->close();

        // Check if already processed
        if ($status === 'approved') {
            Utilities::setFlashMessage('info', 'This registration has already been approved.');
            header('Location: pending_users.php');
            exit;
        }

        if ($status === 'not_interested') {
            Utilities::setFlashMessage('info', 'This registration was previously marked as not interested.');
            header('Location: pending_users.php');
            exit;
        }

        // Get current admin user ID if available
        $adminId = isset($_SESSION['admin_user_id']) ? intval($_SESSION['admin_user_id']) : null;

        // Update status to approved
        $updateQuery = "UPDATE pending_users
                       SET status = 'approved',
                           status_updated_at = NOW(),
                           status_updated_by = ?
                       WHERE id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("ii", $adminId, $id);
        $updateStmt->execute();
        $updateStmt->close();

        // Store the pending user ID in session to track it after successful user creation
        $_SESSION['pending_user_id'] = $id;

        // Format phone number for the form (remove +91 prefix if present)
        $formattedPhone = $phone_number;
        if (strpos($formattedPhone, '+91') === 0) {
            $formattedPhone = substr($formattedPhone, 3);
        }

        // Redirect to user_add.php with prefilled data
        $redirectUrl = "user_add.php?prefill=1&name=" . urlencode($name) . "&phone=" . urlencode($formattedPhone);
        header("Location: $redirectUrl");
        exit;
    } else {
        $stmt->close();
    }

    // If we get here, the pending user wasn't found
    Utilities::setFlashMessage('danger', 'Pending user not found.');
    header('Location: pending_users.php');
    exit;
}

if (isset($_GET['not_interested'])) {
    $id = intval($_GET['not_interested']);

    // Get current admin user ID if available
    $adminId = isset($_SESSION['admin_user_id']) ? intval($_SESSION['admin_user_id']) : null;

    // Update status instead of deleting
    $updateQuery = "UPDATE pending_users
                   SET status = 'not_interested',
                       status_updated_at = NOW(),
                       status_updated_by = ?
                   WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ii", $adminId, $id);
    $stmt->execute();

    Utilities::setFlashMessage('info', 'User marked as not interested.');
    header('Location: pending_users.php');
    exit;
}

require_once 'includes/footer.php';
?>