<?php
require_once 'includes/header.php';

// Check if user has admin privileges
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('warning', 'You do not have permission to access the AI settings page.');
    Utilities::redirect('index.php');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Initialize settings manager
$settingsManager = Settings::getInstance();

// Function to get AI settings
function getAiSettings($conn) {
    $settings = [
        'openai_api_key' => '',
        'openai_model' => 'gpt-4-vision-preview',
        'calorie_analysis_enabled' => '1',
        'calorie_analysis_prompt' => 'Analyze this food image and provide the following information in JSON format: {"food_name": "name of the food", "calories": estimated calories per serving, "protein": estimated protein in grams, "carbs": estimated carbs in grams, "fat": estimated fat in grams, "serving_size": "standard serving size"}. Be as accurate as possible with your estimates.'
    ];

    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM ai_settings");
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    return $settings;
}

// Get current AI settings
$aiSettings = getAiSettings($conn);
$openaiApiKey = $aiSettings['openai_api_key'];
$openaiModel = $aiSettings['openai_model'];
$calorieAnalysisEnabled = $aiSettings['calorie_analysis_enabled'] == '1';
$calorieAnalysisPrompt = $aiSettings['calorie_analysis_prompt'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_ai_settings') {
    // Get form data
    $newOpenaiApiKey = $_POST['openai_api_key'] ?? '';
    $newOpenaiModel = $_POST['openai_model'] ?? 'gpt-4-vision-preview';
    $newCalorieAnalysisEnabled = isset($_POST['calorie_analysis_enabled']) ? '1' : '0';
    $newCalorieAnalysisPrompt = $_POST['calorie_analysis_prompt'] ?? $calorieAnalysisPrompt;
    
    // Update settings in database
    $stmt = $conn->prepare("INSERT INTO ai_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
    
    $settings = [
        'openai_api_key' => $newOpenaiApiKey,
        'openai_model' => $newOpenaiModel,
        'calorie_analysis_enabled' => $newCalorieAnalysisEnabled,
        'calorie_analysis_prompt' => $newCalorieAnalysisPrompt
    ];
    
    $success = true;
    foreach ($settings as $key => $value) {
        $stmt->bind_param("ss", $key, $value);
        if (!$stmt->execute()) {
            $success = false;
            break;
        }
    }
    
    if ($success) {
        Utilities::setFlashMessage('success', 'AI settings saved successfully.');
    } else {
        Utilities::setFlashMessage('danger', 'Error saving AI settings: ' . $conn->error);
    }
    
    // Redirect to refresh the page
    Utilities::redirect('ai_settings.php');
}
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-robot me-2"></i>AI Settings</h1>
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>
    </div>
    
    <?php Utilities::displayFlashMessages(); ?>
    
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">OpenAI Integration for Food Calorie Analysis</h5>
        </div>
        <div class="card-body">
            <form method="post" action="ai_settings.php">
                <input type="hidden" name="action" value="save_ai_settings">
                
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="calorie_analysis_enabled" name="calorie_analysis_enabled" value="1" <?php if ($calorieAnalysisEnabled) echo 'checked'; ?>>
                    <label class="form-check-label" for="calorie_analysis_enabled">
                        Enable AI Food Calorie Analysis
                    </label>
                </div>
                
                <div class="mb-3">
                    <label for="openai_api_key" class="form-label">OpenAI API Key</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="openai_api_key" name="openai_api_key" value="<?php echo htmlspecialchars($openaiApiKey); ?>" placeholder="Enter your OpenAI API key">
                        <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('openai_api_key')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="form-text">Your OpenAI API key is required for AI-powered food calorie analysis.</div>
                </div>
                
                <div class="mb-3">
                    <label for="openai_model" class="form-label">OpenAI Model</label>
                    <select class="form-select" id="openai_model" name="openai_model">
                        <option value="gpt-4-vision-preview" <?php if ($openaiModel === 'gpt-4-vision-preview') echo 'selected'; ?>>GPT-4 Vision (Recommended)</option>
                        <option value="gpt-4o" <?php if ($openaiModel === 'gpt-4o') echo 'selected'; ?>>GPT-4o</option>
                    </select>
                    <div class="form-text">Select the OpenAI model to use for food image analysis.</div>
                </div>
                
                <div class="mb-3">
                    <label for="calorie_analysis_prompt" class="form-label">Analysis Prompt</label>
                    <textarea class="form-control" id="calorie_analysis_prompt" name="calorie_analysis_prompt" rows="4"><?php echo htmlspecialchars($calorieAnalysisPrompt); ?></textarea>
                    <div class="form-text">The prompt sent to OpenAI for food image analysis.</div>
                </div>
                
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">Test Food Image Analysis</h5>
        </div>
        <div class="card-body">
            <p>Upload a food image to test the AI calorie analysis:</p>
            
            <form id="testImageForm" enctype="multipart/form-data">
                <div class="mb-3">
                    <input type="file" class="form-control" id="test_image" name="test_image" accept="image/*">
                </div>
                
                <div class="d-flex justify-content-end">
                    <button type="button" id="testAnalysisBtn" class="btn btn-success">
                        <i class="fas fa-vial me-2"></i>Test Analysis
                    </button>
                </div>
            </form>
            
            <div id="analysisResult" class="mt-4" style="display: none;">
                <h6>Analysis Result:</h6>
                <div class="card">
                    <div class="card-body">
                        <pre id="analysisResultJson" class="mb-0"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    if (input.type === 'password') {
        input.type = 'text';
    } else {
        input.type = 'password';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const testAnalysisBtn = document.getElementById('testAnalysisBtn');
    const testImageForm = document.getElementById('testImageForm');
    const analysisResult = document.getElementById('analysisResult');
    const analysisResultJson = document.getElementById('analysisResultJson');
    
    testAnalysisBtn.addEventListener('click', function() {
        const fileInput = document.getElementById('test_image');
        if (!fileInput.files || fileInput.files.length === 0) {
            alert('Please select an image file.');
            return;
        }
        
        const formData = new FormData();
        formData.append('image', fileInput.files[0]);
        formData.append('action', 'test_analysis');
        
        testAnalysisBtn.disabled = true;
        testAnalysisBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
        
        fetch('api/analyze_food_image.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            testAnalysisBtn.disabled = false;
            testAnalysisBtn.innerHTML = '<i class="fas fa-vial me-2"></i>Test Analysis';
            
            if (data.success) {
                analysisResult.style.display = 'block';
                analysisResultJson.textContent = JSON.stringify(data.result, null, 2);
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            testAnalysisBtn.disabled = false;
            testAnalysisBtn.innerHTML = '<i class="fas fa-vial me-2"></i>Test Analysis';
            alert('Error: ' + error.message);
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
