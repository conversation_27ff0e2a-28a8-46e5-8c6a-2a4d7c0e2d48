<?php
// Test the secure embed URL generation
$vimeoId = '1087487482';
$domain = 'com.kft.fitness';
$userId = 29;
$originalVideoUrl = 'https://vimeo.com/1087487482/ae75b6e329';

// Include the functions from the API file
function extractVimeoPrivateHash($url) {
    if (empty($url)) {
        return null;
    }

    // Pattern for private Vimeo URLs: https://vimeo.com/123456789/abcdef1234
    if (preg_match('/vimeo\.com\/[0-9]+\/([a-zA-Z0-9]+)/', $url, $matches)) {
        return $matches[1];
    }

    // Pattern for embed URLs with hash: https://player.vimeo.com/video/123456789?h=abcdef1234
    if (preg_match('/player\.vimeo\.com\/video\/[0-9]+\?h=([a-zA-Z0-9]+)/', $url, $matches)) {
        return $matches[1];
    }

    return null;
}

function generatePrivateVideoEmbedUrl($vimeoId, $privateHash, $domain, $userId) {
    // For private videos with hash, use the hash directly
    $timestamp = time();
    $nonce = bin2hex(random_bytes(16));

    // Generate secure hash for additional security
    $secretKey = 'kft_fitness_video_secret_key_2025';
    $hashData = $vimeoId . $domain . $userId . $timestamp . $nonce;
    $securityHash = hash_hmac('sha256', $hashData, $secretKey);

    // Build embed URL with private hash
    $baseUrl = "https://player.vimeo.com/video/{$vimeoId}";
    $params = [
        'h' => $privateHash, // Use the actual private hash
        'autoplay' => '0',
        'title' => '0',
        'byline' => '0',
        'portrait' => '0',
        'responsive' => '1',
        'dnt' => '1',
        'controls' => '1',
        'sharing' => '0',
        'download' => '0',
        'app_id' => 'kft_fitness',
        'domain' => urlencode($domain),
        'timestamp' => $timestamp,
        'nonce' => $nonce,
        'security_hash' => $securityHash
    ];

    $queryString = http_build_query($params);
    return $baseUrl . '?' . $queryString;
}

echo "Testing Secure Embed URL Generation\n\n";

echo "Original Video URL: $originalVideoUrl\n";
echo "Vimeo ID: $vimeoId\n";

// Test extracting private hash
$privateHash = extractVimeoPrivateHash($originalVideoUrl);
echo "Extracted Private Hash: " . ($privateHash ?: 'null') . "\n";

if ($privateHash) {
    // Generate secure embed URL
    $secureEmbedUrl = generatePrivateVideoEmbedUrl($vimeoId, $privateHash, $domain, $userId);
    echo "Generated Secure Embed URL: $secureEmbedUrl\n";

    echo "\n--- Expected vs Actual ---\n";
    echo "Expected Hash: ae75b6e329\n";
    echo "Actual Hash: $privateHash\n";
    echo "Match: " . ($privateHash === 'ae75b6e329' ? 'YES' : 'NO') . "\n";
} else {
    echo "ERROR: Could not extract private hash from URL\n";
}
?>
