<?php
header('Content-Type: application/json');
require_once '../includes/config.php';
require_once '../includes/database.php';

// Get parameters
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$courseId = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;

if ($userId <= 0 || $courseId <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid parameters']);
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get real-time analytics
$analyticsQuery = "SELECT
                  COUNT(DISTINCT al.id) as total_views,
                  SUM(al.watch_duration_seconds) as total_watch_duration,
                  COUNT(DISTINCT CASE WHEN al.is_completed = 1 THEN al.video_id END) as completed_videos,
                  MAX(al.timestamp) as last_activity,
                  (SELECT COUNT(*) FROM course_videos WHERE course_id = ?) as total_videos
                  FROM video_access_logs al
                  JOIN course_videos cv ON al.video_id = cv.id
                  WHERE al.user_id = ? AND cv.course_id = ?";

$analyticsStmt = $conn->prepare($analyticsQuery);
$analyticsStmt->bind_param("iii", $courseId, $userId, $courseId);
$analyticsStmt->execute();
$analyticsResult = $analyticsStmt->get_result();
$analytics = $analyticsResult->fetch_assoc();

// Format the response
$response = [
    'total_views' => intval($analytics['total_views'] ?? 0),
    'total_watch_duration' => intval($analytics['total_watch_duration'] ?? 0),
    'completed_videos' => intval($analytics['completed_videos'] ?? 0),
    'total_videos' => intval($analytics['total_videos'] ?? 0),
    'last_activity' => $analytics['last_activity'] ? date('Y-m-d H:i:s', strtotime($analytics['last_activity'])) : null
];

echo json_encode($response); 