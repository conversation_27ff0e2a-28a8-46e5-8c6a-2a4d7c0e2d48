<?php
/**
 * Avatar Upload Handler
 * This file handles the avatar upload functionality for the profile page
 */

// Include necessary files
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    // Return JSON response for AJAX requests
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'redirect' => ''
];

// Check if file was uploaded
if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
    $errorMessage = 'No file uploaded or upload error occurred';
    
    // If it's an AJAX request, return JSON
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json');
        $response['message'] = $errorMessage;
        echo json_encode($response);
        exit;
    }
    
    // Otherwise, redirect back to profile page with error
    $_SESSION['avatar_error'] = $errorMessage;
    header('Location: profile.php');
    exit;
}

// Get file details
$file = $_FILES['avatar'];
$fileName = $file['name'];
$fileTmpName = $file['tmp_name'];
$fileSize = $file['size'];
$fileError = $file['error'];
$fileType = $file['type'];

// Get file extension
$fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

// Allowed extensions
$allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];

// Check if extension is allowed
if (!in_array($fileExt, $allowedExtensions)) {
    $errorMessage = 'Only JPG, JPEG, PNG, and GIF files are allowed';
    
    // If it's an AJAX request, return JSON
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json');
        $response['message'] = $errorMessage;
        echo json_encode($response);
        exit;
    }
    
    // Otherwise, redirect back to profile page with error
    $_SESSION['avatar_error'] = $errorMessage;
    header('Location: profile.php');
    exit;
}

// Check file size (max 2MB)
if ($fileSize > 2 * 1024 * 1024) {
    $errorMessage = 'File size should be less than 2MB';
    
    // If it's an AJAX request, return JSON
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json');
        $response['message'] = $errorMessage;
        echo json_encode($response);
        exit;
    }
    
    // Otherwise, redirect back to profile page with error
    $_SESSION['avatar_error'] = $errorMessage;
    header('Location: profile.php');
    exit;
}

// Create uploads directory if it doesn't exist
$uploadsDir = 'uploads/avatars';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Generate a unique filename
$newFileName = uniqid('avatar_') . '.' . $fileExt;
$uploadPath = $uploadsDir . '/' . $newFileName;

// Move the uploaded file
if (move_uploaded_file($fileTmpName, $uploadPath)) {
    // Update the user's avatar in the database
    $db = new Database();
    $conn = $db->getConnection();
    $adminId = $_SESSION['user_id'];
    
    // Check if avatar column exists in admin_users table
    $checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'avatar'";
    $checkColumnResult = $conn->query($checkColumnQuery);
    
    if ($checkColumnResult->num_rows === 0) {
        // Avatar column doesn't exist, add it
        $alterTableQuery = "ALTER TABLE admin_users ADD COLUMN avatar VARCHAR(255) DEFAULT NULL";
        $conn->query($alterTableQuery);
    }
    
    // Update the avatar
    $stmt = $conn->prepare('UPDATE admin_users SET avatar = ? WHERE id = ?');
    $stmt->bind_param('si', $uploadPath, $adminId);
    
    if ($stmt->execute()) {
        $successMessage = 'Avatar updated successfully';
        
        // If it's an AJAX request, return JSON
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            header('Content-Type: application/json');
            $response['success'] = true;
            $response['message'] = $successMessage;
            $response['avatar_path'] = $uploadPath;
            echo json_encode($response);
            exit;
        }
        
        // Otherwise, redirect back to profile page with success message
        $_SESSION['avatar_success'] = $successMessage;
        header('Location: profile.php');
        exit;
    } else {
        $errorMessage = 'Failed to update avatar in database: ' . $conn->error;
        
        // If it's an AJAX request, return JSON
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            header('Content-Type: application/json');
            $response['message'] = $errorMessage;
            echo json_encode($response);
            exit;
        }
        
        // Otherwise, redirect back to profile page with error
        $_SESSION['avatar_error'] = $errorMessage;
        header('Location: profile.php');
        exit;
    }
} else {
    $errorMessage = 'Failed to upload file';
    
    // If it's an AJAX request, return JSON
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json');
        $response['message'] = $errorMessage;
        echo json_encode($response);
        exit;
    }
    
    // Otherwise, redirect back to profile page with error
    $_SESSION['avatar_error'] = $errorMessage;
    header('Location: profile.php');
    exit;
}
