<?php
/**
 * AJAX endpoint to update a course's WhatsApp number
 */

// Prevent any output before JSON response
ob_start();

// Disable error display for AJAX requests
ini_set('display_errors', 0);
error_reporting(0);

try {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if session is working properly
    if (!isset($_SESSION)) {
        throw new Exception('Session is not available');
    }

    // Include necessary files
    require_once '../includes/config.php';
    require_once '../includes/utilities.php';

    // Debug session
    error_log('Session data: ' . print_r($_SESSION, true));

    // Check if user is logged in and has admin privileges
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        throw new Exception('Authentication required');
    }

    // Check admin privileges - accept either 'role' = 'admin' OR 'is_admin' = 1
    $isAdmin = false;

    // Check role
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $isAdmin = true;
    }

    // Check is_admin flag
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == 1) {
        $isAdmin = true;
    }

    if (!$isAdmin) {
        throw new Exception('Admin privileges required');
    }

    // Initialize database connection
    try {
        $db = new Database();
        $conn = $db->getConnection();

        // Check if connection is valid
        if (!$conn || $conn->connect_error) {
            throw new Exception('Database connection failed');
        }
    } catch (Exception $e) {
        // Fallback to direct connection if Database class fails
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($conn->connect_error) {
            throw new Exception('Database connection failed: ' . $conn->connect_error);
        }
    }

    // Set content type to JSON
    header('Content-Type: application/json');

    // Check if the request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Check if user is logged in and has admin privileges
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
        throw new Exception('Unauthorized access');
    }

    // Get and validate input
    $courseId = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    $whatsappNumber = isset($_POST['whatsapp_number']) ? Utilities::sanitizeInput($_POST['whatsapp_number']) : '';

    // Validate course ID
    if ($courseId <= 0) {
        throw new Exception('Invalid course ID');
    }

    // Validate WhatsApp number (basic validation)
    if (!empty($whatsappNumber)) {
        // Remove any spaces or dashes
        $whatsappNumber = preg_replace('/[\s-]/', '', $whatsappNumber);

        // Ensure it starts with a plus sign if it doesn't already
        if (!empty($whatsappNumber) && $whatsappNumber[0] !== '+') {
            $whatsappNumber = '+' . $whatsappNumber;
        }
    }

    // Update the course WhatsApp number in the database
    $stmt = $conn->prepare("UPDATE courses SET whatsapp_number = ? WHERE id = ?");
    if (!$stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }

    $stmt->bind_param("si", $whatsappNumber, $courseId);

    if (!$stmt->execute()) {
        throw new Exception('Failed to update WhatsApp number: ' . $stmt->error);
    }

    // Clear any previous output
    ob_clean();

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'WhatsApp number updated successfully',
        'whatsapp_number' => $whatsappNumber
    ]);

} catch (Exception $e) {
    // Clear any previous output
    ob_clean();

    // Log the error
    error_log('WhatsApp update error: ' . $e->getMessage());

    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// End output buffer and flush
ob_end_flush();
