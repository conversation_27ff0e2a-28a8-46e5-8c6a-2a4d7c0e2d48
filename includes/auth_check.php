<?php
/**
 * Authentication check for admin pages
 * This file is included in admin pages to ensure the user is logged in and has admin privileges
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    // For AJAX requests, return JSON error
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Authentication required'
        ]);
        exit;
    }

    // For regular requests, redirect to login page
    header('Location: ../login.php');
    exit;
}

// Check if user has admin or staff privileges
if ((!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) &&
    (!isset($_SESSION['is_staff']) || $_SESSION['is_staff'] != 1)) {
    // For AJAX requests, return JSON error
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Admin or staff privileges required'
        ]);
        exit;
    }

    // For regular requests, redirect to login page with error message
    $_SESSION['login_error'] = 'You do not have permission to access the admin area.';
    header('Location: ../login.php');
    exit;
}

// Update last activity time
$_SESSION['last_activity'] = time();
