/*
 * Modern Action Menu - Enhanced Version
 * Refined, minimal design with smart positioning and subtle animations
 */

/* Action button styling */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
    padding: 0;
    position: relative;
    -webkit-tap-highlight-color: transparent;
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #495057;
    transform: scale(1.05);
}

.action-btn:active {
    transform: scale(0.95);
}

.action-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
}

.action-btn i {
    font-size: 16px;
    line-height: 1;
}

/* Action menu container */
.action-menu-container {
    position: relative;
    display: inline-block;
}

/* Action menu styling */
.action-menu {
    position: fixed;
    min-width: 180px;
    max-width: 280px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.16);
    z-index: 9999;
    overflow: hidden;
    display: none;
    opacity: 0;
    transform: scale(0.95);
    transform-origin: top right;
    transition: opacity 0.2s ease, transform 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    pointer-events: auto !important; /* Ensure menu is always clickable */
}

.action-menu.show {
    display: block;
    opacity: 1;
    transform: scale(1);
    animation: menuFadeIn 0.2s ease-out;
    pointer-events: auto !important; /* Ensure menu is always clickable */
}

@keyframes menuFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Action menu items */
.action-menu-header {
    padding: 10px 16px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    pointer-events: auto !important; /* Ensure header is always clickable */
}

.action-menu-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    color: #212529;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    position: relative;
    overflow: hidden;
    pointer-events: auto !important; /* Ensure menu items are always clickable */
    z-index: 10000 !important; /* Ensure menu items are above other elements */
}

.action-menu-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.action-menu-item:active {
    background-color: #f0f0f0;
}

.action-menu-item::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background-color: rgba(13, 110, 253, 0.1);
    transition: width 0.3s ease;
}

.action-menu-item:hover::after {
    width: 3px;
}

.action-menu-item i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
    font-size: 14px;
    transition: transform 0.2s ease;
}

.action-menu-item:hover i {
    transform: translateX(2px);
}

/* Action menu divider */
.action-menu-divider {
    height: 1px;
    background-color: rgba(0, 0, 0, 0.05);
    margin: 6px 0;
}

/* Danger zone items */
.action-menu-item.danger {
    color: #dc3545;
}

.action-menu-item.danger:hover {
    background-color: rgba(220, 53, 69, 0.05);
    color: #dc3545;
}

.action-menu-item.danger:hover::after {
    background-color: rgba(220, 53, 69, 0.5);
    width: 3px;
}

/* Fix for table overflow issues */
.table-responsive {
    overflow: visible !important;
}

/* Fix for action cells */
.actions-cell {
    position: static !important;
}

/* Ensure proper positioning for action buttons in tables */
td .action-menu-container {
    display: inline-flex;
}

/* Special styling for bulk action buttons */
.btn.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    width: auto;
    height: auto;
    transition: all 0.2s ease;
}

.btn.action-btn i {
    margin-right: 0.5rem;
}

/* Success button styling */
.btn-success.action-btn {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-success.action-btn:hover {
    background-color: #219150;
    border-color: #219150;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-success.action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Action menu with arrow indicator */
.action-menu {
    --arrow-left: 20px;
}

.action-menu::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background: white;
    transform: rotate(45deg);
    z-index: 9998;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-right: none;
    border-bottom: none;
}

.action-menu.position-top::before {
    bottom: -5px;
    left: var(--arrow-left, calc(100% - 20px));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-left: none;
    border-top: none;
}

.action-menu.position-bottom::before {
    top: -5px;
    left: var(--arrow-left, 20px);
}

.action-menu.position-middle::before {
    top: 50%;
    margin-top: -5px;
    left: -5px;
}

/* Special fix for middle position to ensure it's clickable */
.action-menu.position-middle {
    z-index: 10000 !important;
    pointer-events: auto !important;
}

/* Ensure all menu items are clickable regardless of position */
.action-menu.position-middle .action-menu-item,
.action-menu.position-top .action-menu-item,
.action-menu.position-bottom .action-menu-item {
    pointer-events: auto !important;
    z-index: 10001 !important;
}

/* Responsive styles */
@media (max-width: 768px) {
    .action-menu {
        position: fixed;
        left: 50% !important;
        transform: translateX(-50%) scale(0.95);
        width: 90%;
        max-width: 300px;
    }

    .action-menu.show {
        transform: translateX(-50%) scale(1);
    }

    .action-menu::before {
        display: none;
    }
}
