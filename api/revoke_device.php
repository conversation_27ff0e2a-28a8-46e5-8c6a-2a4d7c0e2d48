<?php
require_once '../includes/header.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Check admin authentication
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    returnError('Admin authentication required', 401);
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        returnError('Invalid JSON input', 400);
    }

    // Validate required fields
    $requiredFields = ['user_id', 'device_id'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            returnError("Missing required field: $field", 400);
        }
    }

    $userId = (int)$input['user_id'];
    $deviceId = trim($input['device_id']);
    $reason = isset($input['reason']) ? trim($input['reason']) : 'Device access revoked by administrator';
    $adminUserId = $_SESSION['user_id'];
    $adminUsername = $_SESSION['username'] ?? $_SESSION['name'] ?? 'Unknown Admin';

    // Get client information for logging
    $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    // Validate user exists
    $userStmt = $conn->prepare("SELECT id, name, username, device_id FROM users WHERE id = ? AND is_active = 1");
    $userStmt->bind_param("i", $userId);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    $user = $userResult->fetch_assoc();
    $userStmt->close();

    if (!$user) {
        returnError('User not found or inactive', 404);
    }

    // Validate device ID matches user's current device
    if ($user['device_id'] !== $deviceId) {
        returnError('Device ID does not match user\'s current device', 400);
    }

    // Start transaction
    $conn->begin_transaction();

    try {
        // 1. Invalidate all API tokens for this user and device
        $revokeTokensStmt = $conn->prepare("
            UPDATE api_tokens 
            SET is_revoked = 1, revoked_by_admin = ?, revoked_at = NOW() 
            WHERE user_id = ? AND (device_id = ? OR device_id IS NULL)
        ");
        $revokeTokensStmt->bind_param("iis", $adminUserId, $userId, $deviceId);
        $revokeTokensStmt->execute();
        $revokedTokensCount = $revokeTokensStmt->affected_rows;
        $revokeTokensStmt->close();

        // 2. Update device session as revoked
        $revokeSessionStmt = $conn->prepare("
            UPDATE device_sessions 
            SET is_active = 0, revoked_by_admin = ?, revoked_at = NOW(), revocation_reason = ?
            WHERE user_id = ? AND device_id = ?
        ");
        $revokeSessionStmt->bind_param("isis", $adminUserId, $reason, $userId, $deviceId);
        $revokeSessionStmt->execute();
        $revokedSessionsCount = $revokeSessionStmt->affected_rows;
        $revokeSessionStmt->close();

        // 3. Clear device ID from user record to force re-authentication
        $clearDeviceStmt = $conn->prepare("UPDATE users SET device_id = NULL WHERE id = ?");
        $clearDeviceStmt->bind_param("i", $userId);
        $clearDeviceStmt->execute();
        $clearDeviceStmt->close();

        // 4. Log admin action
        $actionDetails = json_encode([
            'revoked_tokens' => $revokedTokensCount,
            'revoked_sessions' => $revokedSessionsCount,
            'original_device_id' => $deviceId,
            'timestamp' => date('Y-m-d H:i:s')
        ]);

        $logActionStmt = $conn->prepare("
            INSERT INTO admin_action_logs 
            (admin_user_id, admin_username, action_type, target_user_id, target_username, 
             target_device_id, action_details, reason, ip_address, user_agent) 
            VALUES (?, ?, 'device_revoke', ?, ?, ?, ?, ?, ?, ?)
        ");
        $logActionStmt->bind_param(
            "ississsss", 
            $adminUserId, 
            $adminUsername, 
            $userId, 
            $user['username'], 
            $deviceId, 
            $actionDetails, 
            $reason, 
            $ipAddress, 
            $userAgent
        );
        $logActionStmt->execute();
        $logActionStmt->close();

        // Commit transaction
        $conn->commit();

        // Log the action for monitoring
        error_log("ADMIN ACTION: Device revoked by {$adminUsername} (ID: {$adminUserId}) for user {$user['name']} (ID: {$userId}). Device: {$deviceId}. Reason: {$reason}");

        returnResponse([
            'success' => true,
            'message' => 'Device access revoked successfully',
            'details' => [
                'user_id' => $userId,
                'user_name' => $user['name'],
                'device_id' => $deviceId,
                'revoked_tokens' => $revokedTokensCount,
                'revoked_sessions' => $revokedSessionsCount,
                'reason' => $reason,
                'revoked_by' => $adminUsername,
                'revoked_at' => date('Y-m-d H:i:s')
            ]
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Device revocation error: " . $e->getMessage());
    returnError('Internal server error during device revocation', 500);
}

/**
 * Return JSON response
 */
function returnResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

/**
 * Return error response
 */
function returnError($message, $statusCode = 400) {
    returnResponse([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], $statusCode);
}
?>
