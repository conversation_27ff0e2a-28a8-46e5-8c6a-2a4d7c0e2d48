<?php
/**
 * Add foreign key constraints for admin_users.parent_admin_id and users.assigned_staff_id
 */
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// First, let's fix the data type mismatch
// Check the data type of the id column
$idColumnQuery = $conn->query("SHOW COLUMNS FROM admin_users LIKE 'id'");
$idColumn = $idColumnQuery->fetch_assoc();
$idType = $idColumn['Type']; // This should be 'bigint unsigned'

// Check the data type of parent_admin_id
$parentIdColumnQuery = $conn->query("SHOW COLUMNS FROM admin_users LIKE 'parent_admin_id'");
$parentIdColumn = $parentIdColumnQuery->fetch_assoc();
$parentIdType = $parentIdColumn['Type']; // This is likely 'int'

// Check the data type of assigned_staff_id
$staffIdColumnQuery = $conn->query("SHOW COLUMNS FROM users LIKE 'assigned_staff_id'");
$staffIdColumn = $staffIdColumnQuery->fetch_assoc();
$staffIdType = $staffIdColumn['Type']; // This is likely 'int'

// If there's a mismatch, update the column types
if ($parentIdType !== $idType) {
    $updateParentIdTypeQuery = "ALTER TABLE admin_users MODIFY COLUMN parent_admin_id $idType";
    if ($conn->query($updateParentIdTypeQuery)) {
        echo "Updated 'parent_admin_id' column type to match 'id' column type ($idType).<br>";
    } else {
        echo "Error updating 'parent_admin_id' column type: " . $conn->error . "<br>";
    }
}

if ($staffIdType !== $idType) {
    $updateStaffIdTypeQuery = "ALTER TABLE users MODIFY COLUMN assigned_staff_id $idType";
    if ($conn->query($updateStaffIdTypeQuery)) {
        echo "Updated 'assigned_staff_id' column type to match 'id' column type ($idType).<br>";
    } else {
        echo "Error updating 'assigned_staff_id' column type: " . $conn->error . "<br>";
    }
}

// Check if foreign key for parent_admin_id exists
$checkParentAdminIdFK = $conn->query("
    SELECT * FROM information_schema.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = '" . DB_NAME . "'
    AND TABLE_NAME = 'admin_users'
    AND COLUMN_NAME = 'parent_admin_id'
    AND REFERENCED_TABLE_NAME IS NOT NULL
");
$parentAdminIdFKExists = $checkParentAdminIdFK && $checkParentAdminIdFK->num_rows > 0;

// Check if foreign key for assigned_staff_id exists
$checkAssignedStaffIdFK = $conn->query("
    SELECT * FROM information_schema.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = '" . DB_NAME . "'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'assigned_staff_id'
    AND REFERENCED_TABLE_NAME IS NOT NULL
");
$assignedStaffIdFKExists = $checkAssignedStaffIdFK && $checkAssignedStaffIdFK->num_rows > 0;

// Add foreign key for parent_admin_id if it doesn't exist
if (!$parentAdminIdFKExists) {
    // First, add an index to parent_admin_id
    $addParentAdminIdIndexQuery = "ALTER TABLE admin_users ADD INDEX (parent_admin_id)";
    if ($conn->query($addParentAdminIdIndexQuery)) {
        echo "Index added to 'parent_admin_id' in 'admin_users' table.<br>";

        // Now add the foreign key constraint
        $addParentAdminIdFKQuery = "
            ALTER TABLE admin_users
            ADD CONSTRAINT fk_parent_admin
            FOREIGN KEY (parent_admin_id) REFERENCES admin_users(id)
            ON DELETE SET NULL
        ";

        if ($conn->query($addParentAdminIdFKQuery)) {
            echo "Foreign key constraint added for 'parent_admin_id' in 'admin_users' table.<br>";
        } else {
            echo "Error adding foreign key constraint for 'parent_admin_id': " . $conn->error . "<br>";
        }
    } else {
        echo "Error adding index to 'parent_admin_id': " . $conn->error . "<br>";
    }
} else {
    echo "Foreign key constraint for 'parent_admin_id' already exists.<br>";
}

// Add foreign key for assigned_staff_id if it doesn't exist
if (!$assignedStaffIdFKExists) {
    // First, add an index to assigned_staff_id
    $addAssignedStaffIdIndexQuery = "ALTER TABLE users ADD INDEX (assigned_staff_id)";
    if ($conn->query($addAssignedStaffIdIndexQuery)) {
        echo "Index added to 'assigned_staff_id' in 'users' table.<br>";

        // Now add the foreign key constraint
        $addAssignedStaffIdFKQuery = "
            ALTER TABLE users
            ADD CONSTRAINT fk_assigned_staff
            FOREIGN KEY (assigned_staff_id) REFERENCES admin_users(id)
            ON DELETE SET NULL
        ";

        if ($conn->query($addAssignedStaffIdFKQuery)) {
            echo "Foreign key constraint added for 'assigned_staff_id' in 'users' table.<br>";
        } else {
            echo "Error adding foreign key constraint for 'assigned_staff_id': " . $conn->error . "<br>";
        }
    } else {
        echo "Error adding index to 'assigned_staff_id': " . $conn->error . "<br>";
    }
} else {
    echo "Foreign key constraint for 'assigned_staff_id' already exists.<br>";
}

echo "Foreign key constraints setup completed!";
?>
