        </div> <!-- End of Page Content Container -->

        <!-- Footer -->
        <footer class="footer mt-auto py-3 bg-white border-top">
            <div class="container-fluid px-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.
                    </div>
                    <div class="text-muted">
                        Version 1.0
                    </div>
                </div>
            </div>
        </footer>

    </div> <!-- End of Admin Content -->
</div> <!-- End of Admin Layout -->

    <!-- Bootstrap JS Bundle with <PERSON>per -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/scripts.js"></script>

    <!-- Responsive Admin Panel JS -->
    <script src="assets/js/responsive-admin-panel.js"></script>

    <!-- Real-time Updates -->
    <script src="assets/js/realtime-updates.js"></script>

    <!-- PWA Install FAB Button -->
    <div id="pwa-install-fab" class="pwa-install-fab" style="display: none;">
        <button id="pwa-install-button" class="pwa-install-button" title="Install KFT Admin App">
            <i class="fas fa-download"></i>
            <span class="pwa-install-text">Install App</span>
        </button>
    </div>

    <!-- PWA Install Modal -->
    <div id="pwa-install-modal" class="pwa-modal" style="display: none;">
        <div class="pwa-modal-content">
            <div class="pwa-modal-header">
                <h3>Install KFT Admin App</h3>
                <button id="pwa-modal-close" class="pwa-modal-close">&times;</button>
            </div>
            <div class="pwa-modal-body">
                <div class="pwa-app-info">
                    <img src="assets/img/pwa-icon-96x96.png" alt="KFT Admin" class="pwa-app-icon">
                    <div class="pwa-app-details">
                        <h4>KFT Admin Panel</h4>
                        <p>Install the KFT Admin app for quick access and offline functionality.</p>
                        <ul class="pwa-features">
                            <li><i class="fas fa-check"></i> Quick access from home screen</li>
                            <li><i class="fas fa-check"></i> Offline functionality</li>
                            <li><i class="fas fa-check"></i> Push notifications</li>
                            <li><i class="fas fa-check"></i> Native app experience</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="pwa-modal-footer">
                <button id="pwa-install-confirm" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>Install App
                </button>
                <button id="pwa-install-cancel" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <!-- PWA JavaScript -->
    <script src="assets/js/pwa-install.js"></script>

    <?php
    // Page-specific JavaScript files
    $currentPage = basename($_SERVER['PHP_SELF']);
    if ($currentPage === 'user_add.php'):
    ?>
    <script src="assets/js/user-add.js"></script>
    <?php endif; ?>
<?php
if (function_exists('ob_end_flush')) ob_end_flush();
?>
</body>
</html>
