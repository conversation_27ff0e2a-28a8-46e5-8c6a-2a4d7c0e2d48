const CACHE_NAME = 'kft-admin-v1.0.0';
const STATIC_CACHE_NAME = 'kft-admin-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'kft-admin-dynamic-v1.0.0';

// Files to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.php',
  '/login.php',
  '/assets/css/modern.css',
  '/assets/css/admin-table.css',
  '/assets/css/responsive-admin-panel.css',
  '/assets/css/dashboard-new.css',
  '/assets/css/stats-grid.css',
  '/assets/js/responsive-admin-panel.js',
  '/assets/js/scripts.js',
  '/assets/img/favicon.svg',
  '/assets/img/favicon.ico',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css',
  'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
  'https://cdn.jsdelivr.net/npm/chart.js'
];

// API endpoints that should be cached
const API_CACHE_PATTERNS = [
  /\/ajax_staff_stats\.php$/,
  /\/ajax_new_users_chart\.php$/,
  /\/api\/.*$/
];

// Install event - cache static assets
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Error caching static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME && 
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!request.url.startsWith('http')) {
    return;
  }
  
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        // Return cached version if available
        if (cachedResponse) {
          // For API calls, also fetch fresh data in background
          if (isApiRequest(request.url)) {
            fetchAndCache(request);
          }
          return cachedResponse;
        }
        
        // Not in cache, fetch from network
        return fetchAndCache(request);
      })
      .catch(() => {
        // Network failed, try to serve offline page for navigation requests
        if (request.mode === 'navigate') {
          return caches.match('/offline.html') || 
                 new Response('Offline - Please check your connection', {
                   status: 503,
                   statusText: 'Service Unavailable'
                 });
        }
      })
  );
});

// Helper function to fetch and cache responses
function fetchAndCache(request) {
  return fetch(request)
    .then(response => {
      // Don't cache error responses
      if (!response.ok) {
        return response;
      }
      
      // Clone response for caching
      const responseClone = response.clone();
      const url = new URL(request.url);
      
      // Determine which cache to use
      let cacheName = DYNAMIC_CACHE_NAME;
      if (STATIC_ASSETS.includes(request.url) || 
          STATIC_ASSETS.includes(url.pathname)) {
        cacheName = STATIC_CACHE_NAME;
      }
      
      // Cache the response
      caches.open(cacheName)
        .then(cache => {
          cache.put(request, responseClone);
        })
        .catch(error => {
          console.error('Service Worker: Error caching response:', error);
        });
      
      return response;
    });
}

// Helper function to check if request is for API
function isApiRequest(url) {
  return API_CACHE_PATTERNS.some(pattern => pattern.test(url));
}

// Background sync for offline actions
self.addEventListener('sync', event => {
  console.log('Service Worker: Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

// Handle background sync
function doBackgroundSync() {
  return new Promise((resolve, reject) => {
    // Get pending actions from IndexedDB or localStorage
    // This would sync any offline actions when connection is restored
    console.log('Service Worker: Performing background sync');
    resolve();
  });
}

// Push notification handler
self.addEventListener('push', event => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New notification from KFT Admin',
    icon: '/assets/img/pwa-icon-192x192.png',
    badge: '/assets/img/pwa-icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View',
        icon: '/assets/img/pwa-icon-192x192.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/assets/img/pwa-icon-192x192.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('KFT Admin', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Message handler for communication with main thread
self.addEventListener('message', event => {
  console.log('Service Worker: Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    const urls = event.data.payload;
    event.waitUntil(
      caches.open(DYNAMIC_CACHE_NAME)
        .then(cache => cache.addAll(urls))
    );
  }
});
