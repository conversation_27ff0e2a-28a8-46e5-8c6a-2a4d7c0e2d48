<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if image_url column exists in calorie_logs table
$checkColumnQuery = "SHOW COLUMNS FROM calorie_logs LIKE 'image_url'";
$checkColumnResult = $conn->query($checkColumnQuery);

if ($checkColumnResult->num_rows === 0) {
    // Add image_url column to calorie_logs table
    $alterTableQuery = "ALTER TABLE calorie_logs ADD COLUMN image_url VARCHAR(255) DEFAULT NULL AFTER notes";
    
    if ($conn->query($alterTableQuery)) {
        echo "Column 'image_url' added to 'calorie_logs' table successfully.<br>";
    } else {
        echo "Error adding column 'image_url' to 'calorie_logs' table: " . $conn->error . "<br>";
    }
} else {
    echo "Column 'image_url' already exists in 'calorie_logs' table.<br>";
}

// Create food_images directory if it doesn't exist
$uploadDir = __DIR__ . '/assets/food_images';
if (!is_dir($uploadDir)) {
    if (mkdir($uploadDir, 0777, true)) {
        echo "Directory 'assets/food_images' created successfully.<br>";
    } else {
        echo "Error creating directory 'assets/food_images'.<br>";
    }
} else {
    echo "Directory 'assets/food_images' already exists.<br>";
}

echo "Calorie logs table update completed.";
?>
