/**
 * KFT Fitness Admin Dashboard - Profile Page Styles
 * Modern, pixel-perfect profile page styling
 */

/* Black & White Profile Theme Overrides */
:root {
    --primary: #111;
    --primary-dark: #222;
    --gray-100: #f5f5f5;
    --gray-200: #e0e0e0;
    --gray-600: #888;
    --gray-700: #666;
    --gray-800: #222;
    --gray-900: #111;
    --font-weight-bold: 700;
    --font-weight-medium: 500;
    --border-radius-lg: 1rem;
    --shadow: 0 4px 12px rgba(0,0,0,0.08);
    --transition-base: all 0.2s;
    --transition-fast: all 0.1s;
}

/* Profile Container */
.profile-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Profile Header */
.profile-header {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: #fff !important;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.profile-cover {
    height: 200px;
    background: #111 !important;
    position: relative;
}

.profile-avatar-container {
    position: absolute;
    bottom: -60px;
    left: 50px;
    z-index: 10;
}

.profile-avatar-wrapper {
    position: relative;
    width: 120px;
    height: 120px;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #111 !important;
    color: #fff !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: var(--font-weight-bold);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 4px solid white;
    overflow: hidden;
    transition: var(--transition-base);
}

.profile-avatar-upload {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff !important;
    color: #111 !important;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200) !important;
    transition: var(--transition-base);
}

.profile-avatar-upload:hover {
    background: #222 !important;
    color: #fff !important;
    transform: scale(1.1);
}

.profile-avatar-upload input[type="file"] {
    display: none;
}

.profile-info {
    padding: 80px 50px 30px;
}

.profile-name {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
    color: #111 !important;
}

.profile-role {
    font-size: 1rem;
    color: #666 !important;
    margin-bottom: 1rem;
}

.profile-meta {
    display: flex;
    gap: 2rem;
    margin-top: 1.5rem;
}

.profile-meta-item {
    display: flex;
    align-items: center;
    color: #888 !important;
    font-size: 0.9rem;
}

.profile-meta-item i {
    margin-right: 0.5rem;
    color: #888 !important;
    font-size: 1rem;
}

/* Profile Content */
.profile-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

@media (max-width: 992px) {
    .profile-content {
        grid-template-columns: 1fr;
    }
}

/* Profile Sidebar */
.profile-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.profile-nav {
    background: #fff !important;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.profile-nav-header {
    padding: 1.25rem;
    background-color: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
}

.profile-nav-title {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
    color: #111 !important;
    margin: 0;
}

.profile-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-nav-item {
    border-bottom: 1px solid var(--gray-200);
}

.profile-nav-item:last-child {
    border-bottom: none;
}

.profile-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    color: #666 !important;
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
}

.profile-nav-link:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: #111 !important;
    opacity: 0;
    transition: var(--transition-fast);
}

.profile-nav-link:hover {
    background: #f5f5f5 !important;
    color: #111 !important;
}

.profile-nav-link.active {
    background: #f5f5f5 !important;
    color: #111 !important;
    font-weight: var(--font-weight-medium);
}

.profile-nav-link.active:before {
    opacity: 1;
}

.profile-nav-link i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Profile Card */
.profile-card {
    background: #fff !important;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.profile-card-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.profile-card-title {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: #111 !important;
    margin: 0;
}

.profile-card-body {
    padding: 1.5rem;
}

.profile-card-footer {
    padding: 1.25rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
}

/* Form Styling */
.profile-form-group {
    margin-bottom: 1.5rem;
}

.profile-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: var(--font-weight-medium);
    color: #111 !important;
}

.profile-form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background: #fff !important;
    color: #111 !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.profile-form-control:focus {
    border-color: #111 !important;
    box-shadow: 0 0 0 2px #1111 !important;
}

.profile-form-text {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #666 !important;
}

/* Password Strength Meter */
progress.password-strength-meter {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 8px;
    background: #e0e0e0 !important;
    color: #111 !important;
    border-radius: 6px;
    overflow: hidden;
}

progress.password-strength-meter::-webkit-progress-bar {
    background: #e0e0e0 !important;
}

progress.password-strength-meter::-webkit-progress-value {
    background: #111 !important;
}

progress.password-strength-meter.strength-0::-webkit-progress-value {
    background: #111 !important;
}

progress.password-strength-meter.strength-1::-webkit-progress-value {
    background: #111 !important;
}

progress.password-strength-meter.strength-2::-webkit-progress-value {
    background: #111 !important;
}

progress.password-strength-meter.strength-3::-webkit-progress-value {
    background: #111 !important;
}

progress.password-strength-meter.strength-4::-webkit-progress-value {
    background: #111 !important;
}

.password-strength-text {
    font-size: 0.85rem;
    font-weight: var(--font-weight-medium);
    transition: var(--transition-base);
    color: #888 !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .profile-avatar-container {
        left: 50%;
        transform: translateX(-50%);
    }

    .profile-info {
        padding-top: 100px;
        text-align: center;
    }

    .profile-meta {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Button Styles */
.btn-primary, .btn-outline-primary, .btn-outline-dark {
    background: #111 !important;
    color: #fff !important;
    border-color: #111 !important;
}

.btn-outline-dark {
    background: #fff !important;
    color: #111 !important;
    border-color: #111 !important;
}

.btn-outline-dark:hover, .btn-outline-dark:focus {
    background: #111 !important;
    color: #fff !important;
}

/* Alert Styles */
.alert-success, .alert-danger, .alert-info, .alert-warning {
    background: #f5f5f5 !important;
    color: #111 !important;
    border: 1px solid #e0e0e0 !important;
}
