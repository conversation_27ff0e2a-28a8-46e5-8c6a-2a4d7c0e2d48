<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to reassign users.');
    Utilities::redirect('index.php');
    exit;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Utilities::setFlashMessage('error', 'Invalid request method.');
    Utilities::redirect('staff_management.php');
    exit;
}

// Check if required parameters are provided
if (!isset($_POST['from_staff_id']) || !is_numeric($_POST['from_staff_id']) ||
    !isset($_POST['to_staff_id']) || !is_numeric($_POST['to_staff_id']) ||
    !isset($_POST['reassign_option'])) {
    Utilities::setFlashMessage('error', 'Missing required parameters.');
    Utilities::redirect('staff_management.php');
    exit;
}

$fromStaffId = (int)$_POST['from_staff_id'];
$toStaffId = (int)$_POST['to_staff_id'];
$reassignOption = $_POST['reassign_option'];

// Verify both staff members exist
$staffQuery = "SELECT id, name FROM admin_users WHERE id IN (?, ?) AND role = 'staff'";
$staffStmt = $conn->prepare($staffQuery);
$staffStmt->bind_param("ii", $fromStaffId, $toStaffId);
$staffStmt->execute();
$staffResult = $staffStmt->get_result();

if ($staffResult->num_rows !== 2) {
    Utilities::setFlashMessage('error', 'One or both staff members not found.');
    Utilities::redirect('staff_management.php');
    exit;
}

// Get staff names for the message
$staffNames = [];
while ($staff = $staffResult->fetch_assoc()) {
    $staffNames[$staff['id']] = $staff['name'];
}

// Build the query based on the reassign option
$updateQuery = "UPDATE users SET assigned_staff_id = ? WHERE assigned_staff_id = ?";

switch ($reassignOption) {
    case 'active':
        $updateQuery .= " AND is_active = 1";
        break;
    case 'inactive':
        $updateQuery .= " AND is_active = 0";
        break;
    case 'premium':
        $updateQuery .= " AND is_premium = 1";
        break;
    case 'all':
    default:
        // No additional conditions for 'all'
        break;
}

// Execute the update
$updateStmt = $conn->prepare($updateQuery);
$updateStmt->bind_param("ii", $toStaffId, $fromStaffId);
$updateStmt->execute();

$affectedRows = $updateStmt->affected_rows;

if ($affectedRows > 0) {
    $optionText = '';
    switch ($reassignOption) {
        case 'active':
            $optionText = 'active';
            break;
        case 'inactive':
            $optionText = 'inactive';
            break;
        case 'premium':
            $optionText = 'premium';
            break;
        case 'all':
        default:
            $optionText = 'all';
            break;
    }
    
    $message = "$affectedRows $optionText users reassigned from " . 
               htmlspecialchars($staffNames[$fromStaffId]) . " to " . 
               htmlspecialchars($staffNames[$toStaffId]);
    
    Utilities::setFlashMessage('success', $message);
} else {
    Utilities::setFlashMessage('info', 'No users were reassigned. There might be no users matching the criteria.');
}

// Redirect back to staff management page
Utilities::redirect('staff_management.php');
?>
