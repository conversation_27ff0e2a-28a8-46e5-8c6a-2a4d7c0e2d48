<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../includes/config.php';
require_once '../includes/jwt_helper.php';

// Enable error reporting for development
if (DEV_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    echo "[" . date('Y-m-d H:i:s') . "] API running in development mode\n";
}

try {
    // Get and validate JWT token
    $user_id = validateJWTFromRequest();
    
    if (!$user_id) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Authentication required'
        ]);
        exit();
    }

    echo "[" . date('Y-m-d H:i:s') . "] Streak API: User authenticated successfully: User ID = $user_id\n";

    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetStreak($user_id);
            break;
        case 'POST':
            handlePostStreak($user_id);
            break;
        case 'PUT':
            handleUpdateStreak($user_id);
            break;
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
            break;
    }

} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] Streak API Error: " . $e->getMessage() . "\n";
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => DEV_MODE ? $e->getMessage() : 'Server error'
    ]);
}

function handleGetStreak($user_id) {
    global $pdo;
    
    try {
        echo "[" . date('Y-m-d H:i:s') . "] Getting streak data for user ID: $user_id\n";
        
        // Get user's streak data
        $stmt = $pdo->prepare("
            SELECT 
                current_streak,
                last_completion_date,
                total_completions,
                best_streak,
                created_at,
                updated_at
            FROM user_streaks 
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $streak_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get completion history (last 30 days)
        $stmt = $pdo->prepare("
            SELECT 
                completion_date,
                completion_type,
                activities,
                completed_at
            FROM streak_completions 
            WHERE user_id = ? 
            AND completion_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            ORDER BY completion_date DESC
        ");
        $stmt->execute([$user_id]);
        $completions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Process completions data
        $processed_completions = [];
        foreach ($completions as $completion) {
            $processed_completions[$completion['completion_date']] = [
                'completion_type' => $completion['completion_type'],
                'activities' => json_decode($completion['activities'], true) ?: [],
                'completed_at' => $completion['completed_at']
            ];
        }
        
        // Calculate stats
        $completion_rate = count($completions) > 0 ? round((count($completions) / 30) * 100) : 0;
        
        $response = [
            'success' => true,
            'streak_data' => [
                'current_streak' => (int)($streak_data['current_streak'] ?? 0),
                'last_completion_date' => $streak_data['last_completion_date'] ?? null,
                'total_completions' => (int)($streak_data['total_completions'] ?? 0),
                'best_streak' => (int)($streak_data['best_streak'] ?? 0),
                'completion_rate' => $completion_rate,
                'completions' => $processed_completions
            ],
            'user_id' => $user_id,
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => DEV_MODE
        ];
        
        echo "[" . date('Y-m-d H:i:s') . "] Streak data retrieved successfully\n";
        echo json_encode($response);
        
    } catch (Exception $e) {
        echo "[" . date('Y-m-d H:i:s') . "] Error getting streak data: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function handlePostStreak($user_id) {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Invalid JSON input'
            ]);
            return;
        }
        
        echo "[" . date('Y-m-d H:i:s') . "] Creating/updating streak for user ID: $user_id\n";
        
        $completion_type = $input['completion_type'] ?? 'general';
        $activity = $input['activity'] ?? 'Activity completed';
        $completion_date = $input['completion_date'] ?? date('Y-m-d');
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Check if completion already exists for today
        $stmt = $pdo->prepare("
            SELECT id, activities 
            FROM streak_completions 
            WHERE user_id = ? AND completion_date = ?
        ");
        $stmt->execute([$user_id, $completion_date]);
        $existing_completion = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing_completion) {
            // Update existing completion
            $existing_activities = json_decode($existing_completion['activities'], true) ?: [];
            $existing_activities[] = $activity;
            
            $stmt = $pdo->prepare("
                UPDATE streak_completions 
                SET activities = ?, completion_type = ?, completed_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([
                json_encode($existing_activities),
                $completion_type,
                $existing_completion['id']
            ]);
            
            echo "[" . date('Y-m-d H:i:s') . "] Updated existing completion\n";
        } else {
            // Create new completion
            $stmt = $pdo->prepare("
                INSERT INTO streak_completions 
                (user_id, completion_date, completion_type, activities, completed_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $user_id,
                $completion_date,
                $completion_type,
                json_encode([$activity])
            ]);
            
            echo "[" . date('Y-m-d H:i:s') . "] Created new completion\n";
        }
        
        // Calculate and update streak
        $current_streak = calculateStreak($user_id);
        
        // Update or create user streak record
        $stmt = $pdo->prepare("
            INSERT INTO user_streaks 
            (user_id, current_streak, last_completion_date, total_completions, best_streak, updated_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            current_streak = VALUES(current_streak),
            last_completion_date = VALUES(last_completion_date),
            total_completions = total_completions + 1,
            best_streak = GREATEST(best_streak, VALUES(current_streak)),
            updated_at = NOW()
        ");
        
        $total_completions = getTotalCompletions($user_id);
        $stmt->execute([
            $user_id,
            $current_streak,
            date('Y-m-d H:i:s'),
            $total_completions,
            $current_streak
        ]);
        
        $pdo->commit();
        
        echo "[" . date('Y-m-d H:i:s') . "] Streak updated successfully: $current_streak days\n";
        
        echo json_encode([
            'success' => true,
            'message' => 'Streak updated successfully',
            'current_streak' => $current_streak,
            'completion_date' => $completion_date,
            'activity' => $activity
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "[" . date('Y-m-d H:i:s') . "] Error updating streak: " . $e->getMessage() . "\n";
        throw $e;
    }
}

function calculateStreak($user_id) {
    global $pdo;
    
    // Get all completion dates in descending order
    $stmt = $pdo->prepare("
        SELECT DISTINCT completion_date 
        FROM streak_completions 
        WHERE user_id = ? 
        ORDER BY completion_date DESC
    ");
    $stmt->execute([$user_id]);
    $dates = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($dates)) {
        return 0;
    }
    
    $streak = 0;
    $current_date = new DateTime();
    $current_date_str = $current_date->format('Y-m-d');
    
    // Check if today is completed
    if ($dates[0] === $current_date_str) {
        $streak = 1;
        $check_date = new DateTime($current_date_str);
        $check_date->modify('-1 day');
        
        // Count consecutive days backwards
        for ($i = 1; $i < count($dates); $i++) {
            $expected_date = $check_date->format('Y-m-d');
            
            if ($dates[$i] === $expected_date) {
                $streak++;
                $check_date->modify('-1 day');
            } else {
                break;
            }
        }
    }
    
    return $streak;
}

function getTotalCompletions($user_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM streak_completions 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    
    return (int)$stmt->fetchColumn();
}

function handleUpdateStreak($user_id) {
    // Handle bulk updates or corrections
    handlePostStreak($user_id);
}
?>
