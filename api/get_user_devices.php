<?php
require_once '../includes/header.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    returnError('Method not allowed', 405);
}

// Check admin authentication
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    returnError('Admin authentication required', 401);
}

try {
    $userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
    $deviceId = isset($_GET['device_id']) ? trim($_GET['device_id']) : null;
    $includeRevoked = isset($_GET['include_revoked']) ? (bool)$_GET['include_revoked'] : false;

    // Build query based on parameters
    $whereConditions = [];
    $params = [];
    $types = '';

    if ($userId) {
        $whereConditions[] = "ads.user_id = ?";
        $params[] = $userId;
        $types .= 'i';
    }

    if ($deviceId) {
        $whereConditions[] = "ads.device_id = ?";
        $params[] = $deviceId;
        $types .= 's';
    }

    if (!$includeRevoked) {
        $whereConditions[] = "ads.is_active = 1";
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    $query = "
        SELECT 
            ads.id,
            ads.user_id,
            ads.user_name,
            ads.username,
            ads.phone_number,
            ads.device_id,
            ads.device_info,
            ads.first_login,
            ads.last_activity,
            ads.is_active,
            ads.revoked_by_admin,
            ads.revoked_at,
            ads.revocation_reason,
            ads.revoked_by_username,
            ads.active_tokens,
            u.is_active as user_active,
            u.created_at as user_created,
            u.last_login as user_last_login
        FROM active_device_sessions ads
        JOIN users u ON ads.user_id = u.id
        $whereClause
        ORDER BY ads.last_activity DESC
    ";

    if (!empty($params)) {
        $stmt = $conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $conn->query($query);
    }

    $devices = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Parse device info JSON
            $deviceInfo = json_decode($row['device_info'], true) ?? [];
            
            // Calculate activity metrics
            $lastActivity = strtotime($row['last_activity']);
            $isRecentlyActive = (time() - $lastActivity) < (24 * 60 * 60); // 24 hours
            $daysSinceLastActivity = floor((time() - $lastActivity) / (24 * 60 * 60));
            
            $devices[] = [
                'id' => (int)$row['id'],
                'user_id' => (int)$row['user_id'],
                'user_name' => $row['user_name'],
                'username' => $row['username'],
                'phone_number' => $row['phone_number'],
                'device_id' => $row['device_id'],
                'device_info' => $deviceInfo,
                'first_login' => $row['first_login'],
                'last_activity' => $row['last_activity'],
                'is_active' => (bool)$row['is_active'],
                'user_active' => (bool)$row['user_active'],
                'active_tokens' => (int)$row['active_tokens'],
                'revoked_by_admin' => $row['revoked_by_admin'] ? (int)$row['revoked_by_admin'] : null,
                'revoked_at' => $row['revoked_at'],
                'revocation_reason' => $row['revocation_reason'],
                'revoked_by_username' => $row['revoked_by_username'],
                'user_created' => $row['user_created'],
                'user_last_login' => $row['user_last_login'],
                'activity_metrics' => [
                    'is_recently_active' => $isRecentlyActive,
                    'days_since_last_activity' => $daysSinceLastActivity,
                    'activity_status' => $isRecentlyActive ? 'active' : ($daysSinceLastActivity < 7 ? 'recent' : 'inactive')
                ]
            ];
        }
    }

    // Get additional statistics if no specific filters
    $statistics = null;
    if (!$userId && !$deviceId) {
        $statistics = [
            'total_devices' => count($devices),
            'active_devices' => count(array_filter($devices, fn($d) => $d['is_active'])),
            'recently_active' => count(array_filter($devices, fn($d) => $d['activity_metrics']['is_recently_active'])),
            'unique_users' => count(array_unique(array_column($devices, 'user_id')))
        ];
    }

    returnResponse([
        'success' => true,
        'devices' => $devices,
        'statistics' => $statistics,
        'filters' => [
            'user_id' => $userId,
            'device_id' => $deviceId,
            'include_revoked' => $includeRevoked
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    error_log("Get user devices error: " . $e->getMessage());
    returnError('Internal server error while fetching device information', 500);
}

/**
 * Return JSON response
 */
function returnResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

/**
 * Return error response
 */
function returnError($message, $statusCode = 400) {
    returnResponse([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], $statusCode);
}
?>
