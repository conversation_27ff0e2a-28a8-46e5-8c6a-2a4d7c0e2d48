<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Create calorie_logs table
$createCalorieLogsTable = "
CREATE TABLE IF NOT EXISTS `calorie_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `log_date` date NOT NULL,
  `meal_type` enum('breakfast','lunch','dinner','snack') NOT NULL,
  `food_item_id` int(11) DEFAULT NULL,
  `food_name` varchar(255) NOT NULL,
  `calories` int(11) NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `quantity` decimal(5,2) NOT NULL DEFAULT '1.00',
  `serving_size` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `food_item_id` (`food_item_id`),
  KEY `log_date` (`log_date`),
  CONSTRAINT `calorie_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Create food_items table
$createFoodItemsTable = "
CREATE TABLE IF NOT EXISTS `food_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `calories` int(11) NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `serving_size` varchar(50) DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `name` (`name`),
  KEY `category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Create user_food_items table
$createUserFoodItemsTable = "
CREATE TABLE IF NOT EXISTS `user_food_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `calories` int(11) NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `serving_size` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_food_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Create calorie_goals table
$createCalorieGoalsTable = "
CREATE TABLE IF NOT EXISTS `calorie_goals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `daily_calories` int(11) NOT NULL,
  `protein_goal` decimal(5,2) DEFAULT NULL,
  `carbs_goal` decimal(5,2) DEFAULT NULL,
  `fat_goal` decimal(5,2) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `calorie_goals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Execute the queries
try {
    if ($conn->query($createCalorieLogsTable)) {
        echo "Table 'calorie_logs' created successfully.<br>";
    } else {
        echo "Error creating table 'calorie_logs': " . $conn->error . "<br>";
    }

    if ($conn->query($createFoodItemsTable)) {
        echo "Table 'food_items' created successfully.<br>";
    } else {
        echo "Error creating table 'food_items': " . $conn->error . "<br>";
    }

    if ($conn->query($createUserFoodItemsTable)) {
        echo "Table 'user_food_items' created successfully.<br>";
    } else {
        echo "Error creating table 'user_food_items': " . $conn->error . "<br>";
    }

    if ($conn->query($createCalorieGoalsTable)) {
        echo "Table 'calorie_goals' created successfully.<br>";
    } else {
        echo "Error creating table 'calorie_goals': " . $conn->error . "<br>";
    }

    // Insert some sample food items
    $insertSampleFoodItems = "
    INSERT INTO `food_items` (`name`, `calories`, `protein`, `carbs`, `fat`, `serving_size`, `category`) VALUES
    ('Apple', 95, 0.5, 25.0, 0.3, '1 medium (182g)', 'Fruits'),
    ('Banana', 105, 1.3, 27.0, 0.4, '1 medium (118g)', 'Fruits'),
    ('Chicken Breast', 165, 31.0, 0.0, 3.6, '100g', 'Protein'),
    ('Egg', 78, 6.3, 0.6, 5.3, '1 large (50g)', 'Protein'),
    ('Brown Rice', 216, 5.0, 45.0, 1.8, '1 cup cooked (195g)', 'Grains'),
    ('Broccoli', 55, 3.7, 11.2, 0.6, '1 cup (91g)', 'Vegetables'),
    ('Salmon', 206, 22.0, 0.0, 13.0, '100g', 'Protein'),
    ('Greek Yogurt', 100, 17.0, 6.0, 0.4, '170g', 'Dairy'),
    ('Almonds', 164, 6.0, 6.1, 14.0, '28g (1oz)', 'Nuts'),
    ('Avocado', 240, 3.0, 12.0, 22.0, '1 medium (200g)', 'Fruits'),
    ('Oatmeal', 150, 5.0, 27.0, 2.5, '1 cup cooked (234g)', 'Grains'),
    ('Spinach', 23, 2.9, 3.6, 0.4, '100g', 'Vegetables'),
    ('Sweet Potato', 112, 2.0, 26.0, 0.1, '1 medium (130g)', 'Vegetables'),
    ('Quinoa', 222, 8.1, 39.0, 3.6, '1 cup cooked (185g)', 'Grains'),
    ('Milk', 103, 8.0, 12.0, 2.4, '1 cup (244g)', 'Dairy')
    ";

    if ($conn->query($insertSampleFoodItems)) {
        echo "Sample food items inserted successfully.<br>";
    } else {
        echo "Error inserting sample food items: " . $conn->error . "<br>";
    }

    echo "Calorie tracking tables setup completed.";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
