<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $vimeoId = $input['vimeo_id'] ?? '';
    $videoId = $input['video_id'] ?? '';
    $domain = $input['domain'] ?? '';
    $accessToken = $input['access_token'] ?? '';
    $clientId = $input['client_id'] ?? '';
    
    // Validate required parameters
    if (empty($vimeoId) || empty($domain) || empty($accessToken)) {
        throw new Exception('Missing required parameters for Vimeo Pro domain verification');
    }
    
    // Verify domain is authorized
    $authorizedDomains = [
        'mycloudforge.com',
        'www.mycloudforge.com',
        'com.kft.fitness',
        'capacitor://localhost',
        'ionic://localhost',
        'localhost',
        '127.0.0.1'
    ];
    
    if (!isAuthorizedDomain($domain, $authorizedDomains)) {
        echo json_encode([
            'success' => false,
            'domain_verified' => false,
            'message' => 'Domain not authorized for Vimeo Pro access'
        ]);
        exit;
    }
    
    // Verify with Vimeo Pro API
    $verificationResult = verifyVimeoProDomainAccess($vimeoId, $domain, $accessToken);
    
    if ($verificationResult['success']) {
        // Log successful verification
        logVimeoProDomainVerification($pdo, $vimeoId, $videoId, $domain, 'verified');
        
        echo json_encode([
            'success' => true,
            'domain_verified' => true,
            'video_accessible' => $verificationResult['accessible'],
            'privacy_settings' => $verificationResult['privacy'],
            'embed_allowed' => $verificationResult['embed_allowed'],
            'message' => 'Vimeo Pro domain verification successful'
        ]);
    } else {
        // Log failed verification
        logVimeoProDomainVerification($pdo, $vimeoId, $videoId, $domain, 'failed', $verificationResult['error']);
        
        echo json_encode([
            'success' => false,
            'domain_verified' => false,
            'message' => $verificationResult['error'] ?? 'Vimeo Pro domain verification failed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'domain_verified' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Check if domain is authorized for Vimeo Pro access
 */
function isAuthorizedDomain($domain, $authorizedDomains) {
    $normalizedDomain = strtolower(str_replace(['https://', 'http://', 'www.'], '', $domain));
    
    foreach ($authorizedDomains as $authorizedDomain) {
        $normalizedAuthorized = strtolower(str_replace(['https://', 'http://', 'www.'], '', $authorizedDomain));
        
        if (strpos($normalizedDomain, $normalizedAuthorized) !== false || 
            strpos($normalizedAuthorized, $normalizedDomain) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * Verify domain access with Vimeo Pro API
 */
function verifyVimeoProDomainAccess($vimeoId, $domain, $accessToken) {
    try {
        // Get video details from Vimeo Pro API
        $videoDetails = getVimeoProVideoDetails($vimeoId, $accessToken);
        
        if (!$videoDetails) {
            return [
                'success' => false,
                'error' => 'Failed to get video details from Vimeo Pro API'
            ];
        }
        
        // Extract privacy settings
        $privacy = $videoDetails['privacy'] ?? [];
        $embedPrivacy = $privacy['embed'] ?? 'public';
        $viewPrivacy = $privacy['view'] ?? 'anybody';
        
        // Check if video is accessible
        $accessible = true;
        $embedAllowed = true;
        
        // Check embed privacy settings
        if ($embedPrivacy === 'private') {
            // Private videos require owner authentication (which we have)
            $accessible = true;
            $embedAllowed = true;
        } elseif ($embedPrivacy === 'whitelist') {
            // Whitelist videos - check if domain is in whitelist
            $embedAllowed = checkDomainWhitelist($vimeoId, $domain, $accessToken);
            $accessible = $embedAllowed;
        } elseif ($embedPrivacy === 'nowhere') {
            // Embedding disabled
            $accessible = false;
            $embedAllowed = false;
        }
        
        // Check view privacy settings
        if ($viewPrivacy === 'password') {
            // Password-protected videos - we can access with owner auth
            $accessible = true;
        } elseif ($viewPrivacy === 'contacts' || $viewPrivacy === 'users') {
            // Restricted to contacts/users - we can access with owner auth
            $accessible = true;
        } elseif ($viewPrivacy === 'disable') {
            // Video is disabled
            $accessible = false;
            $embedAllowed = false;
        }
        
        return [
            'success' => true,
            'accessible' => $accessible,
            'embed_allowed' => $embedAllowed,
            'privacy' => $privacy
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Domain verification failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Get video details from Vimeo Pro API
 */
function getVimeoProVideoDetails($vimeoId, $accessToken) {
    $url = "https://api.vimeo.com/videos/{$vimeoId}";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $accessToken,
            'Accept: application/vnd.vimeo.*+json;version=3.4',
            'User-Agent: KFT-Fitness-App/1.0 (Vimeo Pro Integration)'
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        error_log("Vimeo Pro API cURL error: " . $error);
        return false;
    }
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    error_log("Vimeo Pro API request failed. HTTP Code: $httpCode, Response: $response");
    return false;
}

/**
 * Check if domain is in Vimeo whitelist
 */
function checkDomainWhitelist($vimeoId, $domain, $accessToken) {
    try {
        // Get embed presets for the video
        $url = "https://api.vimeo.com/videos/{$vimeoId}/presets";
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $accessToken,
                'Accept: application/vnd.vimeo.*+json;version=3.4',
                'User-Agent: KFT-Fitness-App/1.0 (Vimeo Pro Integration)'
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $presets = json_decode($response, true);
            
            // Check if domain is in any preset whitelist
            if (isset($presets['data'])) {
                foreach ($presets['data'] as $preset) {
                    if (isset($preset['settings']['embed']['domains'])) {
                        $whitelistedDomains = $preset['settings']['embed']['domains'];
                        
                        foreach ($whitelistedDomains as $whitelistedDomain) {
                            if (strpos($domain, $whitelistedDomain) !== false || 
                                strpos($whitelistedDomain, $domain) !== false) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        
        // If we can't check the whitelist, assume it's allowed since we're the owner
        return true;
        
    } catch (Exception $e) {
        error_log("Failed to check domain whitelist: " . $e->getMessage());
        // If we can't check, assume it's allowed since we're the owner
        return true;
    }
}

/**
 * Log Vimeo Pro domain verification attempts
 */
function logVimeoProDomainVerification($pdo, $vimeoId, $videoId, $domain, $status, $errorMessage = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO vimeo_pro_domain_verification_log (
                vimeo_id, video_id, domain, status, error_message, 
                ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $vimeoId,
            $videoId,
            $domain,
            $status,
            $errorMessage,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to log Vimeo Pro domain verification: " . $e->getMessage());
        return false;
    }
}

/**
 * Create vimeo_pro_domain_verification_log table if it doesn't exist
 */
function createVimeoProDomainVerificationLogTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS vimeo_pro_domain_verification_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            vimeo_id VARCHAR(50) NOT NULL,
            video_id VARCHAR(50),
            domain VARCHAR(255) NOT NULL,
            status ENUM('verified', 'failed') NOT NULL,
            error_message TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_vimeo_id (vimeo_id),
            INDEX idx_domain (domain),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        return true;
    } catch (Exception $e) {
        error_log("Failed to create vimeo_pro_domain_verification_log table: " . $e->getMessage());
        return false;
    }
}

// Ensure the table exists
createVimeoProDomainVerificationLogTable($pdo);
?>
