/* Admin Table Styles */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Full-width table improvements */
#adminUsersTable {
    table-layout: fixed;
    width: 100%;
}

#adminUsersTable th {
    font-weight: 600;
    color: #495057;
    border-top: none;
    white-space: nowrap;
    padding: 0.75rem 1rem;
}

#adminUsersTable td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

/* Responsive table improvements */
@media (max-width: 767.98px) {
    .table-responsive {
        border: 0;
    }

    #adminUsersTable th,
    #adminUsersTable td {
        padding: 0.75rem;
    }

    .btn-group .btn {
        padding: 0.375rem 0.5rem;
    }
}

/* Hover effects */
#adminUsersTable tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badge styles */
#adminUsersTable .badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
}

/* Search input styling */
#adminSearchInput:focus {
    box-shadow: none;
    border-color: #80bdff;
}

/* Dropdown menu styling */
.dropdown-menu {
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
    border-radius: 0.5rem;
    min-width: 11rem;
    z-index: 9999; /* Increased z-index to ensure it's above all other elements */
    position: absolute;
    display: none;
    padding: 0.5rem;
    margin: 0.25rem 0 0;
    background-color: #fff;
    transform-origin: top right;
    animation: dropdown-fade-in 0.2s ease;
    overflow: visible; /* Changed from hidden to visible */
}

@keyframes dropdown-fade-in {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Position the dropdown menu to the left for items near the right edge */
.dropdown-menu-end {
    right: 0;
    left: auto;
}

/* Position the dropdown menu upwards for items near the bottom */
.dropdown-menu-up {
    top: auto;
    bottom: 100%;
    margin-bottom: 0.25rem;
    margin-top: 0;
    transform-origin: bottom right;
    animation: dropdown-fade-in-up 0.2s ease;
}

@keyframes dropdown-fade-in-up {
    from {
        opacity: 0;
        transform: translateY(8px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown-menu.show {
    display: block !important; /* Use !important to override any other styles */
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
    transform: none !important; /* Prevent Bootstrap's transform from affecting positioning */
}

/* Fix for Bootstrap dropdown */
.dropdown-toggle::after {
    display: none !important; /* Hide the default caret */
}

.dropdown-item {
    padding: 0.6rem 0.75rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    margin-bottom: 0.125rem;
    transition: all 0.15s ease;
}

.dropdown-item:last-child {
    margin-bottom: 0;
}

.dropdown-item i {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 0.875rem;
    border-radius: 4px;
    background-color: #eee;
    color: #111;
    transition: all 0.15s ease;
}

.dropdown-item:active {
    background-color: #eee;
    color: #111;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item:hover i {
    transform: scale(1.1);
}

/* Specific icon colors */
.dropdown-item i.text-primary, .dropdown-item i.text-success, .dropdown-item i.text-danger {
    background-color: #eee;
    color: #111;
}

.dropdown-item.text-danger {
    color: #111;
}

.dropdown-item.text-danger:hover {
    background-color: #eee;
}

/* Compact dropdown for small screens */
@media (max-width: 767.98px) {
    .dropdown-menu {
        min-width: 9rem;
        padding: 0.375rem;
    }

    .dropdown-item {
        padding: 0.5rem 0.625rem;
        font-size: 0.8125rem;
        margin-bottom: 0.0625rem;
    }

    .dropdown-item i {
        width: 18px;
        height: 18px;
        margin-right: 6px;
        font-size: 0.75rem;
    }
}

/* Action button styling */
.action-btn {
    width: 34px;
    height: 34px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: transparent;
    border: none;
    color: #111;
    transition: all 0.15s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: currentColor;
    opacity: 0;
    border-radius: inherit;
    transition: opacity 0.15s ease;
    z-index: -1;
}

.action-btn:hover {
    color: #000;
}

.action-btn:hover::before {
    opacity: 0.1;
}

.action-btn:focus {
    box-shadow: 0 0 0 2px #ccc;
    outline: none;
}

.action-btn:active::before {
    opacity: 0.15;
}

.action-btn i {
    font-size: 16px;
    transition: transform 0.2s ease;
}

.action-btn:hover i {
    transform: scale(1.1);
}

/* Hide the dropdown toggle caret */
.action-btn.dropdown-toggle::after {
    display: none !important;
}

/* Action button variants */
.action-btn.action-btn-primary {
    color: #0d6efd;
}

.action-btn.action-btn-success {
    color: #198754;
}

.action-btn.action-btn-danger {
    color: #dc3545;
}

.action-btn.action-btn-info {
    color: #0dcaf0;
}

/* Ensure dropdown is visible */
.dropdown {
    position: relative;
    z-index: 1000; /* Add z-index to the dropdown container */
}

/* Actions cell styling */
.actions-cell {
    position: relative;
    width: 40px;
    min-width: 40px;
    z-index: 1000; /* Add z-index to the cell */
}

/* Fix for dropdown in table */
.table .dropdown {
    position: static;
}

.table .dropdown-menu {
    position: absolute;
    inset: auto !important; /* Override Bootstrap's inset property */
}

/* Fix for dropdown positioning */
.actions-cell .dropdown-menu {
    position: absolute; /* Use absolute positioning for better compatibility with Bootstrap */
    transform: none !important; /* Prevent any transforms that might affect positioning */
    margin: 0; /* Remove default margin */
    padding: 0.5rem; /* Add padding */
    border: none; /* Remove border */
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15); /* Add shadow */
    border-radius: 0.5rem; /* Add border radius */
}

/* Ensure dropdown items are clickable */
.dropdown-item {
    cursor: pointer;
}

/* Fix for dropdown form buttons */
.dropdown-menu form {
    margin: 0;
}

.dropdown-menu button.dropdown-item {
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    padding: 0.6rem 0.75rem;
}

/* Dropdown divider */
.dropdown-divider {
    height: 1px;
    margin: 0.5rem 0;
    overflow: hidden;
    background-color: #f1f3f5;
    border: 0;
    opacity: 1;
}

/* Dropdown header */
.dropdown-header {
    display: block;
    padding: 0.4rem 0.75rem;
    margin-bottom: 0.125rem;
    font-size: 0.75rem;
    color: #6c757d;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    font-weight: 600;
}

/* Card footer styling */
.card-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
}

/* Empty state styling */
.table td.text-center.py-4 {
    background-color: #f8f9fa;
}

/* Full-width table enhancements */
.card-body {
    padding: 0 !important;
}

.table-responsive {
    margin-bottom: 0;
    border-radius: 0.25rem;
    overflow: visible !important; /* Ensure dropdowns are visible outside the table */
}

/* Fix for table overflow issues */
.card-body {
    overflow: visible !important;
}

.table {
    overflow: visible !important;
}

table, tr, td, th {
    overflow: visible !important;
}

#adminUsersTable thead {
    position: sticky;
    top: 0;
    z-index: 1;
}

#adminUsersTable tbody tr:last-child td {
    border-bottom: none;
}

/* Improve text overflow handling */
#adminUsersTable td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Improve email column display */
#adminUsersTable td.d-none.d-lg-table-cell {
    max-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* User info styling */
.user-info {
    min-width: 0;
    flex: 1;
}

.user-info .fw-bold {
    font-size: 14px;
    line-height: 1.4;
}

.user-info .small {
    font-size: 12px;
    line-height: 1.4;
}

/* Refresh button animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fa-sync-alt.spinning {
    animation: spin 1s linear infinite;
}
