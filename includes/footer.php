        </div> <!-- End of Page Content Container -->

        <!-- Footer -->
        <footer class="footer mt-auto py-3 bg-white border-top">
            <div class="container-fluid px-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.
                    </div>
                    <div class="text-muted">
                        Version 1.0
                    </div>
                </div>
            </div>
        </footer>

    </div> <!-- End of Admin Content -->
</div> <!-- End of Admin Layout -->

    <!-- Bootstrap JS Bundle with <PERSON>per -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/scripts.js"></script>

    <!-- Responsive Admin Panel JS -->
    <script src="assets/js/responsive-admin-panel.js"></script>

    <!-- Real-time Updates -->
    <script src="assets/js/realtime-updates.js"></script>

    <?php
    // Page-specific JavaScript files
    $currentPage = basename($_SERVER['PHP_SELF']);
    if ($currentPage === 'user_add.php'):
    ?>
    <script src="assets/js/user-add.js"></script>
    <?php endif; ?>
<?php
if (function_exists('ob_end_flush')) ob_end_flush();
?>
</body>
</html>
