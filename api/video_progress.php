<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

// Verify JWT token and get user ID
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? '';

if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Authorization token required'
    ]);
    exit;
}

$token = substr($authHeader, 7);
$userId = verifyJWTToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid or expired token'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $videoId = $input['video_id'] ?? null;
    $watchDurationSeconds = $input['watch_duration_seconds'] ?? null;
    $lastPositionSeconds = $input['last_position_seconds'] ?? null;
    $isCompleted = $input['completed'] ?? $input['is_completed'] ?? null;

    if (!$videoId) {
        throw new Exception('Missing required parameter: video_id');
    }

    // Update or insert video progress
    $result = updateVideoProgress($pdo, $userId, $videoId, $watchDurationSeconds, $lastPositionSeconds, $isCompleted);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Video progress updated successfully',
            'data' => [
                'video_id' => $videoId,
                'user_id' => $userId,
                'watch_duration_seconds' => $watchDurationSeconds,
                'last_position_seconds' => $lastPositionSeconds,
                'is_completed' => $isCompleted
            ]
        ]);
    } else {
        throw new Exception('Failed to update video progress');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function updateVideoProgress($pdo, $userId, $videoId, $watchDurationSeconds, $lastPositionSeconds, $isCompleted) {
    try {
        // Check if progress record exists
        $stmt = $pdo->prepare("
            SELECT id FROM user_video_progress
            WHERE user_id = ? AND video_id = ?
        ");
        $stmt->execute([$userId, $videoId]);
        $existingProgress = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingProgress) {
            // Update existing progress
            $updateFields = [];
            $updateValues = [];

            if ($watchDurationSeconds !== null) {
                $updateFields[] = "watch_duration_seconds = ?";
                $updateValues[] = $watchDurationSeconds;
            }

            if ($lastPositionSeconds !== null) {
                $updateFields[] = "last_position_seconds = ?";
                $updateValues[] = $lastPositionSeconds;
            }

            if ($isCompleted !== null) {
                $updateFields[] = "is_completed = ?";
                $updateValues[] = $isCompleted ? 1 : 0;

                if ($isCompleted) {
                    $updateFields[] = "completion_date = NOW()";
                }
            }

            if (!empty($updateFields)) {
                $updateValues[] = $userId;
                $updateValues[] = $videoId;

                $sql = "UPDATE user_video_progress SET " . implode(", ", $updateFields) . " WHERE user_id = ? AND video_id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($updateValues);
            }
        } else {
            // Insert new progress record
            $stmt = $pdo->prepare("
                INSERT INTO user_video_progress
                (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, completion_date)
                VALUES (?, ?, ?, ?, ?, 1, NOW(), ?)
            ");

            $completionDate = $isCompleted ? date('Y-m-d H:i:s') : null;
            $stmt->execute([
                $userId,
                $videoId,
                $watchDurationSeconds ?? 0,
                $lastPositionSeconds ?? 0,
                $isCompleted ? 1 : 0,
                $completionDate
            ]);
        }

        // Log activity for analytics
        logVideoActivity($pdo, $userId, $videoId, 'progress_update', [
            'watch_duration_seconds' => $watchDurationSeconds,
            'last_position_seconds' => $lastPositionSeconds,
            'is_completed' => $isCompleted
        ]);

        return true;

    } catch (Exception $e) {
        error_log("Video progress update error: " . $e->getMessage());
        return false;
    }
}

function logVideoActivity($pdo, $userId, $videoId, $action, $details) {
    try {
        // Log to user_activity_log with video_progress type
        $stmt = $pdo->prepare("
            INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at)
            VALUES (?, 'video_progress', ?, ?, NOW())
        ");

        $stmt->execute([
            $userId,
            $videoId,
            json_encode([
                'action' => $action,
                'watch_duration' => $details['watch_duration_seconds'] ?? 0,
                'last_position' => $details['last_position_seconds'] ?? 0,
                'is_completed' => $details['is_completed'] ?? false,
                'timestamp' => date('Y-m-d H:i:s')
            ])
        ]);

        // Also log to video_access_logs
        $stmt = $pdo->prepare("
            INSERT INTO video_access_logs 
            (video_id, user_id, action, timestamp, app_domain, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, NOW(), ?, ?, ?, NOW())
        ");

        $stmt->execute([
            $videoId,
            $userId,
            $action,
            $_SERVER['HTTP_ORIGIN'] ?? 'app',
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);

    } catch (Exception $e) {
        error_log("Failed to log video activity: " . $e->getMessage());
    }
}

function verifyJWTToken($token) {
    try {
        // Simple JWT verification - in production, use a proper JWT library
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }

        $payload = json_decode(base64_decode($parts[1]), true);

        if (!$payload || !isset($payload['user_id']) || !isset($payload['exp'])) {
            return false;
        }

        // Check if token is expired
        if (time() > $payload['exp']) {
            return false;
        }

        return $payload['user_id'];

    } catch (Exception $e) {
        error_log("JWT verification error: " . $e->getMessage());
        return false;
    }
}

// Create user_video_progress table if it doesn't exist
function createUserVideoProgressTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS user_video_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            video_id INT NOT NULL,
            watch_duration_seconds INT DEFAULT 0,
            last_position_seconds INT DEFAULT 0,
            is_completed BOOLEAN DEFAULT FALSE,
            is_unlocked BOOLEAN DEFAULT TRUE,
            unlock_date DATE NULL,
            completion_date DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_video (user_id, video_id),
            INDEX idx_user_id (user_id),
            INDEX idx_video_id (video_id),
            INDEX idx_completed (is_completed)
        )";

        $pdo->exec($sql);

    } catch (Exception $e) {
        error_log("Failed to create user_video_progress table: " . $e->getMessage());
    }
}

// Create user_activity_log table if it doesn't exist
function createUserActivityLogTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS user_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            activity_type VARCHAR(50) NOT NULL,
            related_id INT NULL,
            details JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_activity_type (activity_type),
            INDEX idx_created_at (created_at)
        )";

        $pdo->exec($sql);

    } catch (Exception $e) {
        error_log("Failed to create user_activity_log table: " . $e->getMessage());
    }
}

// Initialize tables
createUserVideoProgressTable($pdo);
createUserActivityLogTable($pdo);
?>
