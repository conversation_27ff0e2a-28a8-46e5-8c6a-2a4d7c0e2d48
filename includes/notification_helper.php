<?php
/**
 * Notification Helper
 *
 * This class provides helper functions for managing notifications.
 */

class NotificationHelper {
    private $conn;
    
    /**
     * Constructor
     *
     * @param mysqli $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * Get notifications
     *
     * @param array $filters Filters for notifications
     * @param int $limit Limit for pagination
     * @param int $offset Offset for pagination
     * @return array Notifications and total count
     */
    public function getNotifications($filters = [], $limit = 10, $offset = 0) {
        // Build the query
        $query = "SELECT * FROM notifications WHERE 1=1";
        $countQuery = "SELECT COUNT(*) as total FROM notifications WHERE 1=1";
        $params = [];
        $types = "";
        
        // Add filters
        if (isset($filters['type']) && !empty($filters['type'])) {
            $query .= " AND type = ?";
            $countQuery .= " AND type = ?";
            $params[] = $filters['type'];
            $types .= "s";
        }
        
        if (isset($filters['is_read']) && $filters['is_read'] !== null) {
            $query .= " AND is_read = ?";
            $countQuery .= " AND is_read = ?";
            $params[] = $filters['is_read'];
            $types .= "i";
        }
        
        if (isset($filters['recipient_id']) && !empty($filters['recipient_id'])) {
            $query .= " AND (recipient_id = ? OR recipient_id IS NULL)";
            $countQuery .= " AND (recipient_id = ? OR recipient_id IS NULL)";
            $params[] = $filters['recipient_id'];
            $types .= "i";
        }
        
        if (isset($filters['recipient_type']) && !empty($filters['recipient_type'])) {
            $query .= " AND recipient_type = ?";
            $countQuery .= " AND recipient_type = ?";
            $params[] = $filters['recipient_type'];
            $types .= "s";
        }
        
        // Add sorting
        $query .= " ORDER BY created_at DESC";
        
        // Add pagination
        $query .= " LIMIT ? OFFSET ?";
        $limitParams = $params;
        $limitParams[] = $limit;
        $limitParams[] = $offset;
        $limitTypes = $types . "ii";
        
        // Prepare and execute the count query
        $countStmt = $this->conn->prepare($countQuery);
        if (!empty($params)) {
            $countStmt->bind_param($types, ...$params);
        }
        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $totalCount = $countResult->fetch_assoc()['total'];
        
        // Prepare and execute the main query
        $stmt = $this->conn->prepare($query);
        if (!empty($limitParams)) {
            $stmt->bind_param($limitTypes, ...$limitParams);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        // Fetch all notifications
        $notifications = [];
        while ($row = $result->fetch_assoc()) {
            $notifications[] = $row;
        }
        
        return [
            'notifications' => $notifications,
            'total' => $totalCount
        ];
    }
    
    /**
     * Mark a notification as read
     *
     * @param int $id Notification ID
     * @return bool Success status
     */
    public function markAsRead($id) {
        $query = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $id);
        return $stmt->execute();
    }
    
    /**
     * Mark all notifications as read
     *
     * @param array $filters Filters for notifications to mark as read
     * @return bool Success status
     */
    public function markAllAsRead($filters = []) {
        $query = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE is_read = 0";
        $params = [];
        $types = "";
        
        // Add filters
        if (isset($filters['recipient_id']) && !empty($filters['recipient_id'])) {
            $query .= " AND (recipient_id = ? OR recipient_id IS NULL)";
            $params[] = $filters['recipient_id'];
            $types .= "i";
        }
        
        if (isset($filters['recipient_type']) && !empty($filters['recipient_type'])) {
            $query .= " AND recipient_type = ?";
            $params[] = $filters['recipient_type'];
            $types .= "s";
        }
        
        $stmt = $this->conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        return $stmt->execute();
    }
    
    /**
     * Create a new notification
     *
     * @param array $data Notification data
     * @return int|bool Notification ID or false on failure
     */
    public function createNotification($data) {
        if (!isset($data['type']) || !isset($data['message'])) {
            return false;
        }
        
        $query = "INSERT INTO notifications (type, message, target_id, target_type, recipient_id, recipient_type) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $this->conn->prepare($query);
        
        $type = $data['type'];
        $message = $data['message'];
        $targetId = isset($data['target_id']) ? $data['target_id'] : null;
        $targetType = isset($data['target_type']) ? $data['target_type'] : null;
        $recipientId = isset($data['recipient_id']) ? $data['recipient_id'] : null;
        $recipientType = isset($data['recipient_type']) ? $data['recipient_type'] : 'admin';
        
        $stmt->bind_param("ssisss", $type, $message, $targetId, $targetType, $recipientId, $recipientType);
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * Get notification statistics
     *
     * @return array Notification statistics
     */
    public function getStatistics() {
        // Get total notifications
        $totalQuery = "SELECT COUNT(*) as total FROM notifications";
        $totalResult = $this->conn->query($totalQuery);
        $total = $totalResult->fetch_assoc()['total'];
        
        // Get unread notifications
        $unreadQuery = "SELECT COUNT(*) as unread FROM notifications WHERE is_read = 0";
        $unreadResult = $this->conn->query($unreadQuery);
        $unread = $unreadResult->fetch_assoc()['unread'];
        
        // Get today's notifications
        $todayQuery = "SELECT COUNT(*) as today FROM notifications WHERE DATE(created_at) = CURDATE()";
        $todayResult = $this->conn->query($todayQuery);
        $today = $todayResult->fetch_assoc()['today'];
        
        // Get this week's notifications
        $weekQuery = "SELECT COUNT(*) as week FROM notifications WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        $weekResult = $this->conn->query($weekQuery);
        $week = $weekResult->fetch_assoc()['week'];
        
        // Get this month's notifications
        $monthQuery = "SELECT COUNT(*) as month FROM notifications WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $monthResult = $this->conn->query($monthQuery);
        $month = $monthResult->fetch_assoc()['month'];
        
        // Get daily counts for the past week
        $dailyQuery = "SELECT DATE(created_at) as date, COUNT(*) as count 
                      FROM notifications 
                      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                      GROUP BY DATE(created_at) 
                      ORDER BY date";
        $dailyResult = $this->conn->query($dailyQuery);
        
        $dailyCounts = [];
        while ($row = $dailyResult->fetch_assoc()) {
            $dailyCounts[$row['date']] = $row['count'];
        }
        
        // Fill in missing days
        $chartData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $count = isset($dailyCounts[$date]) ? $dailyCounts[$date] : 0;
            $chartData[] = [
                'date' => date('D', strtotime($date)),
                'count' => $count
            ];
        }
        
        return [
            'total' => $total,
            'unread' => $unread,
            'today' => $today,
            'week' => $week,
            'month' => $month,
            'chart_data' => $chartData
        ];
    }
}
