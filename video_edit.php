<?php
require_once 'includes/header.php';
require_once 'includes/video_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('error', 'Invalid video ID.');
    Utilities::redirect('courses.php');
}

$videoId = $_GET['id'];

// Fetch video data
$videoQuery = "SELECT v.*, c.title as course_title, c.duration_weeks
               FROM course_videos v
               JOIN courses c ON v.course_id = c.id
               WHERE v.id = ?";
$videoStmt = $conn->prepare($videoQuery);
$videoStmt->bind_param("i", $videoId);
$videoStmt->execute();
$videoResult = $videoStmt->get_result();

if ($videoResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Video not found.');
    Utilities::redirect('courses.php');
}

$video = $videoResult->fetch_assoc();
$courseId = $video['course_id'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $title = Utilities::sanitizeInput($_POST['title'] ?? '');
    $description = Utilities::sanitizeInput($_POST['description'] ?? '');
    $videoUrl = Utilities::sanitizeInput($_POST['video_url'] ?? '');
    $thumbnailUrl = Utilities::sanitizeInput($_POST['thumbnail_url'] ?? '');
    $durationMinutes = intval($_POST['duration_minutes'] ?? 0);
    $weekNumber = intval($_POST['week_number'] ?? 1);
    $sequenceNumber = intval($_POST['sequence_number'] ?? 1);
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate inputs
    $errors = [];

    if (empty($title)) {
        $errors[] = 'Video title is required.';
    }

    if (empty($videoUrl)) {
        $errors[] = 'Video URL is required.';
    }

    if ($weekNumber < 1 || $weekNumber > $video['duration_weeks']) {
        $errors[] = 'Week number must be between 1 and ' . $video['duration_weeks'] . '.';
    }

    if ($sequenceNumber < 1) {
        $errors[] = 'Sequence number must be at least 1.';
    }

    // Check if the week and sequence combination already exists (excluding this video)
    $checkQuery = "SELECT id FROM course_videos
                  WHERE course_id = ? AND week_number = ? AND sequence_number = ? AND id != ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("iiii", $courseId, $weekNumber, $sequenceNumber, $videoId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows > 0) {
        $errors[] = 'A video with this week and sequence number already exists.';
    }

    // If no errors, update the video
    if (empty($errors)) {
        $updateQuery = "UPDATE course_videos
                        SET title = ?, description = ?, video_url = ?, thumbnail_url = ?,
                            duration_minutes = ?, week_number = ?, sequence_number = ?, is_active = ?
                        WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ssssiiiis", $title, $description, $videoUrl, $thumbnailUrl,
                         $durationMinutes, $weekNumber, $sequenceNumber, $isActive, $videoId);

        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Video updated successfully.');
            Utilities::redirect('course_videos.php?course_id=' . $courseId);
        } else {
            $errors[] = 'Failed to update video: ' . $conn->error;
        }
    }
} else {
    // Set variables for form
    $title = $video['title'];
    $description = $video['description'];
    $videoUrl = $video['video_url'];
    $thumbnailUrl = $video['thumbnail_url'];
    $durationMinutes = $video['duration_minutes'];
    $weekNumber = $video['week_number'];
    $sequenceNumber = $video['sequence_number'];
    $isActive = $video['is_active'];
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Video</h1>
    <a href="course_videos.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Videos
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">Course: <?php echo htmlspecialchars($video['course_title']); ?></h5>
    </div>
    <div class="card-body">
        <form method="post">
            <div class="mb-3">
                <label for="title" class="form-label">Video Title</label>
                <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($title); ?>" required>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
            </div>

            <div class="mb-3">
                <label for="video_url" class="form-label">Video URL (Vimeo Share Link)</label>
                <input type="url" class="form-control" id="video_url" name="video_url"
                       value="<?php echo htmlspecialchars($videoUrl); ?>"
                       placeholder="https://vimeo.com/123456789" required>
                <div class="form-text">Paste a Vimeo share link (e.g., https://vimeo.com/123456789)</div>
            </div>

            <div id="vimeoPreview" class="mb-3 d-none">
                <label class="form-label">Video Preview</label>
                <div class="ratio ratio-16x9 mb-2">
                    <iframe id="vimeoFrame" src="" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                </div>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> Valid Vimeo video detected
                </div>
            </div>

            <div id="vimeoError" class="mb-3 d-none">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="vimeoErrorMessage">Invalid Vimeo URL. Please enter a valid Vimeo share link.</span>
                </div>
            </div>

            <div class="mb-3">
                <label for="thumbnail_url" class="form-label">Thumbnail URL (Optional)</label>
                <input type="url" class="form-control" id="thumbnail_url" name="thumbnail_url"
                       value="<?php echo htmlspecialchars($thumbnailUrl); ?>"
                       placeholder="Leave empty to use Vimeo's thumbnail">
                <div class="form-text">Leave empty to use the Vimeo video's thumbnail</div>
            </div>

            <div class="mb-3">
                <label for="duration_minutes" class="form-label">Duration (Minutes)</label>
                <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="0" value="<?php echo $durationMinutes; ?>">
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="week_number" class="form-label">Week Number</label>
                    <input type="number" class="form-control" id="week_number" name="week_number" min="1" max="<?php echo $video['duration_weeks']; ?>" value="<?php echo $weekNumber; ?>" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="sequence_number" class="form-label">Sequence Number</label>
                    <input type="number" class="form-control" id="sequence_number" name="sequence_number" min="1" value="<?php echo $sequenceNumber; ?>" required>
                </div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $isActive ? 'checked' : ''; ?>>
                <label class="form-check-label" for="is_active">Active</label>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Update Video
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const videoUrlInput = document.getElementById('video_url');
    const vimeoPreview = document.getElementById('vimeoPreview');
    const vimeoFrame = document.getElementById('vimeoFrame');
    const vimeoError = document.getElementById('vimeoError');
    const vimeoErrorMessage = document.getElementById('vimeoErrorMessage');

    // Function to extract Vimeo ID from URL
    function extractVimeoId(url) {
        if (!url) return null;

        const patterns = [
            /vimeo\.com\/([0-9]+)/,
            /player\.vimeo\.com\/video\/([0-9]+)/,
            /vimeo\.com\/([0-9]+)\/[a-zA-Z0-9]+/,
            /player\.vimeo\.com\/video\/([0-9]+)\?h=[a-zA-Z0-9]+/
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }

        return null;
    }

    // Function to validate and preview Vimeo URL
    function validateVimeoUrl() {
        const url = videoUrlInput.value.trim();
        const videoId = extractVimeoId(url);

        // Hide both preview and error initially
        vimeoPreview.classList.add('d-none');
        vimeoError.classList.add('d-none');

        if (!url) {
            return; // Empty input, do nothing
        }

        if (videoId) {
            // Valid Vimeo URL
            const embedUrl = `https://player.vimeo.com/video/${videoId}`;
            vimeoFrame.src = embedUrl;
            vimeoPreview.classList.remove('d-none');
        } else {
            // Invalid URL
            vimeoErrorMessage.textContent = 'Invalid Vimeo URL. Please enter a valid Vimeo share link.';
            vimeoError.classList.remove('d-none');
        }
    }

    // Add event listener for input changes
    videoUrlInput.addEventListener('input', validateVimeoUrl);
    videoUrlInput.addEventListener('paste', function() {
        // Short delay to allow paste to complete
        setTimeout(validateVimeoUrl, 100);
    });

    // Initial validation if URL is already present
    validateVimeoUrl();
});
</script>
