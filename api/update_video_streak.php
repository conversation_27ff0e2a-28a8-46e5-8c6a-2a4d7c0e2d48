<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Set content type header
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if data is valid JSON
if (!$data) {
    returnError('Invalid JSON data');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user is authenticated
$auth = new Auth($conn);
$userId = $auth->authenticateToken();

if (!$userId) {
    returnError('Unauthorized access', 401);
}

// Get streak data from request
$currentStreak = isset($data['current_streak']) ? intval($data['current_streak']) : 0;
$highestStreak = isset($data['highest_streak']) ? intval($data['highest_streak']) : 0;
$lastActivityDate = isset($data['last_activity_date']) ? $data['last_activity_date'] : null;

try {
    // Check if user already has a video streak record
    $checkQuery = "SELECT id, current_streak, highest_streak FROM user_video_streaks WHERE user_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("i", $userId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing record
        $existingData = $result->fetch_assoc();
        
        // Ensure highest streak is never decreased
        $finalHighestStreak = max($highestStreak, $existingData['highest_streak']);
        
        $updateQuery = "UPDATE user_video_streaks 
                       SET current_streak = ?, 
                           highest_streak = ?, 
                           last_activity_date = ?,
                           updated_at = NOW()
                       WHERE user_id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("iisi", $currentStreak, $finalHighestStreak, $lastActivityDate, $userId);
        
        if (!$updateStmt->execute()) {
            returnError('Failed to update video streak: ' . $conn->error);
        }
        
        $streakId = $existingData['id'];
    } else {
        // Insert new record
        $insertQuery = "INSERT INTO user_video_streaks 
                       (user_id, current_streak, highest_streak, last_activity_date, created_at, updated_at)
                       VALUES (?, ?, ?, ?, NOW(), NOW())";
        $insertStmt = $conn->prepare($insertQuery);
        $insertStmt->bind_param("iiis", $userId, $currentStreak, $highestStreak, $lastActivityDate);
        
        if (!$insertStmt->execute()) {
            returnError('Failed to create video streak: ' . $conn->error);
        }
        
        $streakId = $conn->insert_id;
        $finalHighestStreak = $highestStreak;
    }

    // Log the streak activity
    $activityQuery = "INSERT INTO user_activity_log 
                     (user_id, activity_type, related_id, details, created_at)
                     VALUES (?, 'video_streak_update', ?, ?, NOW())";
    $activityDetails = json_encode([
        'current_streak' => $currentStreak,
        'highest_streak' => $finalHighestStreak,
        'last_activity_date' => $lastActivityDate
    ]);
    $activityStmt = $conn->prepare($activityQuery);
    $activityStmt->bind_param("iis", $userId, $streakId, $activityDetails);
    $activityStmt->execute();

    // Return success response
    returnResponse([
        'success' => true,
        'message' => 'Video streak updated successfully',
        'data' => [
            'user_id' => $userId,
            'current_streak' => $currentStreak,
            'highest_streak' => $finalHighestStreak,
            'last_activity_date' => $lastActivityDate
        ]
    ]);

} catch (Exception $e) {
    returnError('Database error: ' . $e->getMessage());
}
?>
