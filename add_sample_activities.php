<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in and is admin
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    die('Access denied. Admin access required.');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

echo "<h1>Add Sample Video Activities</h1>";

if (isset($_POST['add_samples'])) {
    $userId = intval($_POST['user_id']);
    $courseId = intval($_POST['course_id']);
    
    if ($userId > 0 && $courseId > 0) {
        // Get videos for this course
        $videosQuery = "SELECT id, title FROM course_videos WHERE course_id = ? ORDER BY week_number, sequence_number LIMIT 5";
        $videosStmt = $conn->prepare($videosQuery);
        $videosStmt->bind_param("i", $courseId);
        $videosStmt->execute();
        $videosResult = $videosStmt->get_result();
        
        $activitiesAdded = 0;
        
        while ($video = $videosResult->fetch_assoc()) {
            // Add multiple activities for each video to simulate real usage
            $activities = [
                [
                    'action' => 'play',
                    'watch_duration' => 30,
                    'last_position' => 30,
                    'is_completed' => false
                ],
                [
                    'action' => 'progress_update',
                    'watch_duration' => 120,
                    'last_position' => 120,
                    'is_completed' => false
                ],
                [
                    'action' => 'progress_update',
                    'watch_duration' => 300,
                    'last_position' => 300,
                    'is_completed' => false
                ],
                [
                    'action' => 'complete',
                    'watch_duration' => 450,
                    'last_position' => 450,
                    'is_completed' => true
                ]
            ];
            
            foreach ($activities as $index => $activity) {
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at)
                        VALUES (?, 'video_progress', ?, ?, DATE_SUB(NOW(), INTERVAL ? HOUR))
                    ");
                    
                    $details = json_encode([
                        'action' => $activity['action'],
                        'watch_duration' => $activity['watch_duration'],
                        'last_position' => $activity['last_position'],
                        'is_completed' => $activity['is_completed'],
                        'timestamp' => date('Y-m-d H:i:s'),
                        'sample_data' => true
                    ]);
                    
                    // Spread activities over the last few hours
                    $hoursAgo = ($index + 1) * 2;
                    
                    $stmt->bind_param("iisi", $userId, $video['id'], $details, $hoursAgo);
                    
                    if ($stmt->execute()) {
                        $activitiesAdded++;
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>Error adding activity for video " . htmlspecialchars($video['title']) . ": " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<p style='color: green;'>✓ Added $activitiesAdded sample activities successfully!</p>";
        echo "<p><a href='video_analytics.php?user_id=$userId&course_id=$courseId'>View Analytics for this user/course</a></p>";
    } else {
        echo "<p style='color: red;'>Please select valid user and course.</p>";
    }
}

// Get users and courses for the form
$usersQuery = "SELECT id, name FROM users ORDER BY name LIMIT 10";
$usersResult = $conn->query($usersQuery);

$coursesQuery = "SELECT id, title FROM courses ORDER BY title LIMIT 10";
$coursesResult = $conn->query($coursesQuery);
?>

<form method="post">
    <h3>Add Sample Activities</h3>
    <p>This will add sample video progress activities for testing the analytics.</p>
    
    <div style="margin-bottom: 15px;">
        <label for="user_id">Select User:</label><br>
        <select name="user_id" id="user_id" required style="width: 300px; padding: 5px;">
            <option value="">-- Select User --</option>
            <?php if ($usersResult): ?>
                <?php while ($user = $usersResult->fetch_assoc()): ?>
                    <option value="<?php echo $user['id']; ?>">
                        <?php echo htmlspecialchars($user['name']); ?> (ID: <?php echo $user['id']; ?>)
                    </option>
                <?php endwhile; ?>
            <?php endif; ?>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="course_id">Select Course:</label><br>
        <select name="course_id" id="course_id" required style="width: 300px; padding: 5px;">
            <option value="">-- Select Course --</option>
            <?php if ($coursesResult): ?>
                <?php while ($course = $coursesResult->fetch_assoc()): ?>
                    <option value="<?php echo $course['id']; ?>">
                        <?php echo htmlspecialchars($course['title']); ?> (ID: <?php echo $course['id']; ?>)
                    </option>
                <?php endwhile; ?>
            <?php endif; ?>
        </select>
    </div>
    
    <button type="submit" name="add_samples" style="padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;">
        Add Sample Activities
    </button>
</form>

<hr>
<p><a href="debug_activity_log.php">← Back to Debug Page</a></p>
<p><a href="video_analytics.php">← Back to Video Analytics</a></p>
