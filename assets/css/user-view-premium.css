/* Premium User View CSS - Pixel Perfect Design */

:root {
  --premium-primary: #4e73df;
  --premium-primary-rgb: 78, 115, 223;
  --premium-primary-light: #bac8f3;
  --premium-primary-dark: #224abe;
  --premium-secondary: #6c757d;
  --premium-success: #1cc88a;
  --premium-success-rgb: 28, 200, 138;
  --premium-info: #36b9cc;
  --premium-warning: #f6c23e;
  --premium-danger: #e74a3b;
  --premium-light: #f8f9fc;
  --premium-dark: #5a5c69;
  --premium-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  --premium-shadow-sm: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.1);
  --premium-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --premium-border-radius: 0.5rem;
  --premium-transition: all 0.2s ease-in-out;
}

/* Minimalistic Profile Header Styles */
.profile-header-premium {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.profile-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.profile-avatar-premium {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-img-premium {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-initials-premium {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 600;
    color: #fff;
    border-radius: 50%;
}

.profile-info-premium {
    margin-left: 1rem;
}

.profile-name-premium {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.profile-meta-premium {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.profile-username-premium {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.profile-status-premium {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.profile-status-premium.active {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.profile-status-premium.inactive {
    background-color: #ffebee;
    color: #c62828;
}

.profile-actions-premium {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.btn-premium {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn-premium i {
    font-size: 1rem;
}

.btn-premium.btn-primary {
    background-color: #2196f3;
    border-color: #2196f3;
}

.btn-premium.btn-primary:hover {
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-premium.btn-success {
    background-color: #4caf50;
    border-color: #4caf50;
}

.btn-premium.btn-success:hover {
    background-color: #388e3c;
    border-color: #388e3c;
}

.btn-premium.btn-danger {
    background-color: #f44336;
    border-color: #f44336;
}

.btn-premium.btn-danger:hover {
    background-color: #d32f2f;
    border-color: #d32f2f;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .profile-header-premium {
        padding: 1rem;
    }
    
    .profile-avatar-premium {
        width: 60px;
        height: 60px;
    }
    
    .profile-name-premium {
        font-size: 1.25rem;
    }
    
    .profile-actions-premium {
        flex-wrap: wrap;
    }
    
    .btn-premium {
        flex: 1;
        justify-content: center;
    }
}

/* Premium Tabs */
.profile-tabs-premium {
  margin-bottom: 1.5rem;
}

.nav-tabs-premium {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0 1rem;
}

.nav-link-premium {
  padding: 1rem 1.25rem;
  color: var(--premium-secondary);
  font-weight: 500;
  border: none;
  background: transparent;
  position: relative;
  transition: var(--premium-transition);
  cursor: pointer;
}

.nav-link-premium:hover {
  color: var(--premium-primary);
}

.nav-link-premium.active {
  color: var(--premium-primary);
  font-weight: 600;
}

.nav-link-premium.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--premium-primary);
  border-radius: 3px 3px 0 0;
}

.badge-count {
  background: var(--premium-primary);
  color: #fff;
  font-size: 0.7rem;
  font-weight: 600;
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 0.5rem;
}

.badge-count-premium {
  background: var(--premium-primary);
  color: #fff;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 0 0.5rem;
}

.badge-count-premium.success {
  background: var(--premium-success);
}

.badge-count-premium.danger {
  background: var(--premium-danger);
}

/* Premium Card */
.premium-card {
  border-radius: var(--premium-border-radius);
  box-shadow: var(--premium-shadow);
  transition: var(--premium-transition);
  overflow: hidden;
  border: none;
}

.premium-card:hover {
  box-shadow: var(--premium-shadow-lg);
  transform: translateY(-2px);
}

.card-header-premium {
  background: var(--premium-light);
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

.card-body-premium {
  padding: 1.5rem;
  background-color: #fff;
}

.card-footer-premium {
  background: var(--premium-light);
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Premium Buttons */
.btn-premium {
  border-radius: 50px;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  transition: var(--premium-transition);
  box-shadow: var(--premium-shadow-sm);
}

.btn-premium:hover {
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow);
}

.btn-premium-sm {
  border-radius: 50px;
  padding: 0.375rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--premium-transition);
  box-shadow: var(--premium-shadow-sm);
}

.btn-premium-sm:hover {
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow);
}

/* Premium Icons */
.icon-circle-premium {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

/* Premium User Profile Card */
.user-profile-card-premium {
  border-radius: var(--premium-border-radius);
  overflow: hidden;
  box-shadow: var(--premium-shadow);
}

.profile-header-premium-card {
  background: linear-gradient(135deg, var(--premium-primary) 0%, var(--premium-primary-dark) 100%);
  color: #fff;
  padding: 2rem 1.5rem;
}

.user-avatar-premium-card {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
  color: #fff;
}

.status-indicator-premium {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid #fff;
}

.status-indicator-premium.active {
  background-color: var(--premium-success);
}

.status-indicator-premium.inactive {
  background-color: var(--premium-danger);
}

.badge-premium-card {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.profile-details-premium {
  padding: 1.5rem;
}

.user-info-item-premium {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
}

.user-info-item-premium:last-child {
  margin-bottom: 0;
}

.user-info-icon-premium {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--premium-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--premium-primary);
  margin-right: 1rem;
  flex-shrink: 0;
}

.user-info-label-premium {
  font-size: 0.8rem;
  color: var(--premium-secondary);
  margin-bottom: 0.25rem;
}

.user-info-value-premium {
  font-weight: 500;
}

/* Premium Avatar Circle */
.avatar-circle-premium {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* Premium Form Elements */
.form-label-premium {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--premium-dark);
  margin-bottom: 0.5rem;
}

.form-select-premium {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--premium-dark);
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235a5c69' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #d1d3e2;
  border-radius: 0.35rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}

.form-select-premium:focus {
  border-color: var(--premium-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* Premium Alert */
.alert-premium {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: var(--premium-border-radius);
  margin-bottom: 1rem;
}

.alert-premium.info {
  background-color: rgba(54, 185, 204, 0.1);
  color: var(--premium-info);
}

.alert-premium.success {
  background-color: rgba(28, 200, 138, 0.1);
  color: var(--premium-success);
}

.alert-premium.warning {
  background-color: rgba(246, 194, 62, 0.1);
  color: var(--premium-warning);
}

.alert-premium.danger {
  background-color: rgba(231, 74, 59, 0.1);
  color: var(--premium-danger);
}

/* Premium Physical Stats */
.physical-stats-card-premium {
  border-radius: var(--premium-border-radius);
  overflow: hidden;
}

.bmi-gauge-container-premium {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: var(--premium-light);
  border-radius: var(--premium-border-radius);
}

.bmi-category-premium {
  margin-bottom: 1rem;
  text-align: center;
}

.bmi-badge-premium {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
}

.bmi-badge-premium.info {
  background: rgba(54, 185, 204, 0.15);
  color: var(--premium-info);
}

.bmi-badge-premium.success {
  background: rgba(28, 200, 138, 0.15);
  color: var(--premium-success);
}

.bmi-badge-premium.warning {
  background: rgba(246, 194, 62, 0.15);
  color: var(--premium-warning);
}

.bmi-badge-premium.danger {
  background: rgba(231, 74, 59, 0.15);
  color: var(--premium-danger);
}

.bmi-gauge-premium {
  position: relative;
  height: 10px;
  background: #f1f1f1;
  border-radius: 10px;
  margin: 0 auto 1.5rem;
  overflow: hidden;
}

.bmi-gauge-scale-premium {
  display: flex;
  height: 100%;
}

.bmi-gauge-section-premium {
  height: 100%;
  flex: 1;
}

.bmi-gauge-section-premium.bmi-underweight-premium {
  background: var(--premium-info);
}

.bmi-gauge-section-premium.bmi-normal-premium {
  background: var(--premium-success);
}

.bmi-gauge-section-premium.bmi-overweight-premium {
  background: var(--premium-warning);
}

.bmi-gauge-section-premium.bmi-obese-premium {
  background: var(--premium-danger);
}

.bmi-gauge-indicator-premium {
  position: absolute;
  top: -7px;
  width: 24px;
  height: 24px;
  background: #fff;
  border: 2px solid var(--premium-primary);
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.bmi-gauge-indicator-value-premium {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--premium-primary);
  color: #fff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
}

.bmi-gauge-labels-premium {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--premium-secondary);
}

.physical-stats-grid-premium {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.25rem;
}

.physical-stat-item-premium {
  display: flex;
  align-items: center;
}

.physical-stat-icon-premium {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--premium-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--premium-primary);
  margin-right: 1rem;
  flex-shrink: 0;
}

.physical-stat-label-premium {
  font-size: 0.8rem;
  color: var(--premium-secondary);
  margin-bottom: 0.25rem;
}

.physical-stat-value-premium {
  font-weight: 500;
  display: flex;
  align-items: baseline;
}

.value-premium {
  font-size: 1.25rem;
  font-weight: 600;
  margin-right: 0.25rem;
  color: var(--premium-dark);
}

.unit-premium {
  font-size: 0.8rem;
  color: var(--premium-secondary);
}

.not-available-premium {
  font-size: 0.875rem;
  color: var(--premium-secondary);
  font-style: italic;
}

/* Premium Access List */
.access-list-premium {
  padding: 1rem;
}

.access-item-premium {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.access-item-premium:last-child {
  border-bottom: none;
}

.access-icon-premium {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.access-content-premium {
  flex: 1;
}

.access-title-premium {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.access-status-premium {
  display: flex;
  align-items: center;
}

.access-badge-premium {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
}

.access-badge-premium.success {
  background: rgba(28, 200, 138, 0.15);
  color: var(--premium-success);
}

.access-badge-premium.info {
  background: rgba(54, 185, 204, 0.15);
  color: var(--premium-info);
}

.access-badge-premium.secondary {
  background: rgba(108, 117, 125, 0.15);
  color: var(--premium-secondary);
}

/* Premium Water Reminder */
.water-reminder-status-premium {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.premium-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--premium-dark);
  margin-bottom: 0.5rem;
}

.form-switch-premium {
  display: flex;
  align-items: center;
}

.toggle-container-premium {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-switch-premium {
  width: 50px !important;
  height: 24px !important;
  background: #e2e8f0;
  border-radius: 50px;
  position: relative;
  cursor: pointer;
  transition: var(--premium-transition);
  display: inline-block;
  outline: none;
  overflow: hidden;
}

.toggle-switch-premium:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--premium-primary-rgb), 0.25);
}

.toggle-switch-premium:hover {
  opacity: 0.9;
}

.toggle-switch-premium.active {
  background: var(--premium-primary);
}

.toggle-switch-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 50px;
  z-index: 0;
}

.toggle-switch-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50px;
  z-index: 0;
}

.toggle-switch-premium.active .toggle-switch-track {
  background: rgba(255, 255, 255, 0.15);
}

.toggle-switch-knob {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 50%;
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.toggle-switch-premium.active .toggle-switch-knob {
  left: calc(100% - 22px);
}

.toggle-switch-premium::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  opacity: 0;
  transition: var(--premium-transition);
  z-index: 0;
}

.toggle-switch-premium:active::after {
  opacity: 1;
}

.toggle-switch-label-premium {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  user-select: none;
  transition: var(--premium-transition);
}

.toggle-switch-premium.active + .toggle-switch-label-premium {
  color: var(--premium-primary);
}

.reminder-details-premium {
  margin-top: 1.5rem;
}

.reminder-item-premium {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
}

.reminder-item-premium:last-child {
  margin-bottom: 0;
}

.reminder-icon-premium {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--premium-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--premium-primary);
  margin-right: 1rem;
  flex-shrink: 0;
}

.reminder-label-premium {
  font-size: 0.8rem;
  color: var(--premium-secondary);
  margin-bottom: 0.25rem;
}

.reminder-value-premium {
  font-weight: 500;
}

.empty-state-premium {
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-state-icon-premium {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: var(--premium-light);
  color: var(--premium-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto 1rem;
}

.empty-state-premium.small {
  padding: 1.5rem;
}

.empty-state-icon-premium.small {
  width: 50px;
  height: 50px;
  font-size: 1.25rem;
}

/* Water Reminder Button Specific Styles */
.empty-state-premium .btn-primary.btn-premium-sm {
  min-width: 140px;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  letter-spacing: 0.01em;
  box-shadow: 0 0.25rem 0.5rem rgba(78, 115, 223, 0.15);
  transition: all 0.2s ease-in-out;
}

.empty-state-premium .btn-primary.btn-premium-sm:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.2);
}

.water-reminder-btn {
  background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
  border: none;
  min-width: 160px;
  padding: 0.6rem 1.5rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  box-shadow: 0 0.25rem 0.75rem rgba(54, 185, 204, 0.25);
}

.water-reminder-btn:hover,
.water-reminder-btn:focus {
  background: linear-gradient(135deg, #258391 0%, #1a6570 100%);
  box-shadow: 0 0.5rem 1rem rgba(54, 185, 204, 0.35);
  transform: translateY(-3px);
}

/* Premium Device Management */
.device-status-premium {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: var(--premium-border-radius);
  font-weight: 500;
  margin-bottom: 1rem;
}

.device-status-premium.registered {
  background: rgba(28, 200, 138, 0.1);
  color: var(--premium-success);
}

.device-status-premium.not-registered {
  background: rgba(246, 194, 62, 0.1);
  color: var(--premium-warning);
}

.device-status-detail-premium {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-left: 0.5rem;
}

.device-id-container-premium {
  background: var(--premium-light);
  border-radius: var(--premium-border-radius);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  word-break: break-all;
}

.device-id-text-premium {
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--premium-dark);
  flex: 1;
  margin-right: 1rem;
}

.btn-copy-premium {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #fff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--premium-primary);
  box-shadow: var(--premium-shadow-sm);
  cursor: pointer;
  transition: var(--premium-transition);
}

.btn-copy-premium:hover {
  background: var(--premium-primary);
  color: #fff;
  box-shadow: var(--premium-shadow);
}

/* Premium Course and Workout Lists */
.course-list-premium, .workout-list-premium {
  padding: 0;
}

.course-item-premium, .workout-item-premium {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.course-item-premium:last-child, .workout-item-premium:last-child {
  border-bottom: none;
}

.course-header-premium {
  margin-bottom: 1rem;
}

.course-avatar-premium {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 600;
  font-size: 1.25rem;
  margin-right: 1rem;
}

.course-info-premium {
  flex: 1;
}

.course-title-premium {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--premium-dark);
}

.course-meta-premium {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.course-category-premium {
  font-size: 0.75rem;
  color: var(--premium-secondary);
  background: var(--premium-light);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.course-status-premium {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
}

.course-status-premium.active {
  background: rgba(28, 200, 138, 0.15);
  color: var(--premium-success);
}

.course-status-premium.completed {
  background: rgba(78, 115, 223, 0.15);
  color: var(--premium-primary);
}

.course-status-premium.inactive {
  background: rgba(108, 117, 125, 0.15);
  color: var(--premium-secondary);
}

.course-progress-premium {
  margin-bottom: 1rem;
}

.progress-label-premium {
  font-size: 0.75rem;
  color: var(--premium-secondary);
}

.progress-value-premium {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--premium-dark);
}

.progress-premium {
  height: 8px;
  background: #f1f1f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.progress-bar-premium {
  height: 100%;
  border-radius: 4px;
}

.progress-bar-premium.success {
  background: var(--premium-success);
}

.progress-bar-premium.info {
  background: var(--premium-info);
}

.progress-bar-premium.warning {
  background: var(--premium-warning);
}

.progress-bar-premium.danger {
  background: var(--premium-danger);
}

.course-details-premium {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--premium-secondary);
}

.course-detail-premium {
  display: flex;
  align-items: center;
}

.course-detail-premium i {
  margin-right: 0.25rem;
}

.course-actions-premium {
  margin-top: 1rem;
  text-align: right;
}

.workout-item-premium {
  display: flex;
  align-items: center;
}

.workout-icon-premium {
  margin-right: 1rem;
}

.workout-avatar-premium {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1rem;
}

.workout-content-premium {
  flex: 1;
}

.workout-title-premium {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--premium-dark);
}

.workout-meta-premium {
  display: flex;
  gap: 1rem;
}

.workout-detail-premium {
  font-size: 0.75rem;
  color: var(--premium-secondary);
  display: flex;
  align-items: center;
}

.workout-detail-premium i {
  margin-right: 0.25rem;
}

.workout-stats-premium {
  text-align: center;
  background: rgba(231, 74, 59, 0.1);
  color: var(--premium-danger);
  padding: 0.5rem 1rem;
  border-radius: var(--premium-border-radius);
  min-width: 80px;
}

.workout-stat-value-premium {
  font-weight: 700;
  font-size: 1.25rem;
}

.workout-stat-label-premium {
  font-size: 0.75rem;
}

/* Premium Food Logs Table */
.table-premium {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table-premium thead {
  background: var(--premium-light);
}

.table-premium th {
  padding: 1rem;
  font-weight: 600;
  color: var(--premium-dark);
  text-align: left;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-premium td {
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
}

.table-premium tr:last-child td {
  border-bottom: none;
}

.table-premium.compact th,
.table-premium.compact td {
  padding: 0.75rem;
  font-size: 0.875rem;
}

.date-header-premium {
  background: rgba(0, 0, 0, 0.02);
}

.date-label-premium {
  font-weight: 600;
  color: var(--premium-dark);
}

.calorie-total-premium {
  background: rgba(231, 74, 59, 0.1);
  color: var(--premium-danger);
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.food-name-premium {
  font-weight: 500;
  color: var(--premium-dark);
}

.calorie-badge-premium {
  background: rgba(78, 115, 223, 0.1);
  color: var(--premium-primary);
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.calorie-badge-premium.success {
  background: rgba(28, 200, 138, 0.1);
  color: var(--premium-success);
}

.calorie-badge-premium.warning {
  background: rgba(246, 194, 62, 0.1);
  color: var(--premium-warning);
}

.calorie-badge-premium.danger {
  background: rgba(231, 74, 59, 0.1);
  color: var(--premium-danger);
}

.time-badges-premium {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.time-badge-premium {
  background: rgba(54, 185, 204, 0.1);
  color: var(--premium-info);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.time-of-day-badge-premium {
  background: rgba(0, 0, 0, 0.05);
  color: var(--premium-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  display: inline-flex;
  align-items: center;
}

/* Premium Modal */
.premium-modal {
  border-radius: var(--premium-border-radius);
  overflow: hidden;
  border: none;
  box-shadow: var(--premium-shadow-lg);
}

.modal-header-premium {
  background: var(--premium-primary);
  color: #fff;
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: none;
}

.modal-title-premium {
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
}

.btn-close-premium {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  transition: var(--premium-transition);
}

.btn-close-premium:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-body-premium {
  padding: 1.5rem;
}

.chart-container-premium {
  margin-bottom: 1.5rem;
}

.divider-premium {
  border: none;
  height: 1px;
  background: rgba(0, 0, 0, 0.05);
  margin: 1.5rem 0;
}

.foods-list-premium {
  font-size: 0.875rem;
  color: var(--premium-secondary);
  max-width: 300px;
}

.calorie-summary-premium {
  background: var(--premium-light);
  border-radius: var(--premium-border-radius);
  padding: 1.25rem;
  margin-top: 1.5rem;
}

.summary-title-premium {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--premium-dark);
}

.summary-subtitle-premium {
  font-size: 0.875rem;
  color: var(--premium-secondary);
  margin-bottom: 0;
}

.average-calories-premium {
  margin-right: 1rem;
  text-align: right;
}

.average-value-premium {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--premium-dark);
  display: block;
}

.average-label-premium {
  font-size: 0.75rem;
  color: var(--premium-secondary);
}

.average-badge-premium {
  background: rgba(28, 200, 138, 0.1);
  color: var(--premium-success);
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.average-badge-premium.warning {
  background: rgba(246, 194, 62, 0.1);
  color: var(--premium-warning);
}

.average-badge-premium.danger {
  background: rgba(231, 74, 59, 0.1);
  color: var(--premium-danger);
}

/* ===== Enhanced Session Access Styles ===== */
.access-list-premium {
  padding: 0;
}

.access-item-premium {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--premium-transition);
}

.access-item-premium:last-child {
  border-bottom: none;
}

.access-item-premium:hover {
  background: var(--premium-light);
}

.access-icon-premium {
  width: 40px;
  height: 40px;
  border-radius: var(--premium-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.bg-primary-light {
  background: rgba(78, 115, 223, 0.1);
}

.bg-success-light {
  background: rgba(28, 200, 138, 0.1);
}

.bg-info-light {
  background: rgba(54, 185, 204, 0.1);
}

.access-content-premium {
  flex: 1;
}

.access-title-premium {
  font-weight: 600;
  color: var(--premium-dark);
  margin-bottom: 0.25rem;
}

.access-status-premium {
  margin-top: 0.25rem;
}

.access-badge-premium {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.access-badge-premium.success {
  background: rgba(28, 200, 138, 0.15);
  color: var(--premium-success);
}

.access-badge-premium.info {
  background: rgba(54, 185, 204, 0.15);
  color: var(--premium-info);
}

.access-badge-premium.secondary {
  background: rgba(108, 117, 125, 0.15);
  color: var(--premium-secondary);
}

/* ===== Enhanced Tab Content Styles ===== */
.tab-content-premium {
  padding: 2rem;
  background: #fff;
}

.tab-pane-premium {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== Enhanced Table Styles ===== */
.table-premium {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: var(--premium-border-radius);
  overflow: hidden;
  box-shadow: var(--premium-shadow);
}

.table-premium th {
  background: var(--premium-light);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--premium-dark);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-premium td {
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  color: var(--premium-dark);
}

.table-premium tr:hover {
  background: var(--premium-light);
}

.table-premium tr:last-child td {
  border-bottom: none;
}

/* ===== Enhanced Search and Filter Styles ===== */
.search-filter-premium {
  background: #fff;
  padding: 1.5rem;
  border-radius: var(--premium-border-radius);
  box-shadow: var(--premium-shadow);
  margin-bottom: 1.5rem;
}

.search-input-premium {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d3e2;
  border-radius: var(--premium-border-radius);
  background: #fff;
  color: var(--premium-dark);
  font-size: 0.875rem;
  transition: var(--premium-transition);
}

.search-input-premium:focus {
  outline: none;
  border-color: var(--premium-primary);
  box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.filter-group-premium {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-label-premium {
  font-weight: 500;
  color: var(--premium-secondary);
  white-space: nowrap;
}

/* ===== Enhanced Empty State Styles ===== */
.empty-state-premium {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--premium-secondary);
}

.empty-state-icon-premium {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: var(--premium-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--premium-secondary);
}

.empty-state-title-premium {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--premium-dark);
  margin-bottom: 0.5rem;
}

.empty-state-description-premium {
  color: var(--premium-secondary);
  margin-bottom: 1.5rem;
}

/* ===== Enhanced Progress Indicators ===== */
.progress-premium {
  height: 8px;
  background: var(--premium-light);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar-premium {
  height: 100%;
  background: linear-gradient(90deg, var(--premium-primary), var(--premium-primary-light));
  border-radius: 4px;
  transition: width 0.6s ease;
  position: relative;
}

.progress-bar-premium::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ===== Enhanced Notification Styles ===== */
.notification-premium {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: #fff;
  border: 1px solid #d1d3e2;
  border-radius: var(--premium-border-radius);
  box-shadow: var(--premium-shadow-lg);
  padding: 1rem;
  max-width: 400px;
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-premium.success {
  border-left: 4px solid var(--premium-success);
}

.notification-premium.error {
  border-left: 4px solid var(--premium-danger);
}

.notification-premium.warning {
  border-left: 4px solid var(--premium-warning);
}

.notification-premium.info {
  border-left: 4px solid var(--premium-info);
}

/* PIN Management Styles */
.pin-actions-premium {
    display: flex;
    flex-direction: row;
    gap: 12px;
}

.pin-action-btn-premium {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: #fff;
    color: #2c3e50;
    transition: all 0.2s ease;
}

.pin-action-btn-premium:hover {
    background: #f8f9fa;
    border-color: rgba(0, 0, 0, 0.2);
}

.pin-action-btn-premium i {
    font-size: 14px;
}

.btn-text-premium {
    font-size: 13px;
    font-weight: 500;
}

/* Mobile Responsive Styles */
@media (max-width: 600px) {
    .pin-management-section-premium,
    .pin-status-card-premium {
        padding: 10px !important;
    }
    
    .pin-actions-premium {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 8px !important;
        width: 100% !important;
    }
    
    .pin-action-btn-premium {
        width: 100% !important;
        justify-content: center !important;
        padding: 8px !important;
    }
    
    .pin-value-container-premium {
        min-width: 100px !important;
        max-width: 100% !important;
    }
}
