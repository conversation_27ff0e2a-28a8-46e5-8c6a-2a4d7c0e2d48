<?php
require_once 'includes/video_helpers.php';

// Test the video URL from the database
$testUrl = 'https://vimeo.com/**********/ae75b6e329';

echo "Testing Video Helpers with URL: $testUrl\n\n";

// Test extractVimeoId
$videoId = extractVimeoId($testUrl);
echo "Video ID: " . ($videoId ?: 'null') . "\n";

// Test extractVimeoPrivateHash
$privateHash = extractVimeoPrivateHash($testUrl);
echo "Private Hash: " . ($privateHash ?: 'null') . "\n";

// Test isVimeoUrl
$isVimeo = isVimeoUrl($testUrl);
echo "Is Vimeo URL: " . ($isVimeo ? 'true' : 'false') . "\n";

// Test getVimeoEmbedUrl
$embedUrl = getVimeoEmbedUrl($testUrl);
echo "Embed URL: " . ($embedUrl ?: 'null') . "\n";

// Test getVideoProvider
$provider = getVideoProvider($testUrl);
echo "Video Provider: $provider\n";

echo "\n--- Expected Results ---\n";
echo "Video ID: **********\n";
echo "Private Hash: ae75b6e329\n";
echo "Is Vimeo URL: true\n";
echo "Embed URL: https://player.vimeo.com/video/**********?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&h=ae75b6e329\n";
echo "Video Provider: vimeo\n";
?>
