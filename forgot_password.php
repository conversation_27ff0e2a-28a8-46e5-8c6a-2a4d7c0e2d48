<?php
// Don't require header.php as we don't want to redirect unauthenticated users
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

$step = isset($_GET['step']) ? $_GET['step'] : 'request';
$token = isset($_GET['token']) ? $_GET['token'] : '';
$email = isset($_GET['email']) ? $_GET['email'] : '';

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 'request') {
        // Step 1: Process email submission
        $email = Utilities::sanitizeInput($_POST['email'] ?? '');
        
        if (empty($email) || !Utilities::validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Check if email exists
            $query = "SELECT id, username, name FROM users WHERE email = ? LIMIT 1";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                $error = 'No account found with that email address.';
            } else {
                $user = $result->fetch_assoc();
                
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry
                
                // Store token in database
                $deleteQuery = "DELETE FROM password_resets WHERE user_id = ?";
                $deleteStmt = $conn->prepare($deleteQuery);
                $deleteStmt->bind_param("i", $user['id']);
                $deleteStmt->execute();
                
                $insertQuery = "INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)";
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bind_param("iss", $user['id'], $token, $expires);
                
                if ($insertStmt->execute()) {
                    // In a real application, you would send an email with the reset link
                    // For this implementation, we'll just show the link on the page
                    $resetLink = "http://{$_SERVER['HTTP_HOST']}/forgot_password.php?step=reset&token=$token&email=" . urlencode($email);
                    $success = "Password reset link has been generated. In a production environment, this would be emailed to you.";
                    $showResetLink = true;
                } else {
                    $error = 'Failed to generate reset token. Please try again.';
                }
            }
        }
    } elseif ($step === 'reset') {
        // Step 2: Process password reset
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $token = $_POST['token'] ?? '';
        $email = $_POST['email'] ?? '';
        
        // Validate inputs
        if (empty($password)) {
            $error = 'Password is required.';
        } else {
            $policyResult = validate_password_policy($password);
            if ($policyResult !== true) {
                $error = $policyResult;
            }
        }
        if (empty($error) && $password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        }
        if (empty($error) && (empty($token) || empty($email))) {
            $error = 'Invalid reset request.';
        } else {
            // Verify token
            $query = "SELECT pr.user_id, pr.expires_at, u.username 
                      FROM password_resets pr 
                      JOIN users u ON pr.user_id = u.id 
                      WHERE pr.token = ? AND u.email = ? LIMIT 1";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ss", $token, $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                $error = 'Invalid or expired reset token.';
            } else {
                $resetData = $result->fetch_assoc();
                
                // Check if token is expired
                if (strtotime($resetData['expires_at']) < time()) {
                    $error = 'Reset token has expired. Please request a new one.';
                } else {
                    // Update password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $updateQuery = "UPDATE users SET password = ? WHERE id = ?";
                    $updateStmt = $conn->prepare($updateQuery);
                    $updateStmt->bind_param("si", $hashedPassword, $resetData['user_id']);
                    
                    if ($updateStmt->execute()) {
                        // Delete used token
                        $deleteQuery = "DELETE FROM password_resets WHERE user_id = ?";
                        $deleteStmt = $conn->prepare($deleteQuery);
                        $deleteStmt->bind_param("i", $resetData['user_id']);
                        $deleteStmt->execute();
                        
                        $success = 'Your password has been reset successfully. You can now login with your new password.';
                        $passwordReset = true;
                    } else {
                        $error = 'Failed to update password. Please try again.';
                    }
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - KFT Fitness Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
    .dashboard-green-btn, .btn-success {
        background-color: #27ae60 !important;
        color: #fff !important;
        border: none !important;
        box-shadow: none !important;
    }
    .dashboard-green-btn:hover, .btn-success:hover {
        background-color: #219150 !important;
        color: #fff !important;
    }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="text-center mb-4">
                    <h2 class="fw-bold text-primary">KFT Fitness</h2>
                    <p class="text-muted">Admin Dashboard</p>
                </div>
                
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <?php if ($step === 'request' && !isset($passwordReset)): ?>
                            <h4 class="mb-4">Forgot Password</h4>
                            
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (isset($success)): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i> <?php echo $success; ?>
                                </div>
                                
                                <?php if (isset($showResetLink)): ?>
                                    <div class="alert alert-info">
                                        <p><strong>Reset Link:</strong></p>
                                        <p><a href="<?php echo $resetLink; ?>"><?php echo $resetLink; ?></a></p>
                                        <p class="small text-muted mt-2">Note: In a production environment, this link would be sent to your email.</p>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <form method="post" action="forgot_password.php?step=request">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                                    <div class="form-text">Enter the email address associated with your account.</div>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i> Send Reset Link
                                    </button>
                                    <a href="login.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i> Back to Login
                                    </a>
                                </div>
                            </form>
                        <?php elseif ($step === 'reset' && !isset($passwordReset)): ?>
                            <h4 class="mb-4">Reset Password</h4>
                            
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="post" action="forgot_password.php?step=reset">
                                <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                                <input type="hidden" name="email" value="<?php echo htmlspecialchars($email); ?>">
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">Password must be at least 6 characters long.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-key me-2"></i> Reset Password
                                    </button>
                                    <a href="login.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i> Back to Login
                                    </a>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="text-center">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                <h4 class="mt-3 mb-3">Password Reset Successful</h4>
                                
                                <?php if (isset($success)): ?>
                                    <div class="alert alert-success">
                                        <?php echo $success; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="d-grid gap-2 mt-4">
                                    <a href="login.php" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i> Go to Login
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-muted small">
                        &copy; <?php echo date('Y'); ?> KFT Fitness. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
