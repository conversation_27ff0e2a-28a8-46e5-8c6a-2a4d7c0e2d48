<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    $userId = 29;
    $videoId = 1;
    
    // Check user enrollments
    $stmt = $pdo->prepare("SELECT * FROM user_enrollments WHERE user_id = ?");
    $stmt->execute([$userId]);
    $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Check course videos
    $stmt = $pdo->prepare("SELECT * FROM course_videos WHERE id = ?");
    $stmt->execute([$videoId]);
    $video = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Check if user is enrolled in the course containing this video
    $hasAccess = false;
    $accessDetails = [];
    
    if ($video) {
        $courseId = $video['course_id'];
        
        $stmt = $pdo->prepare("
            SELECT * FROM user_enrollments 
            WHERE user_id = ? AND course_id = ? AND status = 'active'
        ");
        $stmt->execute([$userId, $courseId]);
        $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($enrollment) {
            $hasAccess = true;
            $accessDetails = [
                'enrollment_found' => true,
                'enrollment' => $enrollment,
                'video' => $video
            ];
        } else {
            $accessDetails = [
                'enrollment_found' => false,
                'course_id' => $courseId,
                'video' => $video,
                'all_enrollments' => $enrollments
            ];
        }
    } else {
        $accessDetails = [
            'video_found' => false,
            'video_id' => $videoId,
            'all_enrollments' => $enrollments
        ];
    }
    
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'video_id' => $videoId,
        'has_access' => $hasAccess,
        'details' => $accessDetails
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
