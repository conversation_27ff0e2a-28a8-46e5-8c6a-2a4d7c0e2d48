<?php
session_start();
require_once 'includes/auth.php';
require_once 'includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if user is admin
$db = new Database();
$conn = $db->getConnection();

// For development mode, assume the user is an admin
if (defined('DEV_MODE') && DEV_MODE === true) {
    $isAdmin = true;
} else {
    // Try to get from admin_users table first
    $stmt = $conn->prepare("SELECT role FROM admin_users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $isAdmin = ($user['role'] === 'admin');
    } else {
        // If not in admin_users, check if user has admin role in session
        $isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
}

if (!$isAdmin) {
    header('Location: index.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $quote = isset($_POST['quote']) ? trim($_POST['quote']) : '';
    $author = isset($_POST['author']) ? trim($_POST['author']) : '';
    $category = isset($_POST['category']) ? trim($_POST['category']) : '';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    $errors = [];

    if (empty($quote)) {
        $errors[] = "Quote text is required.";
    }

    if (empty($errors)) {
        // Insert the quote
        $stmt = $conn->prepare("INSERT INTO motivational_quotes (quote, author, category, is_active) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("sssi", $quote, $author, $category, $isActive);

        if ($stmt->execute()) {
            $successMessage = "Quote added successfully.";
            // Clear form data after successful submission
            $quote = $author = $category = '';
            $isActive = 1;
        } else {
            $errors[] = "Failed to add quote: " . $stmt->error;
        }

        $stmt->close();
    }
}

// Get all categories for dropdown
$categoriesStmt = $conn->prepare("SELECT DISTINCT category FROM motivational_quotes WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categoriesStmt->execute();
$categoriesResult = $categoriesStmt->get_result();
$categories = [];

while ($row = $categoriesResult->fetch_assoc()) {
    $categories[] = $row['category'];
}

$categoriesStmt->close();

// Include header
$pageTitle = "Add New Quote";
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Add New Quote</h1>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quote Details</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($errors) && !empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($successMessage)): ?>
                        <div class="alert alert-success"><?php echo $successMessage; ?></div>
                    <?php endif; ?>

                    <form method="post">
                        <div class="form-group">
                            <label for="quote">Quote Text <span class="text-danger">*</span></label>
                            <textarea name="quote" id="quote" class="form-control" rows="4" required><?php echo isset($quote) ? htmlspecialchars($quote) : ''; ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="author">Author</label>
                            <input type="text" name="author" id="author" class="form-control" value="<?php echo isset($author) ? htmlspecialchars($author) : ''; ?>">
                            <small class="form-text text-muted">Leave blank for "Unknown" or "Anonymous"</small>
                        </div>

                        <div class="form-group">
                            <label for="category">Category</label>
                            <div class="input-group">
                                <input type="text" name="category" id="category" class="form-control" list="category-list" value="<?php echo isset($category) ? htmlspecialchars($category) : ''; ?>">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select</button>
                                    <div class="dropdown-menu">
                                        <?php foreach ($categories as $cat): ?>
                                            <a class="dropdown-item" href="#" onclick="document.getElementById('category').value='<?php echo htmlspecialchars($cat); ?>'; return false;">
                                                <?php echo htmlspecialchars(ucfirst($cat)); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <datalist id="category-list">
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                <?php endforeach; ?>
                            </datalist>
                            <small class="form-text text-muted">Enter a new category or select an existing one</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" <?php echo (!isset($isActive) || $isActive) ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Add Quote</button>
                            <a href="quotes.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">AI Quote Generator</h6>
                </div>
                <div class="card-body">
                    <p>Generate a quote using AI based on a theme or topic.</p>

                    <div class="form-group">
                        <label for="ai-theme">Theme or Topic</label>
                        <input type="text" id="ai-theme" class="form-control" placeholder="e.g., perseverance, morning workout">
                    </div>

                    <div class="form-group">
                        <label for="ai-category">Category</label>
                        <select id="ai-category" class="form-control">
                            <option value="motivation">Motivation</option>
                            <option value="fitness">Fitness</option>
                            <option value="health">Health</option>
                            <option value="mindfulness">Mindfulness</option>
                            <option value="success">Success</option>
                        </select>
                    </div>

                    <button type="button" id="generate-quote" class="btn btn-info btn-block">Generate Quote</button>

                    <div id="ai-result" class="mt-3" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <blockquote class="blockquote mb-0">
                                    <p id="ai-quote-text"></p>
                                    <footer class="blockquote-footer" id="ai-quote-author"></footer>
                                </blockquote>
                            </div>
                        </div>
                        <button type="button" id="use-ai-quote" class="btn btn-success btn-sm mt-2">Use This Quote</button>
                    </div>

                    <div id="ai-loading" class="text-center mt-3" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Generating quote...</p>
                    </div>

                    <div id="ai-error" class="alert alert-danger mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generate-quote');
    const useAiQuoteBtn = document.getElementById('use-ai-quote');
    const aiThemeInput = document.getElementById('ai-theme');
    const aiCategorySelect = document.getElementById('ai-category');
    const aiResult = document.getElementById('ai-result');
    const aiLoading = document.getElementById('ai-loading');
    const aiError = document.getElementById('ai-error');
    const aiQuoteText = document.getElementById('ai-quote-text');
    const aiQuoteAuthor = document.getElementById('ai-quote-author');

    // Form fields
    const quoteTextarea = document.getElementById('quote');
    const authorInput = document.getElementById('author');
    const categoryInput = document.getElementById('category');

    generateBtn.addEventListener('click', function() {
        const theme = aiThemeInput.value.trim();
        const category = aiCategorySelect.value;

        // Show loading, hide results and error
        aiLoading.style.display = 'block';
        aiResult.style.display = 'none';
        aiError.style.display = 'none';

        // Make API request to generate quote
        fetch('../api/ai_quotes.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer <?php echo isset($_SESSION['token']) ? $_SESSION['token'] : ''; ?>'
            },
            body: JSON.stringify({
                theme: theme,
                category: category
            })
        })
        .then(response => response.json())
        .then(data => {
            aiLoading.style.display = 'none';

            if (data.success) {
                // Show the generated quote
                aiQuoteText.textContent = data.quote.quote;
                aiQuoteAuthor.textContent = data.quote.author || 'Unknown';
                aiResult.style.display = 'block';
            } else {
                // Show error message
                aiError.textContent = data.error || 'Failed to generate quote';
                aiError.style.display = 'block';
            }
        })
        .catch(error => {
            aiLoading.style.display = 'none';
            aiError.textContent = 'Error: ' + error.message;
            aiError.style.display = 'block';
        });
    });

    useAiQuoteBtn.addEventListener('click', function() {
        // Fill the form with AI-generated quote
        quoteTextarea.value = aiQuoteText.textContent;
        authorInput.value = aiQuoteAuthor.textContent === 'Unknown' ? '' : aiQuoteAuthor.textContent;
        categoryInput.value = aiCategorySelect.value;
    });
});
</script>

<?php include 'includes/footer.php'; ?>
