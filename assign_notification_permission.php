<?php
/**
 * Assign Notification Permission
 *
 * This script assigns the view_notifications permission to all staff accounts.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    die('You must be a super admin to access this tool.');
}

// Get the view_notifications permission ID
$permissionQuery = "SELECT id FROM admin_permissions WHERE slug = 'view_notifications'";
$result = $conn->query($permissionQuery);
$permissionId = null;

if ($result && $result->num_rows > 0) {
    $permissionId = $result->fetch_assoc()['id'];
} else {
    // Create the permission if it doesn't exist
    $insertPermissionQuery = "INSERT INTO admin_permissions (slug, name, description) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($insertPermissionQuery);
    $name = "View Notifications";
    $description = "Allows staff to view notifications in the admin panel";
    $slug = "view_notifications";
    $stmt->bind_param("sss", $slug, $name, $description);
    
    if ($stmt->execute()) {
        $permissionId = $conn->insert_id;
        echo "<p>Created view_notifications permission with ID: $permissionId</p>";
    } else {
        die("Failed to create permission: " . $stmt->error);
    }
}

// Get all staff accounts
$staffQuery = "SELECT id, username, name FROM admin_users WHERE role = 'staff'";
$staffResult = $conn->query($staffQuery);
$staffMembers = [];

if ($staffResult && $staffResult->num_rows > 0) {
    while ($row = $staffResult->fetch_assoc()) {
        $staffMembers[] = $row;
    }
}

// Process form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'assign_permission') {
        $assignCount = 0;
        $alreadyAssignedCount = 0;
        
        foreach ($staffMembers as $staff) {
            $staffId = $staff['id'];
            
            // Check if the staff already has the permission
            $checkQuery = "SELECT * FROM admin_user_permissions WHERE admin_user_id = ? AND permission_id = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("ii", $staffId, $permissionId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $alreadyAssignedCount++;
            } else {
                // Assign the permission
                $assignQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
                $stmt = $conn->prepare($assignQuery);
                $stmt->bind_param("ii", $staffId, $permissionId);
                
                if ($stmt->execute()) {
                    $assignCount++;
                }
            }
        }
        
        $message = "Assigned view_notifications permission to $assignCount staff members. $alreadyAssignedCount staff members already had the permission.";
    }
}

// Get staff members with the view_notifications permission
$staffWithPermissionQuery = "SELECT au.id, au.username, au.name 
                            FROM admin_users au
                            JOIN admin_user_permissions aup ON au.id = aup.admin_user_id
                            WHERE au.role = 'staff' AND aup.permission_id = ?";
$stmt = $conn->prepare($staffWithPermissionQuery);
$stmt->bind_param("i", $permissionId);
$stmt->execute();
$result = $stmt->get_result();
$staffWithPermission = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $staffWithPermission[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Notification Permission</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border-radius: 5px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Assign Notification Permission</h1>
        <p>This tool assigns the view_notifications permission to all staff accounts.</p>
        
        <?php if (!empty($message)): ?>
        <div class="alert alert-info">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>Permission Information</h2>
            <p>Permission ID: <?php echo $permissionId; ?></p>
            <p>Permission Slug: view_notifications</p>
            <p>Permission Name: View Notifications</p>
            <p>Description: Allows staff to view notifications in the admin panel</p>
        </div>
        
        <div class="section">
            <h2>Staff Members (<?php echo count($staffMembers); ?>)</h2>
            <?php if (!empty($staffMembers)): ?>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Name</th>
                        <th>Has Permission</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($staffMembers as $staff): ?>
                    <tr>
                        <td><?php echo $staff['id']; ?></td>
                        <td><?php echo htmlspecialchars($staff['username']); ?></td>
                        <td><?php echo htmlspecialchars($staff['name']); ?></td>
                        <td>
                            <?php
                            $hasPermission = false;
                            foreach ($staffWithPermission as $staffPerm) {
                                if ($staffPerm['id'] === $staff['id']) {
                                    $hasPermission = true;
                                    break;
                                }
                            }
                            echo $hasPermission ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>';
                            ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="alert alert-warning">
                No staff members found.
            </div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>Assign Permission to All Staff</h2>
            <form method="post" action="">
                <input type="hidden" name="action" value="assign_permission">
                <button type="submit" class="btn btn-primary">Assign view_notifications Permission to All Staff</button>
            </form>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
