<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isStaff = $auth->hasRole('staff');
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');

// Check if user ID and course ID are provided
if (!isset($_GET['user_id']) || !is_numeric($_GET['user_id']) || !isset($_GET['course_id']) || !is_numeric($_GET['course_id'])) {
    Utilities::setFlashMessage('error', 'Invalid user or course ID.');
    Utilities::redirect('courses.php');
}

$userId = $_GET['user_id'];
$courseId = $_GET['course_id'];

// Fetch user data
$userQuery = "SELECT * FROM users WHERE id = ?";
$userStmt = $conn->prepare($userQuery);
$userStmt->bind_param("i", $userId);
$userStmt->execute();
$userResult = $userStmt->get_result();

if ($userResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'User not found.');
    Utilities::redirect('courses.php');
}

$user = $userResult->fetch_assoc();

// Check if staff member is trying to access a user not assigned to them
if ($isStaff && $user['assigned_staff_id'] != $currentAdminId) {
    Utilities::setFlashMessage('error', 'You do not have permission to view this user\'s progress.');
    Utilities::redirect('courses.php');
}

// Fetch course data
$courseQuery = "SELECT * FROM courses WHERE id = ?";
$courseStmt = $conn->prepare($courseQuery);
$courseStmt->bind_param("i", $courseId);
$courseStmt->execute();
$courseResult = $courseStmt->get_result();

if ($courseResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Course not found.');
    Utilities::redirect('courses.php');
}

$course = $courseResult->fetch_assoc();

// Check if user is enrolled in this course
$enrollmentQuery = "SELECT * FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
$enrollmentStmt = $conn->prepare($enrollmentQuery);
$enrollmentStmt->bind_param("ii", $userId, $courseId);
$enrollmentStmt->execute();
$enrollmentResult = $enrollmentStmt->get_result();

if ($enrollmentResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'User is not enrolled in this course.');
    Utilities::redirect('course_enrollments.php?course_id=' . $courseId);
}

$enrollment = $enrollmentResult->fetch_assoc();

// Handle unlock/lock video
if (isset($_GET['toggle_unlock']) && is_numeric($_GET['toggle_unlock'])) {
    $videoId = $_GET['toggle_unlock'];

    // Check if video progress exists
    $progressQuery = "SELECT * FROM user_video_progress WHERE user_id = ? AND video_id = ?";
    $progressStmt = $conn->prepare($progressQuery);
    $progressStmt->bind_param("ii", $userId, $videoId);
    $progressStmt->execute();
    $progressResult = $progressStmt->get_result();

    if ($progressResult->num_rows > 0) {
        $progress = $progressResult->fetch_assoc();
        $newUnlockStatus = $progress['is_unlocked'] ? 0 : 1;
        $unlockDate = $newUnlockStatus ? date('Y-m-d') : null;

        // Update the unlock status
        $updateQuery = "UPDATE user_video_progress
                        SET is_unlocked = ?, unlock_date = ?
                        WHERE user_id = ? AND video_id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("isii", $newUnlockStatus, $unlockDate, $userId, $videoId);

        if ($updateStmt->execute()) {
            $action = $newUnlockStatus ? 'unlocked' : 'locked';
            Utilities::setFlashMessage('success', "Video $action successfully.");
        } else {
            Utilities::setFlashMessage('error', "Failed to update video status.");
        }
    } else {
        // Create new progress entry with unlocked status
        $insertQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                        VALUES (?, ?, 1, CURDATE())";
        $insertStmt = $conn->prepare($insertQuery);
        $insertStmt->bind_param("ii", $userId, $videoId);

        if ($insertStmt->execute()) {
            Utilities::setFlashMessage('success', "Video unlocked successfully.");
        } else {
            Utilities::setFlashMessage('error', "Failed to unlock video.");
        }
    }

    Utilities::redirect('user_progress.php?user_id=' . $userId . '&course_id=' . $courseId);
}

// Handle mark as completed
if (isset($_GET['mark_completed']) && is_numeric($_GET['mark_completed'])) {
    $videoId = $_GET['mark_completed'];

    // Update the completion status
    $updateQuery = "UPDATE user_video_progress
                    SET completed = 1, last_watched_at = NOW()
                    WHERE user_id = ? AND video_id = ?";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("ii", $userId, $videoId);

    if ($updateStmt->execute()) {
        Utilities::setFlashMessage('success', "Video marked as completed.");
    } else {
        Utilities::setFlashMessage('error', "Failed to mark video as completed.");
    }

    Utilities::redirect('user_progress.php?user_id=' . $userId . '&course_id=' . $courseId);
}

// Get all videos for this course with progress information
$query = "SELECT v.*,
          IFNULL(p.is_unlocked, 0) as is_unlocked,
          IFNULL(p.completed, 0) as is_completed,
          p.unlock_date,
          p.last_watched_at,
          p.watch_time_seconds as watch_duration_seconds,
          p.progress_percentage
          FROM course_videos v
          LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
          WHERE v.course_id = ?
          ORDER BY v.week_number, v.sequence_number";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $userId, $courseId);
$stmt->execute();
$result = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>User Progress</h1>
    <a href="course_enrollments.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Enrollments
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                User: <?php echo htmlspecialchars($user['name']); ?> |
                Course: <?php echo htmlspecialchars($course['title']); ?>
            </h5>
            <span class="badge <?php echo $enrollment['status'] === 'active' ? 'bg-success' : ($enrollment['status'] === 'completed' ? 'bg-primary' : 'bg-secondary'); ?>">
                <?php echo ucfirst($enrollment['status']); ?>
            </span>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <strong>Enrollment Date:</strong> <?php echo date('M d, Y', strtotime($enrollment['enrollment_date'])); ?>
            </div>
            <div class="col-md-4">
                <strong>Start Date:</strong> <?php echo date('M d, Y', strtotime($enrollment['start_date'])); ?>
            </div>
            <div class="col-md-4">
                <strong>End Date:</strong> <?php echo $enrollment['end_date'] ? date('M d, Y', strtotime($enrollment['end_date'])) : 'N/A'; ?>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Week</th>
                        <th>Seq</th>
                        <th>Title</th>
                        <th>Status</th>
                        <th>Unlock Date</th>
                        <th>Completion</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result && $result->num_rows > 0): ?>
                        <?php
                        $currentWeek = 0;
                        while ($video = $result->fetch_assoc()):
                            // Add week separator
                            if ($currentWeek != $video['week_number']):
                                $currentWeek = $video['week_number'];
                        ?>
                            <tr class="table-secondary">
                                <td colspan="7"><strong>Week <?php echo $currentWeek; ?></strong></td>
                            </tr>
                        <?php endif; ?>
                            <tr>
                                <td><?php echo $video['week_number']; ?></td>
                                <td><?php echo $video['sequence_number']; ?></td>
                                <td><?php echo htmlspecialchars($video['title']); ?></td>
                                <td>
                                    <?php if ($video['is_unlocked']): ?>
                                        <span class="badge bg-success">Unlocked</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Locked</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $video['unlock_date'] ? date('M d, Y', strtotime($video['unlock_date'])) : 'N/A'; ?></td>
                                <td>
                                    <?php if ($video['is_completed']): ?>
                                        <span class="badge bg-primary">Completed</span>
                                        <small class="d-block text-muted"><?php echo date('M d, Y', strtotime($video['last_watched_at'])); ?></small>
                                    <?php else: ?>
                                        <?php if ($video['watch_duration_seconds'] > 0): ?>
                                            <div class="progress" style="height: 5px;">
                                                <div class="progress-bar" role="progressbar" style="width: <?php echo min(100, ($video['watch_duration_seconds'] / ($video['duration_minutes'] * 60)) * 100); ?>%"></div>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo floor($video['watch_duration_seconds'] / 60); ?> min watched
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">Not started</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="user_progress.php?user_id=<?php echo $userId; ?>&course_id=<?php echo $courseId; ?>&toggle_unlock=<?php echo $video['id']; ?>" class="btn btn-sm <?php echo $video['is_unlocked'] ? 'btn-outline-secondary' : 'btn-outline-success'; ?>">
                                            <i class="fas <?php echo $video['is_unlocked'] ? 'fa-lock' : 'fa-unlock'; ?>"></i> <?php echo $video['is_unlocked'] ? 'Lock' : 'Unlock'; ?>
                                        </a>
                                        <?php if ($video['is_unlocked'] && !$video['is_completed']): ?>
                                            <a href="user_progress.php?user_id=<?php echo $userId; ?>&course_id=<?php echo $courseId; ?>&mark_completed=<?php echo $video['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-check"></i> Mark Completed
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">No videos found for this course</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
