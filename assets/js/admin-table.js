// Admin Table JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips if Bootstrap 5 is available
    if (typeof bootstrap !== 'undefined') {
        // Initialize tooltips
        if (bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    delay: { show: 500, hide: 100 }
                });
            });
        }

        // Initialize dropdowns as a fallback
        if (bootstrap.Dropdown) {
            const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl, {
                    reference: 'parent'
                });
            });
        }
    }
    // Search functionality
    const searchInput = document.getElementById('adminSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('adminUsersTable');
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const username = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const email = row.querySelector('td:nth-child(3)') ?
                    row.querySelector('td:nth-child(3)').textContent.toLowerCase() : '';
                const role = row.querySelector('td:nth-child(4)').textContent.toLowerCase();

                if (username.includes(searchTerm) || email.includes(searchTerm) || role.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Refresh button functionality
    const refreshButton = document.getElementById('refreshAdminTable');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            const icon = this.querySelector('.fa-sync-alt');
            icon.classList.add('spinning');
            this.disabled = true;

            // Simulate refresh delay
            setTimeout(() => {
                location.reload();
            }, 800);
        });
    }

    // Enhanced dropdown functionality with Bootstrap integration
    // Listen for Bootstrap dropdown events to change the icon
    document.addEventListener('show.bs.dropdown', function(e) {
        const toggle = e.relatedTarget;
        if (toggle && toggle.classList.contains('action-btn')) {
            // Change icon to vertical ellipsis when dropdown is shown
            const icon = toggle.querySelector('i.fas');
            if (icon) {
                icon.classList.remove('fa-ellipsis-h');
                icon.classList.add('fa-ellipsis-v');
            }
        }
    });

    document.addEventListener('hide.bs.dropdown', function(e) {
        const toggle = e.relatedTarget;
        if (toggle && toggle.classList.contains('action-btn')) {
            // Change icon back to horizontal ellipsis when dropdown is hidden
            const icon = toggle.querySelector('i.fas');
            if (icon) {
                icon.classList.remove('fa-ellipsis-v');
                icon.classList.add('fa-ellipsis-h');
            }
        }
    });

    // Fix dropdown positioning for better visibility
    document.addEventListener('shown.bs.dropdown', function(e) {
        const dropdown = e.target;
        const toggle = e.relatedTarget;
        const dropdownMenu = dropdown.querySelector('.dropdown-menu');

        if (dropdownMenu && toggle) {
            // Get dimensions and positions
            const toggleRect = toggle.getBoundingClientRect();
            const menuRect = dropdownMenu.getBoundingClientRect();
            const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

            // Check if dropdown goes off screen
            const goesOffBottom = menuRect.bottom > viewportHeight;
            const goesOffRight = menuRect.right > viewportWidth;

            // Apply positioning fixes
            if (goesOffBottom) {
                // Position above the button if it would go off the bottom
                dropdownMenu.classList.add('dropdown-menu-up');
                dropdownMenu.style.top = 'auto';
                dropdownMenu.style.bottom = (viewportHeight - toggleRect.top) + 'px';
            }

            if (goesOffRight) {
                // Align to the right if it would go off the right edge
                dropdownMenu.style.left = 'auto';
                dropdownMenu.style.right = (viewportWidth - toggleRect.right) + 'px';
            }
        }
    });
});
