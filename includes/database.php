<?php
require_once 'config.php';

class Database {
    private $conn;
    
    // Constructor - establishes database connection
    public function __construct() {
        try {
            $this->conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            
            if ($this->conn->connect_error) {
                throw new Exception("Connection failed: " . $this->conn->connect_error);
            }
            
            $this->conn->set_charset("utf8mb4");
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    // Get database connection
    public function getConnection() {
        return $this->conn;
    }
    
    // Execute a query
    public function query($sql) {
        return $this->conn->query($sql);
    }
    
    // Execute a prepared statement
    public function prepare($sql) {
        return $this->conn->prepare($sql);
    }
    
    // Get the last inserted ID
    public function getLastId() {
        return $this->conn->insert_id;
    }
    
    // Escape string for security
    public function escapeString($string) {
        return $this->conn->real_escape_string($string);
    }
    
    // Close the database connection
    public function __destruct() {
        if ($this->conn) {
            $this->conn->close();
        }
    }
    
    // Get user by phone number (checks all common formats)
    public function getUserByPhone($phone) {
        $normalized = preg_replace('/[^0-9]/', '', $phone);
        $withCountry = (strlen($normalized) === 10) ? '+91' . $normalized : $normalized;
        $stmt = $this->conn->prepare("SELECT * FROM users WHERE phone = ? OR phone_number = ? OR phone = ? OR phone_number = ? OR phone = ? OR phone_number = ? LIMIT 1");
        if (!$stmt) return null;
        $stmt->bind_param("ssssss", $phone, $phone, $normalized, $normalized, $withCountry, $withCountry);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
        return $user ? $user : null;
    }
    
    // Update user's device_id
    public function updateUserDeviceId($userId, $deviceId) {
        $stmt = $this->conn->prepare("UPDATE users SET device_id = ? WHERE id = ?");
        if (!$stmt) return false;
        $stmt->bind_param("si", $deviceId, $userId);
        $result = $stmt->execute();
        $stmt->close();
        return $result;
    }

    // Update user's last_login timestamp
    public function updateUserLastLogin($userId) {
        $stmt = $this->conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        if (!$stmt) return false;
        $stmt->bind_param("i", $userId);
        $result = $stmt->execute();
        $stmt->close();
        return $result;
    }
}
