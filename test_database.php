<?php
// Simple database test script
require_once 'includes/config.php';
require_once 'includes/database.php';

echo "<h2>KFT Fitness Database Test</h2>";

try {
    // Initialize database
    $db = new Database();
    $conn = $db->getConnection();

    echo "<p style='color: green;'>✓ Database connection successful!</p>";

    // Test 1: Check admin_users table
    $result = $conn->query("SELECT COUNT(*) as count FROM admin_users");
    $adminCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Admin users table: {$adminCount} admin(s) found</p>";

    // Test 2: Check users table
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    $userCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Users table: {$userCount} user(s) found</p>";

    // Test 3: Check courses table
    $result = $conn->query("SELECT COUNT(*) as count FROM courses");
    $courseCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Courses table: {$courseCount} course(s) found</p>";

    // Test 4: Check settings table
    $result = $conn->query("SELECT COUNT(*) as count FROM settings");
    $settingsCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Settings table: {$settingsCount} setting(s) found</p>";

    // Test 5: Check permissions table
    $result = $conn->query("SELECT COUNT(*) as count FROM admin_permissions");
    $permissionsCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Admin permissions table: {$permissionsCount} permission(s) found</p>";

    // Test 6: Check sample data
    $result = $conn->query("SELECT COUNT(*) as count FROM courses");
    $courseCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Sample courses: {$courseCount} course(s) found</p>";

    $result = $conn->query("SELECT COUNT(*) as count FROM motivational_quotes");
    $quotesCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Motivational quotes: {$quotesCount} quote(s) found</p>";

    $result = $conn->query("SELECT COUNT(*) as count FROM workout_programs");
    $programCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Workout programs: {$programCount} program(s) found</p>";

    // Test course pricing
    $result = $conn->query("SELECT COUNT(*) as count FROM courses WHERE price > 0");
    $paidCourseCount = $result->fetch_assoc()['count'];
    echo "<p>✓ Paid courses: {$paidCourseCount} course(s) with pricing</p>";

    // Test 7: List all tables
    $result = $conn->query("SHOW TABLES");
    $tables = [];
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }
    echo "<p>✓ Total tables: " . count($tables) . "</p>";
    echo "<details><summary>Click to view all tables</summary>";
    echo "<p>Tables: " . implode(', ', $tables) . "</p>";
    echo "</details>";

    // Test 8: Check admin user credentials
    $result = $conn->query("SELECT username, email, role FROM admin_users WHERE username = 'admin'");
    if ($result->num_rows > 0) {
        $admin = $result->fetch_assoc();
        echo "<p style='color: blue;'>✓ Default admin user found:</p>";
        echo "<ul>";
        echo "<li>Username: {$admin['username']}</li>";
        echo "<li>Email: {$admin['email']}</li>";
        echo "<li>Role: {$admin['role']}</li>";
        echo "<li>Password: admin123</li>";
        echo "<li>PIN: 1234</li>";
        echo "</ul>";
    }

    echo "<h3 style='color: green;'>✅ All database tests passed!</h3>";
    echo "<p><a href='index.php'>← Back to Admin Panel</a></p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database test failed: " . $e->getMessage() . "</p>";
}
?>
