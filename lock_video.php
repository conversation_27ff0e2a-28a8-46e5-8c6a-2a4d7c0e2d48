<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Initialize auth
    $auth = new Auth();

    // Check if user is logged in
    if (!$auth->isLoggedIn()) {
        Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
        Utilities::redirect('login.php');
        exit;
    }

    // Check if user has admin role
    if (!$auth->hasRole('admin')) {
        Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
        Utilities::redirect('index.php');
        exit;
    }

    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Utilities::setFlashMessage('error', 'Invalid request method.');
        Utilities::redirect('users.php');
        exit;
    }

    // Get parameters
    $videoId = isset($_POST['video_id']) ? intval($_POST['video_id']) : 0;
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $courseId = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;

    // Validate parameters
    if ($videoId <= 0 || $userId <= 0 || $courseId <= 0) {
        Utilities::setFlashMessage('error', 'Invalid parameters.');
        Utilities::redirect('users.php');
        exit;
    }

    // Initialize database
    $db = new Database();
    $conn = $db->getConnection();

    // Check if video exists and belongs to the course
    $videoQuery = "SELECT * FROM course_videos WHERE id = ? AND course_id = ?";
    $videoStmt = $conn->prepare($videoQuery);
    $videoStmt->bind_param("ii", $videoId, $courseId);
    $videoStmt->execute();
    $videoResult = $videoStmt->get_result();

    if ($videoResult->num_rows === 0) {
        Utilities::setFlashMessage('error', 'Video not found or does not belong to the specified course.');
        Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
        exit;
    }

    // Check if user is enrolled in the course
    $enrollmentQuery = "SELECT * FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
    $enrollmentStmt = $conn->prepare($enrollmentQuery);
    $enrollmentStmt->bind_param("ii", $userId, $courseId);
    $enrollmentStmt->execute();
    $enrollmentResult = $enrollmentStmt->get_result();

    if ($enrollmentResult->num_rows === 0) {
        Utilities::setFlashMessage('error', 'User is not enrolled in this course.');
        Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
        exit;
    }

    // Check if a record exists in user_video_progress
    $checkQuery = "SELECT * FROM user_video_progress WHERE user_id = ? AND video_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("ii", $userId, $videoId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        // Insert a new record with is_unlocked=0
        $insertQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked) VALUES (?, ?, 0)";
        $insertStmt = $conn->prepare($insertQuery);
        $insertStmt->bind_param("ii", $userId, $videoId);
        $insertStmt->execute();
    } else {
        // Update existing record to lock the video
        $lockQuery = "UPDATE user_video_progress SET is_unlocked = 0 WHERE user_id = ? AND video_id = ?";
        $lockStmt = $conn->prepare($lockQuery);
        $lockStmt->bind_param("ii", $userId, $videoId);
        $lockStmt->execute();
    }

    // Log the action
    $logQuery = "INSERT INTO admin_activity_log (admin_id, action, details, created_at) VALUES (?, 'lock_video', ?, NOW())";
    $logStmt = $conn->prepare($logQuery);
    $details = json_encode([
        'user_id' => $userId,
        'video_id' => $videoId,
        'course_id' => $courseId
    ]);
    $logStmt->bind_param("is", $auth->getUserId(), $details);
    $logStmt->execute();

    // Always set success message and redirect
    Utilities::setFlashMessage('success', 'Video has been locked successfully.');
    Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
} catch (Exception $e) {
    error_log('Lock error: ' . $e->getMessage());
    Utilities::setFlashMessage('error', 'An error occurred: ' . $e->getMessage());
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $courseId = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    Utilities::redirect("video_analytics.php?user_id=$userId&course_id=$courseId");
    exit;
} 