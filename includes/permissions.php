<?php
/**
 * Permission helper functions
 */

/**
 * Check if the current user has a specific permission
 *
 * @param string $permissionSlug The permission slug to check
 * @return bool Whether the user has the permission
 */
function hasPermission($permissionSlug) {
    global $auth;
    return $auth->hasPermission($permissionSlug);
}

/**
 * Check if the current user has permission to access a page
 *
 * @param string $requiredPermission The permission slug required to access the page
 * @param bool $redirectOnFailure Whether to redirect to unauthorized page on failure
 * @return bool Whether the user has permission
 */
function checkPagePermission($requiredPermission, $redirectOnFailure = true) {
    global $auth;
    return $auth->checkPagePermission($requiredPermission, $redirectOnFailure);
}

/**
 * Get all permissions for the current user as an associative array
 *
 * @return array Associative array of permission slugs => true
 */
function getPermissionsArray() {
    global $auth;
    return $auth->getPermissionsArray();
}

/**
 * Redirect to unauthorized page
 */
function redirectToUnauthorized() {
    Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
    Utilities::redirect('unauthorized.php');
    exit;
}

/**
 * Define page permissions
 *
 * This array maps page filenames to required permission slugs
 * Add new pages and their required permissions here
 *
 * @return array Associative array of page => permission
 */
function getPagePermissions() {
    return [
        // User management
        'users.php' => 'manage_users',
        'user_add.php' => 'manage_users',
        'user_edit.php' => 'manage_users',
        'user_view.php' => 'manage_users',
        'pending_users.php' => 'manage_users',

        // Course management
        'courses.php' => 'manage_courses',
        'course_add.php' => 'manage_courses',
        'course_edit.php' => 'manage_courses',
        'course_assign.php' => 'manage_courses',

        // Workout management
        'workouts.php' => 'manage_workouts',
        'workout_add.php' => 'manage_workouts',
        'workout_edit.php' => 'manage_workouts',

        // Quote management
        'quotes.php' => 'manage_quotes',
        'quote_add.php' => 'manage_quotes',
        'quote_edit.php' => 'manage_quotes',
        'quote_settings.php' => 'manage_quotes',

        // Reports
        'reports.php' => 'view_reports',

        // Settings
        'settings.php' => 'manage_settings',

        // Staff management
        'admin_add.php' => 'manage_staff',
        'admin_edit.php' => 'manage_staff',

        // Food database
        'food_items.php' => 'manage_food_database',
        'food_item_add.php' => 'manage_food_database',
        'food_item_edit.php' => 'manage_food_database',

        // Chat
        'chat.php' => 'chat_with_users',

        // Notifications - restricted to super_admin and manager roles only
        // 'notifications.php' => 'view_notifications', // Commented out as we now check roles directly

        // Water reminders
        'water_reminders.php' => 'manage_settings',
    ];
}

/**
 * Check if the current page requires permission
 *
 * @return bool Whether the current page requires permission
 */
function currentPageRequiresPermission() {
    $currentPage = basename($_SERVER['PHP_SELF']);
    $pagePermissions = getPagePermissions();

    return isset($pagePermissions[$currentPage]);
}

/**
 * Get the permission required for the current page
 *
 * @return string|null The permission slug required for the current page, or null if none
 */
function getRequiredPermissionForCurrentPage() {
    $currentPage = basename($_SERVER['PHP_SELF']);
    $pagePermissions = getPagePermissions();

    return $pagePermissions[$currentPage] ?? null;
}
