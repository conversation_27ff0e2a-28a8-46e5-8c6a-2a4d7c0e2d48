<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isStaff = $auth->hasRole('staff');
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');

// Handle water reminder status toggle
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $reminderId = (int)$_GET['toggle'];

    // Get current status
    $statusQuery = "SELECT is_active FROM water_reminders WHERE id = ?";
    $stmt = $conn->prepare($statusQuery);
    $stmt->bind_param("i", $reminderId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $reminder = $result->fetch_assoc();
        $newStatus = $reminder['is_active'] ? 0 : 1;

        // Update status
        $updateQuery = "UPDATE water_reminders SET is_active = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ii", $newStatus, $reminderId);

        if ($stmt->execute()) {
            $statusText = $newStatus ? 'activated' : 'deactivated';
            Utilities::setFlashMessage('success', "Water reminder has been $statusText successfully.");
        } else {
            Utilities::setFlashMessage('danger', 'Failed to update water reminder status.');
        }
    }

    Utilities::redirect('water_reminders.php');
}

// Get all water reminders with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? $db->escapeString($_GET['search']) : '';
$status = isset($_GET['status']) ? $db->escapeString($_GET['status']) : '';

$searchCondition = '';
$params = [];
$paramTypes = '';

// For staff members, only show their assigned users
if ($isStaff) {
    $searchCondition = " WHERE u.assigned_staff_id = ?";
    $params[] = $currentAdminId;
    $paramTypes .= 'i';
}

if (!empty($search)) {
    $searchCondition = empty($searchCondition) ?
        " WHERE (u.name LIKE ? OR u.username LIKE ?)" :
        $searchCondition . " AND (u.name LIKE ? OR u.username LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $paramTypes .= 'ss';
}

if ($status === '1' || $status === '0') {
    $searchCondition = empty($searchCondition) ?
        " WHERE wr.is_active = ?" :
        $searchCondition . " AND wr.is_active = ?";
    $params[] = $status;
    $paramTypes .= 'i';
}

// Count total water reminders
$countQuery = "SELECT COUNT(*) as total
               FROM water_reminders wr
               JOIN users u ON wr.user_id = u.id" . $searchCondition;
$stmt = $conn->prepare($countQuery);

if (!empty($params)) {
    $stmt->bind_param($paramTypes, ...$params);
}

$stmt->execute();
$countResult = $stmt->get_result();
$totalReminders = $countResult->fetch_assoc()['total'];
$totalPages = ceil($totalReminders / $limit);

// Check if we need to add the is_verified column to the water_reminders table
$checkColumnQuery = "SHOW COLUMNS FROM water_reminders LIKE 'is_verified'";
$checkColumnResult = $conn->query($checkColumnQuery);

if ($checkColumnResult->num_rows === 0) {
    // Add the is_verified and verified_at columns
    $alterTableQuery = "ALTER TABLE water_reminders
                       ADD COLUMN is_verified TINYINT(1) NOT NULL DEFAULT 0,
                       ADD COLUMN verified_at DATETIME NULL";
    $conn->query($alterTableQuery);
}

// Get water reminders
$remindersQuery = "SELECT wr.*, u.name as user_name, u.username
                  FROM water_reminders wr
                  JOIN users u ON wr.user_id = u.id" .
                  $searchCondition .
                  " ORDER BY u.name ASC LIMIT ? OFFSET ?";

$stmt = $conn->prepare($remindersQuery);

if (!empty($params)) {
    $params[] = $limit;
    $params[] = $offset;
    $paramTypes .= 'ii';
    $stmt->bind_param($paramTypes, ...$params);
} else {
    $stmt->bind_param('ii', $limit, $offset);
}

$stmt->execute();
$remindersResult = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Water Reminders</h1>
</div>

<!-- Search and Filter -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form action="water_reminders.php" method="get" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" placeholder="Search by user name or username" value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All</option>
                    <option value="1" <?php echo $status === '1' ? 'selected' : ''; ?>>Active</option>
                    <option value="0" <?php echo $status === '0' ? 'selected' : ''; ?>>Inactive</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <div class="d-flex w-100">
                    <button type="submit" class="btn btn-primary flex-grow-1 me-2">
                        <i class="fas fa-search me-2"></i> Search
                    </button>
                    <a href="water_reminders.php" class="btn btn-outline-secondary">
                        <i class="fas fa-redo"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Water Reminders Table -->
<div class="card border-0 shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Interval</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th>Status</th>
                        <th>Verification</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($remindersResult && $remindersResult->num_rows > 0): ?>
                        <?php while ($reminder = $remindersResult->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo $reminder['id']; ?></td>
                                <td>
                                    <a href="user_view.php?id=<?php echo $reminder['user_id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($reminder['user_name']); ?>
                                        <small class="text-muted">(<?php echo htmlspecialchars($reminder['username']); ?>)</small>
                                    </a>
                                </td>
                                <td>Every <?php echo $reminder['interval_hours']; ?> hour<?php echo $reminder['interval_hours'] > 1 ? 's' : ''; ?></td>
                                <td><?php echo date('h:i A', strtotime($reminder['start_time'])); ?></td>
                                <td><?php echo date('h:i A', strtotime($reminder['end_time'])); ?></td>
                                <td>
                                    <?php if ($reminder['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($reminder['is_verified']) && $reminder['is_verified']): ?>
                                        <span class="badge bg-success">Verified</span>
                                        <?php if (isset($reminder['verified_at']) && $reminder['verified_at']): ?>
                                            <small class="d-block text-muted"><?php echo date('M d, Y', strtotime($reminder['verified_at'])); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Not Verified</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="water_reminder_edit.php?id=<?php echo $reminder['id']; ?>" class="btn btn-outline-secondary" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="water_reminder_verify.php?id=<?php echo $reminder['id']; ?>" class="btn btn-outline-info" data-bs-toggle="tooltip" title="Verify">
                                            <i class="fas fa-check-circle"></i>
                                        </a>
                                        <a href="water_reminders.php?toggle=<?php echo $reminder['id']; ?>" class="btn btn-outline-<?php echo $reminder['is_active'] ? 'warning' : 'success'; ?>" data-bs-toggle="tooltip" title="<?php echo $reminder['is_active'] ? 'Deactivate' : 'Activate'; ?>">
                                            <i class="fas fa-<?php echo $reminder['is_active'] ? 'toggle-off' : 'toggle-on'; ?>"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">No water reminders found</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if ($totalPages > 1): ?>
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>

        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>

        <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    </ul>
</nav>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
