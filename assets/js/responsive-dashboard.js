/**
 * Comprehensive Responsive Admin Dashboard JavaScript
 * Handles sidebar interactions, bottom navigation, and responsive behavior
 */

class ResponsiveDashboard {
  constructor() {
    this.sidebar = document.getElementById('sidebar-wrapper');
    this.wrapper = document.getElementById('wrapper');
    this.pageContent = document.getElementById('page-content-wrapper');
    this.sidebarClose = document.getElementById('sidebarClose');
    this.sidebarToggle = document.getElementById('sidebarToggle');
    this.sidebarCollapseToggle = document.getElementById('sidebarCollapseToggle');
    this.bottomNav = document.querySelector('.bottom-nav');
    this.overlay = null;
    
    this.isMobile = window.innerWidth <= 767.98;
    this.isTablet = window.innerWidth >= 768 && window.innerWidth <= 991.98;
    this.isDesktop = window.innerWidth >= 992;
    this.sidebarCollapsed = false;
    
    this.init();
  }
  
  init() {
    this.createOverlay();
    this.setupEventListeners();
    this.setupBottomNavigation();
    this.handleInitialState();
    this.setupResizeHandler();
    this.setupKeyboardNavigation();
    this.initializeAnimations();
  }
  
  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.className = 'sidebar-overlay';
    document.body.appendChild(this.overlay);
    
    this.overlay.addEventListener('click', () => {
      this.hideSidebar();
    });
  }
  
  setupEventListeners() {
    // Sidebar close button
    if (this.sidebarClose) {
      this.sidebarClose.addEventListener('click', (e) => {
        e.preventDefault();
        this.hideSidebar();
      });
    }
    
    // Sidebar toggle button
    if (this.sidebarToggle) {
      this.sidebarToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleSidebar();
      });
    }

    // Sidebar collapse toggle button
    if (this.sidebarCollapseToggle) {
      this.sidebarCollapseToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleSidebarCollapse();

        // Update icon
        const icon = this.sidebarCollapseToggle.querySelector('i');
        if (this.sidebarCollapsed) {
          icon.className = 'fas fa-chevron-right';
          this.sidebarCollapseToggle.title = 'Expand Sidebar';
        } else {
          icon.className = 'fas fa-chevron-left';
          this.sidebarCollapseToggle.title = 'Collapse Sidebar';
        }
      });
    }
    
    // Sidebar menu items
    const menuItems = document.querySelectorAll('.sidebar-menu-item');
    menuItems.forEach(item => {
      item.addEventListener('click', (e) => {
        if (this.isMobile) {
          // On mobile, close sidebar after navigation
          setTimeout(() => {
            this.hideSidebar();
          }, 150);
        }
      });
    });
  }
  
  setupBottomNavigation() {
    if (!this.bottomNav) return;
    
    const bottomNavItems = this.bottomNav.querySelectorAll('.bottom-nav-item');
    const currentPage = window.location.pathname.split('/').pop();
    
    // Set active state based on current page
    bottomNavItems.forEach(item => {
      const href = item.getAttribute('href');
      if (href === currentPage) {
        item.classList.add('active');
      }
      
      // Add click animation
      item.addEventListener('click', (e) => {
        // Remove active from all items
        bottomNavItems.forEach(navItem => navItem.classList.remove('active'));
        // Add active to clicked item
        item.classList.add('active');
        
        // Add click animation
        item.style.transform = 'scale(0.95)';
        setTimeout(() => {
          item.style.transform = '';
        }, 150);
      });
    });
  }
  
  handleInitialState() {
    this.updateBreakpoints();
    
    if (this.isDesktop) {
      this.showSidebar();
    } else {
      this.hideSidebar();
    }
  }
  
  setupResizeHandler() {
    let resizeTimer;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(() => {
        this.updateBreakpoints();
        this.handleResponsiveChanges();
      }, 250);
    });
  }
  
  updateBreakpoints() {
    const width = window.innerWidth;
    this.isMobile = width <= 767.98;
    this.isTablet = width >= 768 && width <= 991.98;
    this.isDesktop = width >= 992;
  }
  
  handleResponsiveChanges() {
    if (this.isDesktop) {
      this.showSidebar();
      this.hideOverlay();
    } else if (this.isTablet || this.isMobile) {
      this.hideSidebar();
    }
  }
  
  showSidebar() {
    if (this.sidebar) {
      this.sidebar.classList.add('show');
      this.wrapper.classList.add('toggled');
      
      if (this.isMobile || this.isTablet) {
        this.showOverlay();
      }
      
      // Update page content margins
      this.updatePageContentLayout();
      
      // Add animation class
      this.sidebar.classList.add('slide-in-left');
      setTimeout(() => {
        this.sidebar.classList.remove('slide-in-left');
      }, 500);
    }
  }
  
  hideSidebar() {
    if (this.sidebar) {
      this.sidebar.classList.remove('show');
      this.wrapper.classList.remove('toggled');
      this.hideOverlay();
      
      // Update page content margins
      this.updatePageContentLayout();
    }
  }
  
  toggleSidebar() {
    if (this.sidebar.classList.contains('show')) {
      this.hideSidebar();
    } else {
      this.showSidebar();
    }
  }
  
  showOverlay() {
    if (this.overlay) {
      this.overlay.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
  }
  
  hideOverlay() {
    if (this.overlay) {
      this.overlay.classList.remove('show');
      document.body.style.overflow = '';
    }
  }
  
  updatePageContentLayout() {
    if (!this.pageContent) return;
    
    if (this.isDesktop && this.sidebar.classList.contains('show')) {
      const sidebarWidth = this.sidebarCollapsed ? '60px' : '280px';
      this.pageContent.style.marginLeft = sidebarWidth;
      this.pageContent.style.width = `calc(100% - ${sidebarWidth})`;
    } else {
      this.pageContent.style.marginLeft = '0';
      this.pageContent.style.width = '100%';
    }
  }
  
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // ESC key closes sidebar on mobile/tablet
      if (e.key === 'Escape' && (this.isMobile || this.isTablet)) {
        this.hideSidebar();
      }
      
      // Alt + S toggles sidebar
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        this.toggleSidebar();
      }
    });
  }
  
  initializeAnimations() {
    // Add page transition animation
    const mainContent = document.querySelector('.container-fluid');
    if (mainContent) {
      mainContent.classList.add('page-transition');
    }
    
    // Animate cards on scroll
    this.setupScrollAnimations();
  }
  
  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('bounce-in');
        }
      });
    }, observerOptions);
    
    // Observe dashboard cards
    const cards = document.querySelectorAll('.dashboard-card, .premium-card');
    cards.forEach(card => {
      observer.observe(card);
    });
  }
  
  // Public methods for external use
  collapseSidebar() {
    if (this.sidebar && this.isDesktop) {
      this.sidebar.classList.add('collapsed');
      this.sidebarCollapsed = true;
      this.updatePageContentLayout();
    }
  }
  
  expandSidebar() {
    if (this.sidebar && this.isDesktop) {
      this.sidebar.classList.remove('collapsed');
      this.sidebarCollapsed = false;
      this.updatePageContentLayout();
    }
  }
  
  toggleSidebarCollapse() {
    if (this.sidebarCollapsed) {
      this.expandSidebar();
    } else {
      this.collapseSidebar();
    }
  }
}

// Initialize the responsive dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.responsiveDashboard = new ResponsiveDashboard();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResponsiveDashboard;
}
