/**
 * KFT Fitness - User Add Form JavaScript
 * Handles the user add form functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const quickForm = document.getElementById('quickAddForm');
    const detailedForm = document.getElementById('detailedAddForm');
    const nameInput = document.getElementById('name');
    const detailedNameInput = document.getElementById('detailed_name');
    const usernameInput = document.getElementById('username');
    const detailedUsernameInput = document.getElementById('detailed_username');
    const generateUsernameBtn = document.getElementById('generateUsername');
    const generateDetailedUsernameBtn = document.getElementById('generateDetailedUsername');
    const showDetailedFormBtn = document.getElementById('showDetailedForm');
    const showQuickFormBtn = document.getElementById('showQuickForm');
    const userTypeRadios = document.querySelectorAll('input[name="user_type"]');
    const isPremiumHidden = document.getElementById('is_premium');
    const detailedIsPremium = document.getElementById('detailed_is_premium');
    const phoneInput = document.getElementById('phone_number');
    const detailedPhoneInput = document.getElementById('detailed_phone_number');
    
    // Tab elements
    const quickTab = document.getElementById('quick-tab');
    const detailedTab = document.getElementById('detailed-tab');
    
    // Initialize Bootstrap tabs
    const tabs = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            // Sync form data between tabs when switching
            if (event.target.id === 'detailed-tab') {
                syncQuickToDetailed();
            } else if (event.target.id === 'quick-tab') {
                syncDetailedToQuick();
            }
        });
    });
    
    // Button click handlers
    if (showDetailedFormBtn) {
        showDetailedFormBtn.addEventListener('click', function() {
            const detailedTabInstance = new bootstrap.Tab(detailedTab);
            detailedTabInstance.show();
        });
    }
    
    if (showQuickFormBtn) {
        showQuickFormBtn.addEventListener('click', function() {
            const quickTabInstance = new bootstrap.Tab(quickTab);
            quickTabInstance.show();
        });
    }
    
    // Generate username from name
    function generateUsername(name) {
        if (!name) return '';
        
        // Remove special characters and spaces, convert to lowercase
        let username = name.toLowerCase()
            .replace(/[^\w\s]/gi, '')  // Remove special characters
            .replace(/\s+/g, '');      // Remove spaces
        
        // Add a random number (1000-9999) to make it more unique
        username += Math.floor(Math.random() * 9000 + 1000);
        
        return username;
    }
    
    // Generate username button handlers
    if (generateUsernameBtn) {
        generateUsernameBtn.addEventListener('click', function() {
            const name = nameInput.value.trim();
            if (name) {
                usernameInput.value = generateUsername(name);
            } else {
                // Show error if name is empty
                nameInput.classList.add('is-invalid');
                setTimeout(() => nameInput.classList.remove('is-invalid'), 3000);
            }
        });
    }
    
    if (generateDetailedUsernameBtn) {
        generateDetailedUsernameBtn.addEventListener('click', function() {
            const name = detailedNameInput.value.trim();
            if (name) {
                detailedUsernameInput.value = generateUsername(name);
            } else {
                // Show error if name is empty
                detailedNameInput.classList.add('is-invalid');
                setTimeout(() => detailedNameInput.classList.remove('is-invalid'), 3000);
            }
        });
    }
    
    // Auto-generate username when name is entered
    if (nameInput) {
        nameInput.addEventListener('blur', function() {
            const name = nameInput.value.trim();
            const username = usernameInput.value.trim();
            
            // Only auto-generate if username field is empty
            if (name && !username) {
                usernameInput.value = generateUsername(name);
            }
        });
    }
    
    if (detailedNameInput) {
        detailedNameInput.addEventListener('blur', function() {
            const name = detailedNameInput.value.trim();
            const username = detailedUsernameInput.value.trim();
            
            // Only auto-generate if username field is empty
            if (name && !username) {
                detailedUsernameInput.value = generateUsername(name);
            }
        });
    }
    
    // Handle user type radio buttons
    if (userTypeRadios.length > 0) {
        userTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'premium') {
                    isPremiumHidden.value = '1';
                } else {
                    isPremiumHidden.value = '0';
                }
            });
        });
    }
    
    // Sync data between quick and detailed forms
    function syncQuickToDetailed() {
        if (nameInput && detailedNameInput) {
            detailedNameInput.value = nameInput.value;
        }
        
        if (usernameInput && detailedUsernameInput) {
            detailedUsernameInput.value = usernameInput.value;
        }
        
        if (phoneInput && detailedPhoneInput) {
            detailedPhoneInput.value = phoneInput.value;
        }
        
        if (isPremiumHidden && detailedIsPremium) {
            detailedIsPremium.checked = isPremiumHidden.value === '1';
        }
    }
    
    function syncDetailedToQuick() {
        if (nameInput && detailedNameInput) {
            nameInput.value = detailedNameInput.value;
        }
        
        if (usernameInput && detailedUsernameInput) {
            usernameInput.value = detailedUsernameInput.value;
        }
        
        if (phoneInput && detailedPhoneInput) {
            phoneInput.value = detailedPhoneInput.value;
        }
        
        if (isPremiumHidden && detailedIsPremium) {
            isPremiumHidden.value = detailedIsPremium.checked ? '1' : '0';
            
            // Update radio buttons
            const premiumRadio = document.getElementById('user_type_premium');
            const basicRadio = document.getElementById('user_type_basic');
            
            if (detailedIsPremium.checked) {
                premiumRadio.checked = true;
                basicRadio.checked = false;
            } else {
                basicRadio.checked = true;
                premiumRadio.checked = false;
            }
        }
    }
    
    // Form validation
    function validateForm(form) {
        let isValid = true;
        
        // Get all required inputs
        const requiredInputs = form.querySelectorAll('[required]');
        
        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
                
                // Additional validation for phone number
                if (input.id === 'phone_number' || input.id === 'detailed_phone_number') {
                    if (!input.value.match(/^[0-9]{10}$/)) {
                        input.classList.add('is-invalid');
                        isValid = false;
                    }
                }
            }
        });
        
        return isValid;
    }
    
    // Form submission handlers
    if (quickForm) {
        quickForm.addEventListener('submit', function(e) {
            if (!validateForm(quickForm)) {
                e.preventDefault();
            }
        });
    }
    
    if (detailedForm) {
        detailedForm.addEventListener('submit', function(e) {
            if (!validateForm(detailedForm)) {
                e.preventDefault();
            }
        });
    }
    
    // Format phone number input to only allow digits
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    });
});
