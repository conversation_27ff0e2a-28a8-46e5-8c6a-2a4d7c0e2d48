<?php
require_once 'includes/header.php';

// Check if admin is logged in
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    Utilities::setFlashMessage('danger', 'You do not have permission to access this page.');
    Utilities::redirect('login.php');
}

// Check if user ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('danger', 'Invalid user ID.');
    Utilities::redirect('users.php');
}

$userId = (int)$_GET['id'];

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get user details
$userQuery = "SELECT id, username, name, email FROM users WHERE id = ?";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('danger', 'User not found.');
    Utilities::redirect('users.php');
}

$user = $result->fetch_assoc();

// Check if reset link exists in session
$resetLink = $_SESSION['reset_link'] ?? '';
$resetEmail = $_SESSION['reset_email'] ?? '';

// Clear session variables
unset($_SESSION['reset_link']);
unset($_SESSION['reset_email']);

// If no reset link, redirect back
if (empty($resetLink)) {
    Utilities::setFlashMessage('danger', 'No reset link found. Please try again.');
    Utilities::redirect('user_reset_password.php?id=' . $userId);
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Password Reset Link</h1>
    <a href="users.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Users
    </a>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i> Password reset link has been generated successfully for <strong><?php echo htmlspecialchars($user['name']); ?></strong> (Username: <strong><?php echo htmlspecialchars($user['username']); ?></strong>).
        </div>
        
        <div class="mb-4">
            <h5>Reset Link</h5>
            <div class="alert alert-info">
                <p>The following link can be used to reset the password:</p>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="resetLink" value="<?php echo htmlspecialchars($resetLink); ?>" readonly>
                    <button class="btn btn-outline-secondary" type="button" id="copyLink">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
                <p class="small text-muted mb-0">This link will expire in 1 hour.</p>
            </div>
        </div>
        
        <?php if (!empty($resetEmail)): ?>
        <div class="mb-4">
            <h5>Send Email</h5>
            <div class="alert alert-info">
                <p>In a production environment, an email would be sent to:</p>
                <p><strong><?php echo htmlspecialchars($resetEmail); ?></strong></p>
                <p class="small text-muted mb-0">The email would contain the reset link and instructions for the user.</p>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="users.php" class="btn btn-primary">
                <i class="fas fa-users me-2"></i> Return to Users
            </a>
            <a href="user_reset_password.php?id=<?php echo $userId; ?>" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-redo me-2"></i> Generate New Link
            </a>
        </div>
    </div>
</div>

<script>
// Copy link to clipboard
document.getElementById('copyLink').addEventListener('click', function() {
    const resetLink = document.getElementById('resetLink');
    resetLink.select();
    resetLink.setSelectionRange(0, 99999); // For mobile devices
    
    navigator.clipboard.writeText(resetLink.value).then(function() {
        // Change button text temporarily
        const button = document.getElementById('copyLink');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
