<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to unenroll users from courses.');
    Utilities::redirect('index.php');
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $enrollmentId = isset($_POST['enrollment_id']) ? intval($_POST['enrollment_id']) : 0;
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    
    $errors = [];
    
    // Validate enrollment ID
    if ($enrollmentId <= 0) {
        $errors[] = 'Invalid enrollment ID.';
    }
    
    // Validate user ID
    if ($userId <= 0) {
        $errors[] = 'Invalid user ID.';
    }
    
    if (empty($errors)) {
        // Check if enrollment exists and belongs to the specified user
        $checkQuery = "SELECT e.id, e.course_id, c.title 
                      FROM user_course_enrollments e
                      JOIN courses c ON e.course_id = c.id
                      WHERE e.id = ? AND e.user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $enrollmentId, $userId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        
        if ($checkResult->num_rows === 0) {
            $errors[] = 'Enrollment not found or does not belong to this user.';
        } else {
            $enrollment = $checkResult->fetch_assoc();
            
            // Begin transaction
            $conn->begin_transaction();
            
            try {
                // Delete enrollment
                $deleteEnrollmentQuery = "DELETE FROM user_course_enrollments WHERE id = ?";
                $deleteEnrollmentStmt = $conn->prepare($deleteEnrollmentQuery);
                $deleteEnrollmentStmt->bind_param("i", $enrollmentId);
                $deleteEnrollmentStmt->execute();
                
                // Delete video progress for this course
                $deleteProgressQuery = "DELETE uvp FROM user_video_progress uvp
                                      JOIN course_videos cv ON uvp.video_id = cv.id
                                      WHERE uvp.user_id = ? AND cv.course_id = ?";
                $deleteProgressStmt = $conn->prepare($deleteProgressQuery);
                $deleteProgressStmt->bind_param("ii", $userId, $enrollment['course_id']);
                $deleteProgressStmt->execute();
                
                // Commit transaction
                $conn->commit();
                
                Utilities::setFlashMessage('success', 'User has been unenrolled from the course "' . $enrollment['title'] . '" successfully.');
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $errors[] = 'Failed to unenroll user from course: ' . $e->getMessage();
            }
        }
    }
    
    if (!empty($errors)) {
        foreach ($errors as $error) {
            Utilities::setFlashMessage('error', $error);
        }
    }
    
    // Redirect back to user edit page
    Utilities::redirect('user_edit.php?id=' . $userId);
    exit;
} else {
    // If not a POST request, redirect to users page
    Utilities::redirect('users.php');
    exit;
}
