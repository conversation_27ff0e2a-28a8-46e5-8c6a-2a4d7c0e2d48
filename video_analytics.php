<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Set timezone
date_default_timezone_set('UTC');

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
    Utilities::redirect('index.php');
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get course ID and user ID from the URL parameters
$courseId = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

// Redirect to users.php if no IDs are provided
if ($courseId === 0 || $userId === 0) {
    Utilities::setFlashMessage('error', 'Please select a user first.');
    Utilities::redirect('users.php');
    exit;
}

// Get analytics data if course ID and user ID are provided
$analytics = [];
$videos = [];
$selectedCourse = null;
$selectedUser = null;

if ($courseId > 0 && $userId > 0) {
    // Get course details
    $courseQuery = "SELECT * FROM courses WHERE id = ?";
    $courseStmt = $conn->prepare($courseQuery);
    $courseStmt->bind_param("i", $courseId);
    $courseStmt->execute();
    $courseResult = $courseStmt->get_result();
    $selectedCourse = $courseResult->fetch_assoc();

    // Get user details
    $userQuery = "SELECT * FROM users WHERE id = ?";
    $userStmt = $conn->prepare($userQuery);
    $userStmt->bind_param("i", $userId);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    $selectedUser = $userResult->fetch_assoc();

    // Get enrollment details
    $enrollmentQuery = "SELECT e.*,
                       DATEDIFF(CURRENT_DATE, e.start_date) as days_enrolled,
                       DATEDIFF(e.end_date, CURRENT_DATE) as days_remaining,
                       DATEDIFF(CURRENT_DATE, e.start_date) DIV 7 as weeks_enrolled
                       FROM user_course_enrollments e
                       WHERE e.user_id = ? AND e.course_id = ?";
    $enrollmentStmt = $conn->prepare($enrollmentQuery);
    $enrollmentStmt->bind_param("ii", $userId, $courseId);
    $enrollmentStmt->execute();
    $enrollmentResult = $enrollmentStmt->get_result();
    $enrollment = $enrollmentResult->fetch_assoc();

    // Get videos with progress and analytics
    $videosQuery = "SELECT v.*,
                   IFNULL(p.is_unlocked, 0) as is_unlocked,
                   IFNULL(p.is_completed, 0) as is_completed,
                   p.unlock_date, p.completion_date,
                   p.watch_duration_seconds, p.last_position_seconds,
                   va.total_views, va.total_completions,
                   COALESCE(va.total_watch_duration, 0) as total_watch_duration,
                   COALESCE(va.average_watch_duration, 0) as average_watch_duration
                   FROM course_videos v
                   LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
                   LEFT JOIN video_analytics va ON v.id = va.video_id
                   WHERE v.course_id = ?
                   ORDER BY v.week_number, v.sequence_number";
    $videosStmt = $conn->prepare($videosQuery);
    $videosStmt->bind_param("ii", $userId, $courseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();

    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = $video;
    }

    // Get real-time analytics
    $analyticsQuery = "SELECT
                      COUNT(DISTINCT al.id) as total_views,
                      SUM(al.watch_duration_seconds) as total_watch_duration,
                      COUNT(DISTINCT CASE WHEN al.is_completed = 1 THEN al.video_id END) as completed_videos,
                      MAX(al.timestamp) as last_activity
                      FROM video_access_logs al
                      JOIN course_videos cv ON al.video_id = cv.id
                      WHERE al.user_id = ? AND cv.course_id = ?";
    $analyticsStmt = $conn->prepare($analyticsQuery);
    $analyticsStmt->bind_param("ii", $userId, $courseId);
    $analyticsStmt->execute();
    $analyticsResult = $analyticsStmt->get_result();
    $analytics = $analyticsResult->fetch_assoc();

    // Get detailed activity timeline
    $timelineQuery = "SELECT
                     al.*,
                     cv.title as video_title,
                     cv.week_number,
                     al.watch_duration_seconds,
                     al.last_position_seconds,
                     al.is_completed,
                     al.action,
                     al.device_info
                     FROM video_access_logs al
                     JOIN course_videos cv ON al.video_id = cv.id
                     WHERE al.user_id = ? AND cv.course_id = ?
                     ORDER BY al.timestamp DESC
                     LIMIT 50";
    $timelineStmt = $conn->prepare($timelineQuery);
    $timelineStmt->bind_param("ii", $userId, $courseId);
    $timelineStmt->execute();
    $timelineResult = $timelineStmt->get_result();
    $timeline = [];

    while ($activity = $timelineResult->fetch_assoc()) {
        $timeline[] = $activity;
    }
}

// After fetching $videos
$completedCount = 0;
foreach ($videos as $video) {
    if (!empty($video['is_completed'])) {
        $completedCount++;
    }
}
$totalVideos = count($videos);

$totalWatchSeconds = 0;
foreach ($videos as $video) {
    if (!empty($video['watch_duration_seconds'])) {
        $totalWatchSeconds += (int)$video['watch_duration_seconds'];
    }
}
$hours = floor($totalWatchSeconds / 3600);
$minutes = floor(($totalWatchSeconds % 3600) / 60);

// After fetching $videos
$lastActivity = null;
foreach ($videos as $video) {
    foreach (['completion_date', 'unlock_date'] as $field) {
        if (!empty($video[$field]) && ($lastActivity === null || strtotime($video[$field]) > strtotime($lastActivity))) {
            $lastActivity = $video[$field];
        }
    }
}

// After fetching $videos and $timeline
$activityTimeline = [];
foreach ($videos as $video) {
    if (!empty($video['completion_date'])) {
        $activityTimeline[] = [
            'timestamp' => $video['completion_date'],
            'video_title' => $video['title'],
            'is_completed' => true,
            'action' => 'completed',
            'watch_duration_seconds' => $video['watch_duration_seconds'] ?? 0,
            'device_info' => null
        ];
    }
    if (!empty($video['unlock_date'])) {
        $activityTimeline[] = [
            'timestamp' => $video['unlock_date'],
            'video_title' => $video['title'],
            'is_completed' => false,
            'action' => 'unlocked',
            'watch_duration_seconds' => $video['watch_duration_seconds'] ?? 0,
            'device_info' => null
        ];
    }
}
// Add activities from video_access_logs timeline
foreach ($timeline as $activity) {
    $activityTimeline[] = [
        'timestamp' => $activity['timestamp'],
        'video_title' => $activity['video_title'],
        'is_completed' => $activity['is_completed'],
        'action' => $activity['action'],
        'watch_duration_seconds' => $activity['watch_duration_seconds'],
        'device_info' => $activity['device_info'] ?? null
    ];
}
// Sort by timestamp descending
usort($activityTimeline, function($a, $b) {
    return strtotime($b['timestamp']) - strtotime($a['timestamp']);
});
// Limit to 50
$activityTimeline = array_slice($activityTimeline, 0, 50);

// Include header
$pageTitle = 'Video Analytics Dashboard';
require_once 'includes/header.php';
?>

<!-- Custom CSS for modern design -->
<style>
:root {
    --primary-color: #4f46e5;
    --secondary-color: #818cf8;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f3f4f6;
}

.analytics-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.analytics-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.views { background: var(--primary-color); }
.stat-icon.completed { background: var(--success-color); }
.stat-icon.time { background: var(--warning-color); }
.stat-icon.activity { background: var(--secondary-color); }

.stat-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--dark-color);
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
}

.video-card {
    background: white;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.video-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.video-title {
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.video-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.locked { background: #e5e7eb; color: #374151; }
.status-badge.completed { background: #dcfce7; color: #166534; }
.status-badge.in-progress { background: #fef3c7; color: #92400e; }

.progress-bar {
    height: 6px;
    background: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-bar-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e5e7eb;
    }
    
.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
    }

.timeline-item::before {
    content: '';
    position: absolute;
    left: -2rem;
    top: 0;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: var(--primary-color);
    border: 2px solid white;
}

.timeline-content {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-time {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.timeline-details {
    font-size: 0.875rem;
    color: #4b5563;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
}

.btn-lock {
    background: #f3f4f6;
    color: #374151;
}

.btn-unlock {
    background: #dcfce7;
    color: #166534;
}

.btn-complete {
    background: #dbeafe;
    color: #1e40af;
}

@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .stat-content h3 {
        font-size: 1.25rem;
    }
}

@media (max-width: 575.98px) {
    .stat-card, .video-card, .analytics-card, .timeline-content {
        padding: 0.75rem !important;
        border-radius: 10px !important;
    }
    .stat-content h3 {
        font-size: 1rem;
    }
    .video-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
    }
    .timeline {
        padding-left: 1rem;
    }
    .timeline-item::before {
        left: -1rem;
        width: 0.75rem;
        height: 0.75rem;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="analytics-header">
        <div class="container">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
                <div>
                    <h1 class="display-6 mb-2">Video Analytics</h1>
                    <?php if ($selectedUser && $selectedCourse): ?>
                        <p class="mb-0 text-white-50">
                            <?php echo htmlspecialchars($selectedUser['name']); ?> - 
                            <?php echo htmlspecialchars($selectedCourse['title']); ?>
                        </p>
                    <?php endif; ?>
                </div>
                <div class="header-actions mt-3 mt-md-0">
                    <?php if ($selectedUser): ?>
                        <a href="user_edit.php?id=<?php echo $selectedUser['id']; ?>" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-user-edit"></i>
                        </a>
                    <?php endif; ?>
                    <a href="users.php" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($selectedCourse && $selectedUser): ?>
        <!-- Analytics Overview Cards -->
        <div class="container">
            <div class="row g-3 mb-4">
                <div class="col-12 col-md-6 col-xl-3">
                    <div class="stat-card">
                        <div class="stat-icon views">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($analytics['total_views'] ?? 0); ?></h3>
                            <p>Total Views</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-xl-3">
                    <div class="stat-card">
                        <div class="stat-icon completed">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>
                                <?php
                                echo "$completedCount / $totalVideos";
                                ?>
                            </h3>
                            <p>Completed Videos</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-xl-3">
                    <div class="stat-card">
                        <div class="stat-icon time">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>
                                <?php
                                echo "$hours h $minutes m";
                                ?>
                            </h3>
                            <p>Total Watch Time</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-xl-3">
                    <div class="stat-card">
                        <div class="stat-icon activity">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3>
                                <?php
                                if ($lastActivity) {
                                    $dt = strtotime($lastActivity);
                                    if (date('H:i', $dt) === '00:00') {
                                        echo date('M d', $dt);
                                    } else {
                                        echo date('M d, H:i', $dt);
                                    }
                                } else {
                                    echo 'N/A';
                                }
                                ?>
                            </h3>
                            <p>Last Activity</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Progress Section -->
            <div class="row">
                <div class="col-12 col-lg-8">
                    <div class="analytics-card">
                        <div class="card-header bg-transparent border-0 py-3">
                            <h5 class="m-0 fw-bold text-dark">
                                <i class="fas fa-video me-2 text-primary"></i>Video Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($videos as $video): ?>
                                <div class="video-card">
                                    <div class="video-header">
                                        <h6 class="video-title"><?php echo htmlspecialchars($video['title']); ?></h6>
                                        <div class="video-status">
                                            <?php if (!$video['is_unlocked']): ?>
                                                <span class="status-badge locked">Locked</span>
                                            <?php elseif ($video['is_completed']): ?>
                                                <span class="status-badge completed">Completed</span>
                                            <?php else: ?>
                                                <span class="status-badge in-progress">In Progress</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if ($video['duration_minutes'] && $video['watch_duration_seconds']): ?>
                                        <?php
                                        $totalVideoSeconds = $video['duration_minutes'] * 60;
                                        $watchPercentage = min(100, round(($video['watch_duration_seconds'] / $totalVideoSeconds) * 100));
                                        ?>
                                        <div class="progress-bar">
                                            <div class="progress-bar-fill" style="width: <?php echo $watchPercentage; ?>%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <small class="text-muted">
                                                <?php echo floor($video['watch_duration_seconds'] / 60); ?>m / <?php echo $video['duration_minutes']; ?>m
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    <div class="action-buttons mt-2">
                                        <?php if (!$video['is_unlocked']): ?>
                                            <form action="unlock_video.php" method="POST" class="d-inline">
                                                <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                                <button type="submit" class="btn btn-action btn-unlock">
                                                    <i class="fas fa-unlock"></i> Unlock
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <form action="lock_video.php" method="POST" class="d-inline">
                                                <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                                <button type="submit" class="btn btn-action btn-lock">
                                                    <i class="fas fa-lock"></i> Lock
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        <?php if (!$video['is_completed']): ?>
                                            <form action="mark_video_completed.php" method="POST" class="d-inline">
                                                <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                                <button type="submit" class="btn btn-action btn-complete">
                                                    <i class="fas fa-check"></i> Mark Complete
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <!-- Recent Activity Timeline -->
                <div class="col-12 col-lg-4">
                    <div class="analytics-card">
                        <div class="card-header bg-transparent border-0 py-3">
                            <h5 class="m-0 fw-bold text-dark">
                                <i class="fas fa-history me-2 text-primary"></i>Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <?php foreach ($activityTimeline as $activity): ?>
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-time">
                                                <?php echo date('M d, H:i', strtotime($activity['timestamp'])); ?>
                                            </div>
                                            <div class="timeline-title">
                                                <?php echo htmlspecialchars($activity['video_title']); ?>
                                            </div>
                                            <div class="timeline-details">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span>
                                                        <i class="fas fa-clock text-info me-1"></i>
                                                        <?php if (!empty($activity['watch_duration_seconds']) && $activity['watch_duration_seconds'] > 0): ?>
                                                            <?php echo floor($activity['watch_duration_seconds'] / 60); ?>m
                                                        <?php endif; ?>
                                                    </span>
                                                    <?php if (!empty($activity['is_completed'])): ?>
                                                        <span class="badge bg-success">Completed</span>
                                                    <?php elseif ($activity['action'] === 'unlocked'): ?>
                                                        <span class="badge bg-primary">Unlocked</span>
                                                    <?php elseif ($activity['action'] === 'progress_update' && !empty($activity['watch_duration_seconds']) && $activity['watch_duration_seconds'] > 0): ?>
                                                        <span class="badge bg-info">Watched</span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if (!empty($activity['device_info'])): ?>
                                                    <small class="text-muted d-block mt-1">
                                                        <i class="fas fa-mobile-alt me-1"></i>
                                                        <?php echo json_decode($activity['device_info'], true)['device_name'] ?? 'Unknown Device'; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="container">
            <div class="alert alert-warning">
                No data available. Please select a user and course.
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- JavaScript for real-time updates -->
<script>
function updateAnalytics() {
    fetch(`api/get_video_analytics.php?user_id=<?php echo $userId; ?>&course_id=<?php echo $courseId; ?>`)
        .then(response => response.json())
        .then(data => {
            // Update stats
            document.querySelector('.stat-card:nth-child(1) .stat-content h3').textContent = data.total_views;
            document.querySelector('.stat-card:nth-child(2) .stat-content h3').textContent = `${data.completed_videos} / ${data.total_videos}`;
            
            const hours = Math.floor(data.total_watch_duration / 3600);
            const minutes = Math.floor((data.total_watch_duration % 3600) / 60);
            document.querySelector('.stat-card:nth-child(3) .stat-content h3').textContent = `${hours} h ${minutes} m`;
            
            if (data.last_activity) {
                const date = new Date(data.last_activity);
                document.querySelector('.stat-card:nth-child(4) .stat-content h3').textContent = 
                    date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
            }
        })
        .catch(error => console.error('Error updating analytics:', error));
}

// Update analytics every 30 seconds
setInterval(updateAnalytics, 30000);
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    function handleAjaxForm(form, button) {
        button.disabled = true;
        const formData = new FormData(form);
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            // Try to extract flash message from the returned HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const alert = doc.querySelector('.alert');
            if (alert) {
                document.querySelector('.ajax-flash-message').innerHTML = alert.outerHTML;
            }
            // Optionally, refresh just the video progress section or reload the page
            location.reload();
        })
        .catch(error => {
            document.querySelector('.ajax-flash-message').innerHTML = '<div class="alert alert-danger">' + error + '</div>';
        })
        .finally(() => {
            button.disabled = false;
        });
    }

    document.querySelectorAll('.action-buttons form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const button = form.querySelector('button[type="submit"]');
            handleAjaxForm(form, button);
        });
    });
});
</script>
<div class="ajax-flash-message"></div>

<?php require_once 'includes/footer.php'; ?>
