/*
 * Global Dropdown Fix
 * Ensures dropdowns remain within the parent container and don't overflow
 * For use in staff_management.php, settings.php, and users.php
 */

/* Fix for table containers to ensure proper overflow handling */
.table-responsive {
    position: relative !important;
    overflow: visible !important;
    z-index: 1 !important;
}

/* Fix for admin table containers */
.admin-table-container {
    position: relative !important;
    overflow: visible !important;
    z-index: 1 !important;
}

/* Fix for action cells that contain dropdowns */
.actions-cell {
    position: relative !important;
    z-index: 10 !important;
}

/* Fix for dropdown positioning */
.dropdown {
    position: relative !important;
}

/* Ensure dropdown toggle buttons are properly positioned */
.dropdown-toggle {
    position: relative !important;
    z-index: 1 !important;
}

/* Remove dropdown toggle arrow for action buttons */
.action-btn.dropdown-toggle::after,
.btn-icon.dropdown-toggle::after {
    display: none !important;
}

/* Ensure dropdown menus appear above other elements */
.dropdown-menu {
    position: absolute !important;
    z-index: 1050 !important;
    max-width: 250px !important;
    min-width: 200px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    overflow: visible !important;
    max-height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
}

/* Fix for dropdown menu positioning */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
    transform: none !important;
}

/* Fix for dropdown in tables */
.table .dropdown-menu {
    position: absolute !important;
    inset: auto auto auto auto !important;
    margin: 0 !important;
    transform: translate3d(0px, 38px, 0px) !important;
}

/* Fix for dropdown in action cells */
.actions-cell .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
    transform: none !important;
}

/* Fix for dropdown in cards */
.card .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
}

/* Fix for dropdown in modals */
.modal .dropdown-menu {
    z-index: 1056 !important;
}

/* Fix for modal z-index */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1045 !important;
}

/* Fix for dropdown in staff management page */
.staff-management-dropdown .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
}

/* Fix for dropdown in users page */
.users-dropdown .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
}

/* Fix for dropdown in settings page */
.settings-dropdown .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
}

/* Ensure tables have proper overflow handling */
.pixel-perfect-table {
    overflow: visible !important;
}

/* Ensure dropdown menu is visible when shown */
.dropdown-menu.show {
    display: block !important;
    z-index: 1050 !important;
}

/* Fix for dropdown positioning in admin tables */
.admin-table {
    position: relative !important;
    z-index: 1 !important;
}

/* Media queries for responsive behavior */
@media (min-width: 992px) {
    .table-responsive {
        overflow: visible !important;
    }
}

@media (max-width: 991.98px) {
    .dropdown-menu {
        max-width: 200px !important;
    }
    
    .table-responsive {
        overflow-x: auto !important;
    }
    
    /* Ensure dropdown menus are still visible on mobile */
    .table-responsive .dropdown-menu {
        position: fixed !important;
        top: auto !important;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) !important;
        max-width: 90% !important;
        width: 300px !important;
        margin-top: 10px !important;
    }
}
