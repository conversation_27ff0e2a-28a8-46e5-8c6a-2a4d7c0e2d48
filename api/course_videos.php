<?php
// Include the API configuration file which has error handlers and helper functions
require_once 'config.php';
require_once '../includes/video_helpers.php'; // Include video helpers
require_once '../includes/auth.php'; // Include auth class

// Set content type header to ensure clean JSON response
header('Content-Type: application/json; charset=UTF-8');

// Handle CORS
handleCors();

// Get database connection
$conn = getDbConnection();

// Get authorization header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

// Log all headers for debugging
error_log("📹 Course Videos API: All headers: " . print_r($headers, true));
error_log("📹 Course Videos API: Request method: " . $_SERVER['REQUEST_METHOD']);
error_log("📹 Course Videos API: Request URI: " . $_SERVER['REQUEST_URI']);

// Try different header variations
$authHeaderVariations = [
    'Authorization',
    'authorization',
    'AUTHORIZATION',
    'HTTP_AUTHORIZATION'
];

foreach ($authHeaderVariations as $headerName) {
    if (isset($headers[$headerName])) {
        $authHeader = $headers[$headerName];
        error_log("📹 Course Videos API: Found auth header with key: $headerName");
        break;
    }
}

// Also check $_SERVER for authorization header
if (empty($authHeader) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
    error_log("📹 Course Videos API: Found auth header in \$_SERVER['HTTP_AUTHORIZATION']");
}

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Log the token for debugging
error_log("📹 Course Videos API: Authorization header: " . $authHeader);
error_log("📹 Course Videos API: Extracted token: " . substr($token, 0, 50) . "...");
error_log("📹 Course Videos API: Token length: " . strlen($token));

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

// Log the authentication result
if ($userId) {
    error_log("User authenticated successfully: User ID = $userId");
} else {
    error_log("Authentication failed for token: " . substr($token, 0, 20) . "...");
}

if (!$userId) {
    returnError('Unauthorized: Please log in again', 401);
}

// Check if course ID is provided
if (!isset($_GET['course_id']) || !is_numeric($_GET['course_id'])) {
    returnError('Course ID is required');
}

$courseId = $_GET['course_id'];
error_log("📹 Course Videos API: Requesting videos for course ID: $courseId by user ID: $userId");

// Get user's phone number for additional verification
$userQuery = "SELECT phone_number FROM users WHERE id = ?";
$userStmt = $conn->prepare($userQuery);
$userStmt->bind_param("i", $userId);
$userStmt->execute();
$userResult = $userStmt->get_result();
$userPhone = '';
if ($userResult->num_rows > 0) {
    $userPhone = $userResult->fetch_assoc()['phone_number'];
    error_log("User phone number: $userPhone");
}

// Check if user is enrolled in this course - with additional security
$enrollmentQuery = "SELECT e.* FROM user_course_enrollments e
                   JOIN users u ON e.user_id = u.id
                   WHERE e.user_id = ?
                   AND e.course_id = ?
                   AND e.status = 'active'
                   AND u.id = ?";
$enrollmentStmt = $conn->prepare($enrollmentQuery);
$enrollmentStmt->bind_param("iii", $userId, $courseId, $userId);
$enrollmentStmt->execute();
$enrollmentResult = $enrollmentStmt->get_result();

if ($enrollmentResult->num_rows === 0) {
    error_log("User $userId attempted to access course $courseId but is not enrolled");
    returnError('You are not enrolled in this course', 403);
}

// Get enrollment details to calculate which weeks should be unlocked
$enrollment = $enrollmentResult->fetch_assoc();
$enrollmentStartDate = new DateTime($enrollment['start_date']);
$currentDate = new DateTime();
$weeksSinceEnrollment = floor($enrollmentStartDate->diff($currentDate)->days / 7);

// Get course details
$courseQuery = "SELECT * FROM courses WHERE id = ? AND is_active = 1";
$courseStmt = $conn->prepare($courseQuery);
$courseStmt->bind_param("i", $courseId);
$courseStmt->execute();
$courseResult = $courseStmt->get_result();

if ($courseResult->num_rows === 0) {
    returnError('Course not found', 404);
}

$course = $courseResult->fetch_assoc();

// Get all videos for this course with progress information
$query = "SELECT v.*,
          IFNULL(p.is_unlocked, 0) as is_unlocked,
          IFNULL(p.is_completed, 0) as is_completed,
          p.unlock_date,
          p.last_watched_at,
          p.watch_duration_seconds,
          p.progress_percentage
          FROM course_videos v
          LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
          WHERE v.course_id = ? AND v.is_active = 1
          ORDER BY v.week_number, v.sequence_number";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $userId, $courseId);
$stmt->execute();
$result = $stmt->get_result();

$videos = [];
$videosByWeek = [];

while ($row = $result->fetch_assoc()) {
    $weekNumber = $row['week_number'];

    // Determine if video should be unlocked based on week number
    $shouldBeUnlocked = $weekNumber <= ($weeksSinceEnrollment + 1); // +1 to unlock first week immediately

    // If video is not yet unlocked in the database but should be based on time
    if (!$row['is_unlocked'] && $shouldBeUnlocked) {
        // Unlock the video
        $unlockQuery = "INSERT INTO user_video_progress
                       (user_id, video_id, is_unlocked, unlock_date)
                       VALUES (?, ?, 1, NOW())
                       ON DUPLICATE KEY UPDATE is_unlocked = 1, unlock_date = NOW()";
        $unlockStmt = $conn->prepare($unlockQuery);
        $unlockStmt->bind_param("ii", $userId, $row['id']);
        $unlockStmt->execute();

        // Update the row data
        $row['is_unlocked'] = 1;
        $row['unlock_date'] = date('Y-m-d H:i:s');
    }

    // Calculate unlock date for locked videos
    if (!$shouldBeUnlocked && !$row['unlock_date']) {
        $weeksToUnlock = $weekNumber - $weeksSinceEnrollment;
        $unlockDate = clone $currentDate;
        $unlockDate->add(new DateInterval('P' . $weeksToUnlock . 'W'));
        $row['unlock_date'] = $unlockDate->format('Y-m-d H:i:s');

        // Calculate days until unlock
        $daysUntilUnlock = $currentDate->diff($unlockDate)->days;
        $row['days_until_unlock'] = $daysUntilUnlock;
        $row['weeks_until_unlock'] = $weeksToUnlock;
    }

    // Determine if this is the current week's content
    $isCurrentWeek = false;
    if ($shouldBeUnlocked && !$row['is_unlocked']) {
        // Just about to be unlocked
        $isCurrentWeek = true;
    } else if ($row['is_unlocked'] && $row['unlock_date']) {
        // Check if it was unlocked within the last 7 days
        $unlockDate = new DateTime($row['unlock_date']);
        $daysSinceUnlock = $currentDate->diff($unlockDate)->days;
        if ($daysSinceUnlock < 7) {
            $isCurrentWeek = true;
        }
    } else if (!$shouldBeUnlocked && isset($row['days_until_unlock']) && $row['days_until_unlock'] < 7) {
        // Will be unlocked within the next 7 days
        $isCurrentWeek = true;
    }

    $row['is_current_week'] = $isCurrentWeek;

    // Process video URL for Vimeo
    $videoUrl = $row['video_url'];
    $videoProvider = 'unknown';
    $videoEmbedUrl = null;
    $videoId = null;

    // Check if it's a Vimeo URL
    if (isVimeoUrl($videoUrl)) {
        $videoProvider = 'vimeo';
        $videoId = extractVimeoId($videoUrl);
        $videoEmbedUrl = getVimeoEmbedUrl($videoUrl);

        // If no thumbnail URL is provided, use Vimeo's thumbnail
        if (empty($row['thumbnail_url'])) {
            $row['thumbnail_url'] = getVimeoThumbnailUrl($videoUrl);
        }
    }

    $videoData = [
        'id' => $row['id'],
        'course_id' => $courseId,
        'title' => $row['title'],
        'description' => $row['description'],
        'video_url' => $row['video_url'],
        'thumbnail_url' => $row['thumbnail_url'],
        'duration_minutes' => $row['duration_minutes'],
        'week_number' => $weekNumber,
        'sequence_number' => $row['sequence_number'],
        'is_unlocked' => (bool)$row['is_unlocked'],
        'is_completed' => (bool)$row['is_completed'],
        'unlock_date' => $row['unlock_date'],
        'completion_date' => $row['last_watched_at'],
        'watch_duration_seconds' => $row['watch_duration_seconds'],
        'progress_percentage' => $row['progress_percentage'],
        // Add Vimeo-specific data
        'video_provider' => $videoProvider,
        'video_embed_url' => $videoEmbedUrl,
        'video_id' => $videoId,
        // Add unlock status information
        'days_until_unlock' => isset($row['days_until_unlock']) ? $row['days_until_unlock'] : null,
        'weeks_until_unlock' => isset($row['weeks_until_unlock']) ? $row['weeks_until_unlock'] : null,
        'is_current_week' => isset($row['is_current_week']) ? (bool)$row['is_current_week'] : false
    ];

    $videos[] = $videoData;

    // Group videos by week
    if (!isset($videosByWeek[$weekNumber])) {
        $videosByWeek[$weekNumber] = [];
    }

    $videosByWeek[$weekNumber][] = $videoData;
}

// Calculate course progress
$totalVideos = count($videos);
$completedVideos = 0;
foreach ($videos as $video) {
    if ($video['is_completed']) {
        $completedVideos++;
    }
}
$progressPercentage = $totalVideos > 0 ? round(($completedVideos / $totalVideos) * 100) : 0;

// Add user information to the response for verification
$responseData = [
    'success' => true,
    'course' => [
        'id' => $course['id'],
        'title' => $course['title'],
        'description' => $course['description'],
        'thumbnail_url' => $course['thumbnail_url'],
        'duration_weeks' => $course['duration_weeks'],
        'total_videos' => $totalVideos,
        'completed_videos' => $completedVideos,
        'progress_percentage' => $progressPercentage
    ],
    'videos' => $videos,
    'videos_by_week' => $videosByWeek,
    'enrollment' => [
        'id' => $enrollment['id'],
        'start_date' => $enrollment['start_date'],
        'end_date' => $enrollment['end_date'],
        'status' => $enrollment['status'],
        'weeks_since_enrollment' => $weeksSinceEnrollment
    ],
    'user_id' => $userId,
    'phone_last_digits' => !empty($userPhone) ? substr($userPhone, -4) : ''
];

// Return clean JSON response with no HTML tags
returnCleanResponse($responseData);
?>
