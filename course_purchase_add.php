<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $userId = intval($_POST['user_id'] ?? 0);
    $courseId = intval($_POST['course_id'] ?? 0);
    $purchaseDate = Utilities::sanitizeInput($_POST['purchase_date'] ?? date('Y-m-d H:i:s'));
    $amountPaid = floatval($_POST['amount_paid'] ?? 0);
    $paymentMethod = Utilities::sanitizeInput($_POST['payment_method'] ?? 'credit_card');
    $transactionId = Utilities::sanitizeInput($_POST['transaction_id'] ?? '');
    $status = Utilities::sanitizeInput($_POST['status'] ?? 'completed');

    // Validate inputs
    $errors = [];

    if ($userId <= 0) {
        $errors[] = 'Please select a user.';
    }

    if ($courseId <= 0) {
        $errors[] = 'Please select a course.';
    }

    if (empty($purchaseDate)) {
        $errors[] = 'Purchase date is required.';
    }

    if ($amountPaid < 0) {
        $errors[] = 'Amount paid cannot be negative.';
    }

    // Check if user already purchased this course
    $checkQuery = "SELECT id FROM course_purchases WHERE user_id = ? AND course_id = ? AND status = 'completed'";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("ii", $userId, $courseId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows > 0) {
        $errors[] = 'This user has already purchased this course.';
    }

    // If no errors, insert the purchase
    if (empty($errors)) {
        $insertQuery = "INSERT INTO course_purchases (user_id, course_id, purchase_date, amount_paid, payment_method, transaction_id, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("iisdssss", $userId, $courseId, $purchaseDate, $amountPaid, $paymentMethod, $transactionId, $status);

        if ($stmt->execute()) {
            // If purchase is completed, also create an enrollment
            if ($status === 'completed') {
                $enrollmentQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                                   SELECT ?, ?, ?, ?, DATE_ADD(?, INTERVAL c.duration_weeks WEEK), 'active'
                                   FROM courses c WHERE c.id = ?";
                $enrollmentStmt = $conn->prepare($enrollmentQuery);
                $today = date('Y-m-d');
                $enrollmentStmt->bind_param("iisssi", $userId, $courseId, $today, $today, $today, $courseId);
                $enrollmentStmt->execute();

                // Initialize video progress for first week videos
                $videosQuery = "SELECT id, week_number FROM course_videos WHERE course_id = ? AND week_number = 1 ORDER BY sequence_number";
                $videosStmt = $conn->prepare($videosQuery);
                $videosStmt->bind_param("i", $courseId);
                $videosStmt->execute();
                $videosResult = $videosStmt->get_result();

                while ($video = $videosResult->fetch_assoc()) {
                    $progressQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                                     VALUES (?, ?, 1, ?)";
                    $progressStmt = $conn->prepare($progressQuery);
                    $progressStmt->bind_param("iis", $userId, $video['id'], $today);
                    $progressStmt->execute();
                }
            }

            Utilities::setFlashMessage('success', 'Purchase added successfully.');
            Utilities::redirect('course_purchases.php');
        } else {
            $errors[] = 'Failed to add purchase: ' . $conn->error;
        }
    }
}

// Get all users for the dropdown
$usersQuery = "SELECT id, name, username FROM users WHERE is_active = 1 ORDER BY name";
$usersResult = $conn->query($usersQuery);

// Get all courses for the dropdown
$coursesQuery = "SELECT id, title, price, discount_percentage FROM courses WHERE is_active = 1 ORDER BY title";
$coursesResult = $conn->query($coursesQuery);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Add Purchase</h1>
    <a href="course_purchases.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Purchases
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <form method="post" id="purchaseForm">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="user_id" class="form-label">User</label>
                    <select class="form-select user-select" id="user_id" name="user_id" required>
                        <option value="">-- Select User --</option>
                        <?php if ($usersResult && $usersResult->num_rows > 0): ?>
                            <?php while ($user = $usersResult->fetch_assoc()): ?>
                                <option value="<?php echo $user['id']; ?>">
                                    <?php echo htmlspecialchars($user['name']); ?> (<?php echo htmlspecialchars($user['username']); ?>)
                                </option>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="course_id" class="form-label">Course</label>
                    <select class="form-select" id="course_id" name="course_id" required>
                        <option value="">-- Select Course --</option>
                        <?php if ($coursesResult && $coursesResult->num_rows > 0): ?>
                            <?php while ($course = $coursesResult->fetch_assoc()): ?>
                                <?php
                                    $displayPrice = $course['price'];
                                    if ($course['discount_percentage'] > 0) {
                                        $displayPrice = $displayPrice * (1 - $course['discount_percentage'] / 100);
                                    }
                                ?>
                                <option value="<?php echo $course['id']; ?>" data-price="<?php echo $displayPrice; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                    (₹<?php echo number_format($displayPrice, 2); ?>
                                    <?php if ($course['discount_percentage'] > 0): ?>
                                        - <?php echo $course['discount_percentage']; ?>% off
                                    <?php endif; ?>)
                                </option>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="purchase_date" class="form-label">Purchase Date</label>
                    <input type="datetime-local" class="form-control" id="purchase_date" name="purchase_date" value="<?php echo date('Y-m-d\TH:i'); ?>" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="amount_paid" class="form-label">Amount Paid (₹)</label>
                    <input type="number" class="form-control" id="amount_paid" name="amount_paid" value="0.00" min="0" step="0.01" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="payment_method" class="form-label">Payment Method</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="credit_card">Credit Card</option>
                        <option value="paypal">PayPal</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="cash">Cash</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="transaction_id" class="form-label">Transaction ID</label>
                    <input type="text" class="form-control" id="transaction_id" name="transaction_id" placeholder="Optional">
                </div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="completed">Completed</option>
                    <option value="pending">Pending</option>
                    <option value="refunded">Refunded</option>
                    <option value="failed">Failed</option>
                </select>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Save Purchase
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const courseSelect = document.getElementById('course_id');
    const amountInput = document.getElementById('amount_paid');

    // Update amount when course changes
    courseSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const price = parseFloat(selectedOption.getAttribute('data-price'));
            amountInput.value = price.toFixed(2);
        } else {
            amountInput.value = '0.00';
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
