<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Set content type header
header('Content-Type: application/json');

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    returnError('Method not allowed', 405);
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user is authenticated
$auth = new Auth($conn);
$userId = $auth->authenticateToken();

if (!$userId) {
    returnError('Unauthorized access', 401);
}

try {
    // Get user's video streak data
    $streakQuery = "SELECT current_streak, highest_streak, last_activity_date, created_at, updated_at
                   FROM user_video_streaks 
                   WHERE user_id = ?";
    $streakStmt = $conn->prepare($streakQuery);
    $streakStmt->bind_param("i", $userId);
    $streakStmt->execute();
    $result = $streakStmt->get_result();

    if ($result->num_rows > 0) {
        $streakData = $result->fetch_assoc();
        
        // Check if streak should be reset due to inactivity
        $lastActivityDate = $streakData['last_activity_date'];
        $currentStreak = intval($streakData['current_streak']);
        
        if ($lastActivityDate && $currentStreak > 0) {
            $lastActivity = new DateTime($lastActivityDate);
            $today = new DateTime();
            $today->setTime(0, 0, 0); // Set to start of day
            $lastActivity->setTime(0, 0, 0); // Set to start of day
            
            $daysDifference = $today->diff($lastActivity)->days;
            
            // If more than 1 day has passed, reset current streak
            if ($daysDifference > 1) {
                $updateQuery = "UPDATE user_video_streaks 
                               SET current_streak = 0, updated_at = NOW()
                               WHERE user_id = ?";
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->bind_param("i", $userId);
                $updateStmt->execute();
                
                $streakData['current_streak'] = 0;
            }
        }
        
        returnResponse([
            'success' => true,
            'streak' => [
                'current_streak' => intval($streakData['current_streak']),
                'highest_streak' => intval($streakData['highest_streak']),
                'last_activity_date' => $streakData['last_activity_date'],
                'created_at' => $streakData['created_at'],
                'updated_at' => $streakData['updated_at']
            ]
        ]);
    } else {
        // No streak data found - return default values
        returnResponse([
            'success' => true,
            'streak' => [
                'current_streak' => 0,
                'highest_streak' => 0,
                'last_activity_date' => null,
                'created_at' => null,
                'updated_at' => null
            ]
        ]);
    }

} catch (Exception $e) {
    returnError('Database error: ' . $e->getMessage());
}
?>
