<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Check if staff ID is provided
if (!isset($_GET['staff_id']) || !is_numeric($_GET['staff_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid staff ID']);
    exit;
}

$staffId = (int)$_GET['staff_id'];

// Get permissions for the staff member
$query = "SELECT permission_id FROM admin_user_permissions WHERE admin_user_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $staffId);
$stmt->execute();
$result = $stmt->get_result();

$permissions = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $permissions[] = $row['permission_id'];
    }
}

// Return permissions as JSON
header('Content-Type: application/json');
echo json_encode(['permissions' => $permissions]);
?>
