<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in and is admin
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    die('Access denied. Admin access required.');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

echo "<h1>Activity Log Debug Information</h1>";

// Check if user_activity_log table exists
echo "<h2>1. Table Structure Check</h2>";
$result = $conn->query("SHOW TABLES LIKE 'user_activity_log'");
if ($result->num_rows > 0) {
    echo "<p style='color: green;'>✓ user_activity_log table exists</p>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE user_activity_log");
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ user_activity_log table does not exist</p>";
}

// Check total records
echo "<h2>2. Data Check</h2>";
$totalResult = $conn->query("SELECT COUNT(*) as total FROM user_activity_log");
if ($totalResult) {
    $total = $totalResult->fetch_assoc()['total'];
    echo "<p>Total activity records: <strong>$total</strong></p>";
} else {
    echo "<p style='color: red;'>Error querying activity log: " . $conn->error . "</p>";
}

// Check activity types
$typesResult = $conn->query("SELECT activity_type, COUNT(*) as count FROM user_activity_log GROUP BY activity_type");
if ($typesResult && $typesResult->num_rows > 0) {
    echo "<h3>Activity Types:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Activity Type</th><th>Count</th></tr>";
    while ($row = $typesResult->fetch_assoc()) {
        echo "<tr><td>" . htmlspecialchars($row['activity_type']) . "</td><td>" . $row['count'] . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p>No activity types found or error occurred.</p>";
}

// Check recent activities
echo "<h2>3. Recent Activities (Last 10)</h2>";
$recentResult = $conn->query("SELECT * FROM user_activity_log ORDER BY created_at DESC LIMIT 10");
if ($recentResult && $recentResult->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>User ID</th><th>Activity Type</th><th>Related ID</th><th>Details</th><th>Created At</th></tr>";
    while ($row = $recentResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['user_id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['activity_type']) . "</td>";
        echo "<td>" . ($row['related_id'] ?? 'NULL') . "</td>";
        echo "<td style='max-width: 300px; word-wrap: break-word;'>" . htmlspecialchars($row['details'] ?? '') . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No recent activities found.</p>";
}

// Check users with activity
echo "<h2>4. Users with Activity</h2>";
$usersResult = $conn->query("
    SELECT u.id, u.name, u.username, COUNT(al.id) as activity_count 
    FROM users u 
    LEFT JOIN user_activity_log al ON u.id = al.user_id 
    GROUP BY u.id, u.name, u.username 
    HAVING activity_count > 0 
    ORDER BY activity_count DESC 
    LIMIT 10
");
if ($usersResult && $usersResult->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>User ID</th><th>Name</th><th>Username</th><th>Activity Count</th></tr>";
    while ($row = $usersResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . $row['activity_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No users with activity found.</p>";
}

// Test insert capability
echo "<h2>5. Test Activity Logging</h2>";
if (isset($_POST['test_insert'])) {
    $testUserId = intval($_POST['test_user_id']);
    $testVideoId = intval($_POST['test_video_id']);
    
    if ($testUserId > 0 && $testVideoId > 0) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at)
                VALUES (?, 'video_progress', ?, ?, NOW())
            ");
            
            $details = json_encode([
                'action' => 'test_play',
                'watch_duration' => 120,
                'last_position' => 60,
                'is_completed' => false,
                'timestamp' => date('Y-m-d H:i:s'),
                'test_entry' => true
            ]);
            
            $stmt->bind_param("iis", $testUserId, $testVideoId, $details);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ Test activity logged successfully! ID: " . $conn->insert_id . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to log test activity: " . $stmt->error . "</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Exception: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Invalid user ID or video ID</p>";
    }
}

// Get some users and videos for testing
$usersForTest = $conn->query("SELECT id, name FROM users LIMIT 5");
$videosForTest = $conn->query("SELECT id, title FROM course_videos LIMIT 5");

echo "<form method='post'>";
echo "<p>Test logging an activity:</p>";
echo "<select name='test_user_id' required>";
echo "<option value=''>Select User</option>";
if ($usersForTest) {
    while ($user = $usersForTest->fetch_assoc()) {
        echo "<option value='" . $user['id'] . "'>" . htmlspecialchars($user['name']) . " (ID: " . $user['id'] . ")</option>";
    }
}
echo "</select>";

echo "<select name='test_video_id' required>";
echo "<option value=''>Select Video</option>";
if ($videosForTest) {
    while ($video = $videosForTest->fetch_assoc()) {
        echo "<option value='" . $video['id'] . "'>" . htmlspecialchars($video['title']) . " (ID: " . $video['id'] . ")</option>";
    }
}
echo "</select>";

echo "<button type='submit' name='test_insert'>Log Test Activity</button>";
echo "</form>";

echo "<hr>";
echo "<p><a href='video_analytics.php'>← Back to Video Analytics</a></p>";
?>
