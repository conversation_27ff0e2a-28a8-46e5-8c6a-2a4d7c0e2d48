<?php
require_once 'config.php';

// Set appropriate headers for API response
header('Content-Type: application/json');
// Prevent caching
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Validate token and get user data
try {
    $tokenData = validateToken();
    $userId = $tokenData['user_id'];
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Authentication error: ' . $e->getMessage(),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => defined('DEV_MODE') && DEV_MODE === true
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

if (!isset($_FILES['food_image'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'No image uploaded']);
    error_log("Food image upload failed: No image uploaded");
    exit;
}

// Create upload directory if it doesn't exist
$uploadDir = __DIR__ . '/../assets/food_images/';
if (!is_dir($uploadDir)) {
    if (!mkdir($uploadDir, 0777, true)) {
        $error = error_get_last();
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create upload directory: ' . ($error['message'] ?? 'Unknown error')]);
        error_log("Failed to create upload directory: " . ($error['message'] ?? 'Unknown error'));
        exit;
    }
} elseif (!is_writable($uploadDir)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Upload directory is not writable']);
    error_log("Upload directory is not writable: " . $uploadDir);
    exit;
}

// Log file information for debugging
$fileType = $_FILES['food_image']['type'];
$fileName = $_FILES['food_image']['name'];
$fileSize = $_FILES['food_image']['size'];
$ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

error_log("Food image upload details - Name: $fileName, Type: $fileType, Size: $fileSize, Extension: $ext");

// Validate file type
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/octet-stream'];
$allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic', 'heif'];

// In development mode, be more lenient with file types
if (defined('DEV_MODE') && DEV_MODE === true) {
    // If we have a valid extension but unknown mime type, proceed anyway
    if (in_array($ext, $allowedExtensions)) {
        error_log("DEV MODE: Allowing file with valid extension ($ext) despite mime type ($fileType)");
    } else if (!in_array($fileType, $allowedTypes)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid file type. Allowed types: JPG, PNG, GIF, WEBP',
            'debug_info' => [
                'file_type' => $fileType,
                'file_name' => $fileName,
                'file_size' => $fileSize,
                'extension' => $ext
            ]
        ]);
        error_log("Invalid file type: " . $fileType);
        exit;
    }
} else {
    // In production, be more strict
    if (!in_array($fileType, $allowedTypes)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid file type. Allowed types: JPG, PNG, GIF, WEBP']);
        error_log("Invalid file type: " . $fileType);
        exit;
    }

    if (!in_array($ext, $allowedExtensions)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid file extension. Allowed extensions: jpg, jpeg, png, gif, webp']);
        error_log("Invalid file extension: " . $ext);
        exit;
    }
}

// Generate a unique filename
$filename = 'food_' . $userId . '_' . time() . '.' . $ext;
$targetPath = $uploadDir . $filename;

if (!move_uploaded_file($_FILES['food_image']['tmp_name'], $targetPath)) {
    $error = error_get_last();
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to save image: ' . ($error['message'] ?? 'Unknown error')]);
    error_log("Failed to save food image: " . ($error['message'] ?? 'Unknown error'));
    exit;
}

// Log successful file upload
error_log("Food image uploaded successfully: " . $targetPath);

// Use a relative path for better compatibility across environments
$imageUrl = 'admin/assets/food_images/' . $filename;

// Log the image URL for debugging
error_log("Food image URL (relative): " . $imageUrl);

// Also log the full URL for reference
$baseUrl = defined('APP_URL') ? APP_URL : 'http://localhost:8000';
$fullImageUrl = $baseUrl . '/' . $imageUrl;
error_log("Full food image URL: " . $fullImageUrl);

// Get AI settings
$conn = getDbConnection();
$stmt = $conn->prepare("SELECT setting_key, setting_value FROM ai_settings");
$stmt->execute();
$result = $stmt->get_result();

$aiSettings = [
    'openai_api_key' => '',
    'openai_model' => 'gpt-4-vision-preview',
    'calorie_analysis_enabled' => '1',
    'calorie_analysis_prompt' => 'Analyze this food image and provide the following information in JSON format: {"food_name": "name of the food", "calories": estimated calories per serving, "protein": estimated protein in grams, "carbs": estimated carbs in grams, "fat": estimated fat in grams, "serving_size": "standard serving size"}. Be as accurate as possible with your estimates.'
];

while ($row = $result->fetch_assoc()) {
    $aiSettings[$row['setting_key']] = $row['setting_value'];
}

// Check if AI analysis is enabled and API key is configured
$aiAnalysisResult = null;
if ($aiSettings['calorie_analysis_enabled'] === '1' && !empty($aiSettings['openai_api_key'])) {
    try {
        // Convert image to base64 for OpenAI API
        $imageData = 'data:image/jpeg;base64,' . base64_encode(file_get_contents($targetPath));

        // Call OpenAI API for analysis
        $url = 'https://api.openai.com/v1/chat/completions';

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $aiSettings['openai_api_key']
        ];

        $data = [
            'model' => $aiSettings['openai_model'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a nutritionist AI assistant that analyzes food images to estimate calories and nutritional content.'
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $aiSettings['calorie_analysis_prompt']
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $imageData
                            ]
                        ]
                    ]
                ]
            ],
            'max_tokens' => 500
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception('cURL error: ' . curl_error($ch));
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            $responseData = json_decode($response, true);
            $errorMessage = isset($responseData['error']['message']) ? $responseData['error']['message'] : 'Unknown error';
            throw new Exception('OpenAI API error (' . $httpCode . '): ' . $errorMessage);
        }

        $responseData = json_decode($response, true);

        if (!isset($responseData['choices'][0]['message']['content'])) {
            throw new Exception('Invalid response from OpenAI API');
        }

        $content = $responseData['choices'][0]['message']['content'];

        // Try to extract JSON from the response
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $jsonStr = $matches[0];
            $aiAnalysisResult = json_decode($jsonStr, true);

            if ($aiAnalysisResult === null) {
                throw new Exception('Failed to parse JSON from OpenAI response');
            }
        } else {
            throw new Exception('No JSON found in OpenAI response');
        }
    } catch (Exception $e) {
        error_log("AI analysis error: " . $e->getMessage());
        // Continue without AI analysis
    }
}

// Respond with the image URL and AI analysis result
http_response_code(200);
// Use the returnCleanResponse function from config.php to ensure proper JSON formatting
returnCleanResponse([
    'success' => true,
    'image_url' => $imageUrl,
    'ai_analysis' => $aiAnalysisResult
]);
?>
