<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

session_start();
require_once 'includes/auth.php';
require_once 'includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if user is admin
$db = new Database();
$conn = $db->getConnection();

// For development mode, assume the user is an admin
if (defined('DEV_MODE') && DEV_MODE === true) {
    $isAdmin = true;
} else {
    // Try to get from admin_users table first
    $stmt = $conn->prepare("SELECT role FROM admin_users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $isAdmin = ($user['role'] === 'admin');
    } else {
        // If not in admin_users, check if user has admin role in session
        $isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
}

if (!$isAdmin) {
    header('Location: index.php');
    exit;
}

// Handle quote deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $quoteId = (int)$_GET['delete'];
    $deleteStmt = $conn->prepare("DELETE FROM motivational_quotes WHERE id = ?");
    $deleteStmt->bind_param("i", $quoteId);
    $deleteStmt->execute();

    if ($deleteStmt->affected_rows > 0) {
        $successMessage = "Quote deleted successfully.";
    } else {
        $errorMessage = "Failed to delete quote.";
    }

    $deleteStmt->close();
}

// Handle quote activation/deactivation
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $quoteId = (int)$_GET['toggle'];

    // Get current status
    $statusStmt = $conn->prepare("SELECT is_active FROM motivational_quotes WHERE id = ?");
    $statusStmt->bind_param("i", $quoteId);
    $statusStmt->execute();
    $statusResult = $statusStmt->get_result();
    $quote = $statusResult->fetch_assoc();
    $statusStmt->close();

    if ($quote) {
        $newStatus = $quote['is_active'] ? 0 : 1;
        $updateStmt = $conn->prepare("UPDATE motivational_quotes SET is_active = ? WHERE id = ?");
        $updateStmt->bind_param("ii", $newStatus, $quoteId);
        $updateStmt->execute();

        if ($updateStmt->affected_rows > 0) {
            $successMessage = "Quote " . ($newStatus ? "activated" : "deactivated") . " successfully.";
        } else {
            $errorMessage = "Failed to update quote status.";
        }

        $updateStmt->close();
    }
}

// Get quotes with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

$search = isset($_GET['search']) ? $conn->real_escape_string($_GET['search']) : '';
$category = isset($_GET['category']) ? $conn->real_escape_string($_GET['category']) : '';
$status = isset($_GET['status']) ? $conn->real_escape_string($_GET['status']) : '';

// Build the query
$query = "SELECT * FROM motivational_quotes WHERE 1=1";
$countQuery = "SELECT COUNT(*) as total FROM motivational_quotes WHERE 1=1";

$params = [];
$types = "";

if (!empty($search)) {
    $query .= " AND (quote LIKE ? OR author LIKE ?)";
    $countQuery .= " AND (quote LIKE ? OR author LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= "ss";
}

if (!empty($category)) {
    $query .= " AND category = ?";
    $countQuery .= " AND category = ?";
    $params[] = $category;
    $types .= "s";
}

if ($status !== '') {
    $statusValue = (int)$status;
    $query .= " AND is_active = ?";
    $countQuery .= " AND is_active = ?";
    $params[] = $statusValue;
    $types .= "i";
}

$query .= " ORDER BY id DESC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$types .= "ii";

// Get total count
$countStmt = $conn->prepare($countQuery);
if (!empty($params) && count($params) > 2) {
    $countParams = array_slice($params, 0, -2);
    $countTypes = substr($types, 0, -2);
    $countStmt->bind_param($countTypes, ...$countParams);
}
$countStmt->execute();
$countResult = $countStmt->get_result();
$totalRows = $countResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $limit);
$countStmt->close();

// Get quotes
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$quotes = [];

while ($row = $result->fetch_assoc()) {
    $quotes[] = $row;
}

$stmt->close();

// Get all categories for filter
$categoriesStmt = $conn->prepare("SELECT DISTINCT category FROM motivational_quotes WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categoriesStmt->execute();
$categoriesResult = $categoriesStmt->get_result();
$categories = [];

while ($row = $categoriesResult->fetch_assoc()) {
    $categories[] = $row['category'];
}

$categoriesStmt->close();

// Include header
$pageTitle = "Motivational Quotes Management";
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Motivational Quotes Management</h1>

    <?php if (isset($successMessage)): ?>
        <div class="alert alert-success"><?php echo $successMessage; ?></div>
    <?php endif; ?>

    <?php if (isset($errorMessage)): ?>
        <div class="alert alert-danger"><?php echo $errorMessage; ?></div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Quotes</h6>
            <div>
                <a href="quote_add.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New Quote
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <form method="get" class="form-inline">
                        <div class="form-group mr-2">
                            <input type="text" name="search" class="form-control" placeholder="Search quotes..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group mr-2">
                            <select name="category" class="form-control">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>" <?php echo $category == $cat ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(ucfirst($cat)); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group mr-2">
                            <select name="status" class="form-control">
                                <option value="">All Status</option>
                                <option value="1" <?php echo $status === '1' ? 'selected' : ''; ?>>Active</option>
                                <option value="0" <?php echo $status === '0' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="quotes.php" class="btn btn-secondary ml-2">Reset</a>
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Quote</th>
                            <th>Author</th>
                            <th>Category</th>
                            <th>AI Generated</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($quotes)): ?>
                            <tr>
                                <td colspan="8" class="text-center">No quotes found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($quotes as $quote): ?>
                                <tr>
                                    <td><?php echo $quote['id']; ?></td>
                                    <td><?php echo htmlspecialchars(substr($quote['quote'], 0, 100)) . (strlen($quote['quote']) > 100 ? '...' : ''); ?></td>
                                    <td><?php echo htmlspecialchars($quote['author'] ?: 'Unknown'); ?></td>
                                    <td><?php echo htmlspecialchars(ucfirst($quote['category'] ?: 'Uncategorized')); ?></td>
                                    <td><?php echo $quote['is_ai_generated'] ? 'Yes' : 'No'; ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $quote['is_active'] ? 'success' : 'danger'; ?>">
                                            <?php echo $quote['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($quote['created_at'])); ?></td>
                                    <td>
                                        <a href="quote_edit.php?id=<?php echo $quote['id']; ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="quotes.php?toggle=<?php echo $quote['id']; ?>" class="btn btn-<?php echo $quote['is_active'] ? 'warning' : 'success'; ?> btn-sm">
                                            <i class="fas fa-<?php echo $quote['is_active'] ? 'times' : 'check'; ?>"></i>
                                        </a>
                                        <a href="quotes.php?delete=<?php echo $quote['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this quote?');">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($totalPages > 1): ?>
                <nav>
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&status=<?php echo urlencode($status); ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&status=<?php echo urlencode($status); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&status=<?php echo urlencode($status); ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
