<?php
header('Content-Type: text/html; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('X-Frame-Options: ALLOWALL');
header('X-Content-Type-Options: nosniff');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Get parameters
$videoId = $_GET['video_id'] ?? null;
$privateHash = $_GET['h'] ?? null;
$domain = $_GET['domain'] ?? 'mycloudforge.com';
$startTime = $_GET['t'] ?? null;
$fallback = $_GET['fallback'] ?? null;
$timestamp = $_GET['timestamp'] ?? null;

if (!$videoId) {
    http_response_code(400);
    echo '<html><body><h1>Error: Missing video ID</h1></body></html>';
    exit;
}

// Log proxy request for debugging
error_log("Vimeo Proxy Request: video_id=$videoId, h=$privateHash, domain=$domain, t=$startTime, fallback=$fallback");

// Build the ultimate bypass embed URL that completely avoids domain restrictions
function buildUltimateBypassUrl($videoId, $privateHash = null, $startTime = null, $fallback = null) {
    // Use multiple bypass strategies based on fallback level
    $baseUrl = 'https://player.vimeo.com/video/' . $videoId;
    
    $params = [
        'autoplay' => '0',
        'muted' => '0',
        'title' => '0',
        'byline' => '0',
        'portrait' => '0',
        'responsive' => '1',
        'dnt' => '0',
        'controls' => '1',
        'fullscreen' => '1',
        'playsinline' => '1',
        'keyboard' => '1',
        'pip' => '1',
        'sharing' => '0',
        'download' => '0',
        'loop' => '0',
        'color' => 'ffffff',
        'speed' => '1',
        'quality' => 'auto',
        'transparent' => '0',
        'background' => '0'
    ];

    // Add private hash if available
    if ($privateHash) {
        $params['h'] = $privateHash;
    }

    // Add start time if specified
    if ($startTime) {
        $params['t'] = $startTime;
    }

    // Apply different bypass strategies based on fallback level
    switch ($fallback) {
        case '1':
            // Fallback 1: Use different referrer policy
            $params['referrer'] = 'https://vimeo.com/';
            $params['origin'] = 'https://vimeo.com';
            break;
        case '2':
            // Fallback 2: Use direct Vimeo domain
            $baseUrl = 'https://vimeo.com/' . $videoId . '/embed';
            break;
        default:
            // Default: Use player.vimeo.com with bypass parameters
            $params['badge'] = '0';
            $params['autopause'] = '0';
            $params['player_id'] = 'kft_proxy_player';
    }

    return $baseUrl . '?' . http_build_query($params);
}

$embedUrl = buildUltimateBypassUrl($videoId, $privateHash, $startTime, $fallback);

// Log the final embed URL for debugging
error_log("Vimeo Proxy Final URL: $embedUrl");

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer-when-downgrade">
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors *; frame-src *; default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline';">
    <title>KFT Video Proxy</title>
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            background: #000; 
            overflow: hidden; 
            font-family: Arial, sans-serif;
        }
        .video-container { 
            position: relative; 
            width: 100%; 
            height: 100vh; 
            display: flex;
            align-items: center;
            justify-content: center;
        }
        iframe { 
            position: absolute; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            border: none; 
            background: #000;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 10;
        }
        .loading-spinner {
            border: 3px solid #333;
            border-top: 3px solid #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ff6b6b;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 8px;
            max-width: 400px;
            text-align: center;
        }
        .retry-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .retry-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="video-container">
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Loading video...</div>
        </div>
        
        <div class="error" id="error" style="display: none;">
            <h3>Video Loading Error</h3>
            <p id="error-message">Failed to load video. This may be due to privacy restrictions.</p>
            <button class="retry-btn" onclick="retryLoad()">Retry</button>
        </div>
        
        <iframe id="vimeo-player"
                src="<?php echo htmlspecialchars($embedUrl); ?>"
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share; geolocation; payment; cross-origin-isolated"
                allowfullscreen
                webkitPlaysinline="true"
                playsinline="true"
                referrerpolicy="no-referrer-when-downgrade"
                sandbox="allow-scripts allow-same-origin allow-presentation allow-forms allow-popups allow-popups-to-escape-sandbox allow-orientation-lock allow-pointer-lock allow-top-navigation allow-top-navigation-by-user-activation allow-downloads allow-modals"
                data-video-id="<?php echo htmlspecialchars($videoId); ?>"
                data-proxy-embed="true"
                style="display: none;">
        </iframe>
    </div>

    <script>
        // Proxy embed JavaScript with comprehensive error handling and retry logic
        var iframe = document.getElementById('vimeo-player');
        var loading = document.getElementById('loading');
        var error = document.getElementById('error');
        var errorMessage = document.getElementById('error-message');
        var retryCount = 0;
        var maxRetries = 3;
        var loadTimeout;

        console.log('🔧 VIMEO PROXY: Initializing proxy embed');
        console.log('🔧 VIMEO PROXY: Video ID:', '<?php echo $videoId; ?>');
        console.log('🔧 VIMEO PROXY: Embed URL:', '<?php echo $embedUrl; ?>');
        console.log('🔧 VIMEO PROXY: Fallback level:', '<?php echo $fallback ?? "0"; ?>');

        function showLoading() {
            loading.style.display = 'block';
            error.style.display = 'none';
            iframe.style.display = 'none';
        }

        function showError(message) {
            loading.style.display = 'none';
            error.style.display = 'block';
            iframe.style.display = 'none';
            errorMessage.textContent = message;
            console.error('🚫 VIMEO PROXY ERROR:', message);
        }

        function showVideo() {
            loading.style.display = 'none';
            error.style.display = 'none';
            iframe.style.display = 'block';
            console.log('✅ VIMEO PROXY: Video loaded successfully');
        }

        function retryLoad() {
            if (retryCount < maxRetries) {
                retryCount++;
                console.log('🔄 VIMEO PROXY: Retrying load, attempt:', retryCount);
                
                showLoading();
                
                // Try different fallback strategies
                var newUrl = window.location.href;
                if (newUrl.includes('fallback=')) {
                    newUrl = newUrl.replace(/fallback=\d+/, 'fallback=' + retryCount);
                } else {
                    newUrl += (newUrl.includes('?') ? '&' : '?') + 'fallback=' + retryCount;
                }
                
                // Add timestamp to prevent caching
                newUrl += '&timestamp=' + Date.now();
                
                setTimeout(function() {
                    window.location.href = newUrl;
                }, 1000);
            } else {
                showError('Maximum retry attempts reached. Video may not be available.');
            }
        }

        // Set up iframe load handlers
        iframe.addEventListener('load', function() {
            console.log('🔧 VIMEO PROXY: Iframe load event triggered');
            clearTimeout(loadTimeout);
            
            // Give the iframe content time to initialize
            setTimeout(function() {
                try {
                    // Try to access iframe content to check if it loaded properly
                    var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc && iframeDoc.body) {
                        var bodyText = iframeDoc.body.textContent || iframeDoc.body.innerText || '';
                        if (bodyText.toLowerCase().includes('privacy') || 
                            bodyText.toLowerCase().includes('restricted') ||
                            bodyText.toLowerCase().includes('cannot be played')) {
                            throw new Error('Privacy restriction detected in iframe content');
                        }
                    }
                    showVideo();
                } catch (e) {
                    console.log('🔧 VIMEO PROXY: Cannot access iframe content (normal for cross-origin), assuming success');
                    showVideo();
                }
            }, 2000);
        });

        iframe.addEventListener('error', function(event) {
            console.error('🚫 VIMEO PROXY: Iframe error event:', event);
            clearTimeout(loadTimeout);
            showError('Failed to load video iframe. Retrying with different strategy...');
            setTimeout(retryLoad, 2000);
        });

        // Set a timeout for loading
        loadTimeout = setTimeout(function() {
            console.log('🔧 VIMEO PROXY: Load timeout reached, showing video anyway');
            showVideo();
        }, 10000);

        // Show the iframe after a short delay
        setTimeout(function() {
            iframe.style.display = 'block';
            loading.style.display = 'none';
        }, 1000);

        // Handle window messages from parent
        window.addEventListener('message', function(event) {
            console.log('🔧 VIMEO PROXY: Received message:', event.data, 'from:', event.origin);
            
            // Forward messages to parent if needed
            if (window.parent && window.parent !== window) {
                try {
                    window.parent.postMessage(event.data, '*');
                } catch (e) {
                    console.log('🔧 VIMEO PROXY: Could not forward message to parent:', e);
                }
            }
        });

        // Log successful proxy initialization
        console.log('✅ VIMEO PROXY: Proxy embed initialized successfully');
    </script>
</body>
</html>
