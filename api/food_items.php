<?php
require_once 'config.php';

// Validate token
$tokenData = validateToken();
$userId = $tokenData['user_id'];

// Handle different request methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get food items
        $conn = getDbConnection();
        
        // Get search query from parameters
        $search = isset($_GET['search']) ? $_GET['search'] : '';
        $category = isset($_GET['category']) ? $_GET['category'] : '';
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
        
        // Build query
        $query = "SELECT * FROM food_items WHERE 1=1";
        $params = [];
        $types = "";
        
        if (!empty($search)) {
            $query .= " AND name LIKE ?";
            $searchParam = "%$search%";
            $params[] = $searchParam;
            $types .= "s";
        }
        
        if (!empty($category)) {
            $query .= " AND category = ?";
            $params[] = $category;
            $types .= "s";
        }
        
        // Add limit and offset
        $query .= " ORDER BY name ASC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        $types .= "ii";
        
        $stmt = $conn->prepare($query);
        
        // Bind parameters dynamically if we have any
        if (!empty($params)) {
            $bindParams = array_merge([$stmt, $types], $params);
            call_user_func_array('mysqli_stmt_bind_param', $bindParams);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        $foodItems = [];
        while ($row = $result->fetch_assoc()) {
            $foodItems[] = $row;
        }
        
        // Get user's custom food items
        $userQuery = "SELECT * FROM user_food_items WHERE user_id = ?";
        $userParams = [$userId];
        $userTypes = "i";
        
        if (!empty($search)) {
            $userQuery .= " AND name LIKE ?";
            $userParams[] = $searchParam;
            $userTypes .= "s";
        }
        
        $userQuery .= " ORDER BY name ASC";
        
        $userStmt = $conn->prepare($userQuery);
        
        // Bind parameters
        $bindUserParams = array_merge([$userStmt, $userTypes], $userParams);
        call_user_func_array('mysqli_stmt_bind_param', $bindUserParams);
        
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        
        $userFoodItems = [];
        while ($row = $userResult->fetch_assoc()) {
            $row['is_custom'] = true;
            $userFoodItems[] = $row;
        }
        
        // Get categories
        $categoryQuery = "SELECT DISTINCT category FROM food_items WHERE category IS NOT NULL ORDER BY category";
        $categoryStmt = $conn->prepare($categoryQuery);
        $categoryStmt->execute();
        $categoryResult = $categoryStmt->get_result();
        
        $categories = [];
        while ($row = $categoryResult->fetch_assoc()) {
            $categories[] = $row['category'];
        }
        
        // Return response
        returnResponse([
            'food_items' => $foodItems,
            'user_food_items' => $userFoodItems,
            'categories' => $categories
        ]);
        break;
        
    case 'POST':
        // Add a new custom food item
        $conn = getDbConnection();
        
        // Get request body
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        if (!isset($data['name']) || !isset($data['calories'])) {
            returnError('Missing required fields', 400);
        }
        
        // Prepare query
        $query = "INSERT INTO user_food_items 
                 (user_id, name, calories, protein, carbs, fat, serving_size) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);
        
        // Set default values for optional fields
        $protein = isset($data['protein']) ? $data['protein'] : null;
        $carbs = isset($data['carbs']) ? $data['carbs'] : null;
        $fat = isset($data['fat']) ? $data['fat'] : null;
        $servingSize = isset($data['serving_size']) ? $data['serving_size'] : null;
        
        $stmt->bind_param("isiddds", $userId, $data['name'], $data['calories'], $protein, $carbs, $fat, $servingSize);
        
        if ($stmt->execute()) {
            $itemId = $conn->insert_id;
            
            // Get the inserted record
            $selectQuery = "SELECT * FROM user_food_items WHERE id = ?";
            $selectStmt = $conn->prepare($selectQuery);
            $selectStmt->bind_param("i", $itemId);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $item = $result->fetch_assoc();
            $item['is_custom'] = true;
            
            returnResponse([
                'success' => true,
                'message' => 'Custom food item added successfully',
                'food_item' => $item
            ]);
        } else {
            returnError('Failed to add custom food item: ' . $stmt->error, 500);
        }
        break;
        
    case 'PUT':
        // Update a custom food item
        $conn = getDbConnection();
        
        // Get request body
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        if (!isset($data['id'])) {
            returnError('Missing food item ID', 400);
        }
        
        // Check if the food item exists and belongs to the user
        $checkQuery = "SELECT * FROM user_food_items WHERE id = ? AND user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $data['id'], $userId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows === 0) {
            returnError('Food item not found or access denied', 404);
        }
        
        // Build update query
        $updateFields = [];
        $params = [];
        $types = "";
        
        if (isset($data['name'])) {
            $updateFields[] = "name = ?";
            $params[] = $data['name'];
            $types .= "s";
        }
        
        if (isset($data['calories'])) {
            $updateFields[] = "calories = ?";
            $params[] = $data['calories'];
            $types .= "i";
        }
        
        if (isset($data['protein'])) {
            $updateFields[] = "protein = ?";
            $params[] = $data['protein'];
            $types .= "d";
        }
        
        if (isset($data['carbs'])) {
            $updateFields[] = "carbs = ?";
            $params[] = $data['carbs'];
            $types .= "d";
        }
        
        if (isset($data['fat'])) {
            $updateFields[] = "fat = ?";
            $params[] = $data['fat'];
            $types .= "d";
        }
        
        if (isset($data['serving_size'])) {
            $updateFields[] = "serving_size = ?";
            $params[] = $data['serving_size'];
            $types .= "s";
        }
        
        if (empty($updateFields)) {
            returnError('No fields to update', 400);
        }
        
        // Add ID and user_id to params
        $params[] = $data['id'];
        $params[] = $userId;
        $types .= "ii";
        
        $updateQuery = "UPDATE user_food_items SET " . implode(", ", $updateFields) . " WHERE id = ? AND user_id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        
        // Bind parameters dynamically
        $bindParams = array_merge([$updateStmt, $types], $params);
        call_user_func_array('mysqli_stmt_bind_param', $bindParams);
        
        if ($updateStmt->execute()) {
            // Get the updated record
            $selectQuery = "SELECT * FROM user_food_items WHERE id = ?";
            $selectStmt = $conn->prepare($selectQuery);
            $selectStmt->bind_param("i", $data['id']);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $item = $result->fetch_assoc();
            $item['is_custom'] = true;
            
            returnResponse([
                'success' => true,
                'message' => 'Custom food item updated successfully',
                'food_item' => $item
            ]);
        } else {
            returnError('Failed to update custom food item: ' . $updateStmt->error, 500);
        }
        break;
        
    case 'DELETE':
        // Delete a custom food item
        $conn = getDbConnection();
        
        // Get item ID from query parameters
        $itemId = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($itemId <= 0) {
            returnError('Invalid food item ID', 400);
        }
        
        // Check if the food item exists and belongs to the user
        $checkQuery = "SELECT * FROM user_food_items WHERE id = ? AND user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $itemId, $userId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows === 0) {
            returnError('Food item not found or access denied', 404);
        }
        
        // Delete the food item
        $deleteQuery = "DELETE FROM user_food_items WHERE id = ? AND user_id = ?";
        $deleteStmt = $conn->prepare($deleteQuery);
        $deleteStmt->bind_param("ii", $itemId, $userId);
        
        if ($deleteStmt->execute()) {
            returnResponse([
                'success' => true,
                'message' => 'Custom food item deleted successfully'
            ]);
        } else {
            returnError('Failed to delete custom food item: ' . $deleteStmt->error, 500);
        }
        break;
        
    default:
        returnError('Method not allowed', 405);
}
