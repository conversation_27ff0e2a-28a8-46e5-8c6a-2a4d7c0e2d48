<?php
/**
 * Alter admin_users table to add role and parent_admin_id fields
 * Alter users table to add assigned_staff_id field
 */
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if 'role' column exists in admin_users table
$checkRoleColumn = $conn->query("SHOW COLUMNS FROM admin_users LIKE 'role'");
$roleColumnExists = $checkRoleColumn && $checkRoleColumn->num_rows > 0;

// If role column exists, check if it has the correct ENUM values
$needsRoleUpdate = false;
if ($roleColumnExists) {
    $roleColumn = $checkRoleColumn->fetch_assoc();
    // Check if the ENUM includes 'super_admin' and 'staff'
    if (strpos($roleColumn['Type'], "'super_admin'") === false || 
        strpos($roleColumn['Type'], "'staff'") === false) {
        $needsRoleUpdate = true;
        echo "Column 'role' in 'admin_users' table needs to be updated with new ENUM values.<br>";
    } else {
        echo "Column 'role' in 'admin_users' table already has the correct ENUM values.<br>";
    }
} else {
    echo "Column 'role' in 'admin_users' table does not exist and needs to be created.<br>";
}

// Check if 'parent_admin_id' column exists in admin_users table
$checkParentAdminIdColumn = $conn->query("SHOW COLUMNS FROM admin_users LIKE 'parent_admin_id'");
$parentAdminIdColumnExists = $checkParentAdminIdColumn && $checkParentAdminIdColumn->num_rows > 0;

if (!$parentAdminIdColumnExists) {
    echo "Column 'parent_admin_id' in 'admin_users' table does not exist and needs to be created.<br>";
}

// Check if 'assigned_staff_id' column exists in users table
$checkAssignedStaffIdColumn = $conn->query("SHOW COLUMNS FROM users LIKE 'assigned_staff_id'");
$assignedStaffIdColumnExists = $checkAssignedStaffIdColumn && $checkAssignedStaffIdColumn->num_rows > 0;

if (!$assignedStaffIdColumnExists) {
    echo "Column 'assigned_staff_id' in 'users' table does not exist and needs to be created.<br>";
}

// Perform the necessary alterations

// 1. Update or create 'role' column in admin_users table
if ($needsRoleUpdate) {
    $alterRoleQuery = "ALTER TABLE admin_users MODIFY COLUMN role ENUM('admin', 'editor', 'viewer', 'super_admin', 'staff') NOT NULL DEFAULT 'staff'";
    if ($conn->query($alterRoleQuery)) {
        echo "Column 'role' in 'admin_users' table updated successfully.<br>";
    } else {
        echo "Error updating column 'role' in 'admin_users' table: " . $conn->error . "<br>";
    }
} else if (!$roleColumnExists) {
    $addRoleQuery = "ALTER TABLE admin_users ADD COLUMN role ENUM('super_admin', 'staff') NOT NULL DEFAULT 'staff'";
    if ($conn->query($addRoleQuery)) {
        echo "Column 'role' added to 'admin_users' table successfully.<br>";
    } else {
        echo "Error adding column 'role' to 'admin_users' table: " . $conn->error . "<br>";
    }
}

// 2. Add 'parent_admin_id' column to admin_users table if it doesn't exist
if (!$parentAdminIdColumnExists) {
    $addParentAdminIdQuery = "ALTER TABLE admin_users ADD COLUMN parent_admin_id INT DEFAULT NULL";
    if ($conn->query($addParentAdminIdQuery)) {
        echo "Column 'parent_admin_id' added to 'admin_users' table successfully.<br>";
    } else {
        echo "Error adding column 'parent_admin_id' to 'admin_users' table: " . $conn->error . "<br>";
    }
}

// 3. Add 'assigned_staff_id' column to users table if it doesn't exist
if (!$assignedStaffIdColumnExists) {
    $addAssignedStaffIdQuery = "ALTER TABLE users ADD COLUMN assigned_staff_id INT DEFAULT NULL";
    if ($conn->query($addAssignedStaffIdQuery)) {
        echo "Column 'assigned_staff_id' added to 'users' table successfully.<br>";
    } else {
        echo "Error adding column 'assigned_staff_id' to 'users' table: " . $conn->error . "<br>";
    }
}

// Update existing admin users to be super_admin if they have the 'admin' role
if ($needsRoleUpdate) {
    $updateAdminRolesQuery = "UPDATE admin_users SET role = 'super_admin' WHERE role = 'admin'";
    if ($conn->query($updateAdminRolesQuery)) {
        echo "Updated existing admin users to 'super_admin' role.<br>";
    } else {
        echo "Error updating existing admin roles: " . $conn->error . "<br>";
    }
}

echo "Database schema update completed!";
?>
