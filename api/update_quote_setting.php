<?php
require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/utilities.php';
require_once '../includes/settings.php';

$auth = new Auth();

header('Content-Type: application/json');

// Log all POST data for debugging
file_put_contents(__DIR__ . '/update_quote_setting.log', date('c') . ' POST: ' . json_encode($_POST) . "\n", FILE_APPEND);

if (!$auth->hasRole('admin')) {
    file_put_contents(__DIR__ . '/update_quote_setting.log', date('c') . ' ERROR: Unauthorized' . "\n", FILE_APPEND);
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}
$settingsManager = Settings::getInstance();
$key = $_POST['key'] ?? '';
$value = $_POST['value'] ?? '';
if ($key) {
    $result = $settingsManager->set($key, $value);
    file_put_contents(__DIR__ . '/update_quote_setting.log', date('c') . " SET: $key = $value, result: " . var_export($result, true) . "\n", FILE_APPEND);
    echo json_encode(['success' => $result]);
} else {
    file_put_contents(__DIR__ . '/update_quote_setting.log', date('c') . ' ERROR: Invalid key' . "\n", FILE_APPEND);
    echo json_encode(['success' => false, 'message' => 'Invalid key']);
} 