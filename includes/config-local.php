<?php
// Local Database configuration for testing
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'root');
}
if (!defined('DB_PASS')) {
    define('DB_PASS', '');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'kft_admin_local');
}

// Application configuration
define('APP_NAME', 'KFT Fitness Admin');
if (!defined('APP_URL')) {
    define('APP_URL', 'http://localhost:8000');
}
define('APP_VERSION', '1.0.0');
// JWT secret for token generation
if (!defined('APP_SECRET')) {
    define('APP_SECRET', 'kft_fitness_jwt_secret_key_2025');
}

// Session configuration
define('SESSION_NAME', 'kft_admin_session');
define('SESSION_LIFETIME', 18000); // 5 hours

// Development mode
define('DEV_MODE', true);

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/settings.php';

// Initialize settings
$settingsManager = Settings::getInstance();

// Check if development mode is enabled
$isDevMode = $settingsManager->isEnabled('is_dev_mode', true);

// Error reporting based on environment
if ($isDevMode) {
    // Development mode: Show all errors
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);

    // Define development mode constant
    if (!defined('DEV_MODE')) {
        define('DEV_MODE', true);
    }
} else {
    // Production mode: Hide errors
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);

    // Define development mode constant
    if (!defined('DEV_MODE')) {
        define('DEV_MODE', false);
    }
}

// Time zone
date_default_timezone_set('UTC');
