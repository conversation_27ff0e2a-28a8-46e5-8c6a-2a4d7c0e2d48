<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isLoggedIn() || (!$auth->hasRole('admin') && !$auth->hasRole('super_admin') && !$auth->hasPermission('manage_users'))) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
$newPin = isset($_POST['new_pin']) ? trim($_POST['new_pin']) : '';
$singleUse = isset($_POST['single_use']) ? (bool)$_POST['single_use'] : false;

if (!$userId || !preg_match('/^\d{4}$/', $newPin)) {
    echo json_encode(['success' => false, 'message' => 'Invalid input.']);
    exit;
}

// Set expiration based on single-use flag
if ($singleUse) {
    // For single-use PINs, set a far future date but mark as single-use
    // The PIN will be invalidated after first successful login
    $expiresAt = date('Y-m-d H:i:s', strtotime('+1 year')); // Long expiry, but single-use
    $pinUsed = 0; // Mark as not used yet
} else {
    // Legacy 30-minute expiration for backward compatibility
    $expiresAt = date('Y-m-d H:i:s', strtotime('+30 minutes'));
    $pinUsed = null; // Not applicable for time-based expiry
}

$db = new Database();
$conn = $db->getConnection();

// Check if pin_used column exists, if not add it
$checkColumn = $conn->query("SHOW COLUMNS FROM users LIKE 'pin_used'");
if ($checkColumn->num_rows == 0) {
    $conn->query("ALTER TABLE users ADD COLUMN pin_used TINYINT(1) DEFAULT 0 AFTER pin_expires_at");
}

// Update PIN with single-use tracking
if ($singleUse) {
    $stmt = $conn->prepare('UPDATE users SET pin = ?, pin_expires_at = ?, pin_used = 0 WHERE id = ?');
    $stmt->bind_param('ssi', $newPin, $expiresAt, $userId);
} else {
    $stmt = $conn->prepare('UPDATE users SET pin = ?, pin_expires_at = ?, pin_used = NULL WHERE id = ?');
    $stmt->bind_param('ssi', $newPin, $expiresAt, $userId);
}

if ($stmt->execute()) {
    $response = [
        'success' => true,
        'pin' => $newPin,
        'expires_at' => $expiresAt,
        'single_use' => $singleUse
    ];
    echo json_encode($response);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to update PIN.']);
}
