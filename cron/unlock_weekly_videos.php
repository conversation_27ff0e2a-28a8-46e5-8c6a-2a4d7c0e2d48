<?php
/**
 * Weekly Video Unlock Cron Job
 * 
 * This script should be run once a day to unlock videos for users based on their enrollment date.
 * It will unlock videos for the current week based on the start date of the enrollment.
 * 
 * Example cron job (run daily at midnight):
 * 0 0 * * * php /path/to/admin/cron/unlock_weekly_videos.php
 */

// Set to true to enable debug output
$debug = false;

// Disable direct access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

// Include required files
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get current date
$currentDate = date('Y-m-d');

// Log function
function logMessage($message) {
    global $debug;
    if ($debug) {
        echo date('[Y-m-d H:i:s] ') . $message . PHP_EOL;
    }
}

logMessage("Starting weekly video unlock process...");

// Get all active enrollments
$enrollmentQuery = "SELECT e.id, e.user_id, e.course_id, e.start_date, c.duration_weeks
                   FROM user_course_enrollments e
                   JOIN courses c ON e.course_id = c.id
                   WHERE e.status = 'active'";
$enrollmentResult = $conn->query($enrollmentQuery);

$totalEnrollments = $enrollmentResult->num_rows;
logMessage("Found $totalEnrollments active enrollments");

$updatedCount = 0;

while ($enrollment = $enrollmentResult->fetch_assoc()) {
    $userId = $enrollment['user_id'];
    $courseId = $enrollment['course_id'];
    $startDate = $enrollment['start_date'];
    $durationWeeks = $enrollment['duration_weeks'];
    
    // Calculate the current week number based on start date
    $daysSinceStart = (strtotime($currentDate) - strtotime($startDate)) / (60 * 60 * 24);
    $currentWeek = floor($daysSinceStart / 7) + 1; // Add 1 to account for first week being unlocked immediately
    
    logMessage("User ID: $userId, Course ID: $courseId, Start Date: $startDate, Current Week: $currentWeek");
    
    // Only process if within the course duration
    if ($currentWeek <= $durationWeeks) {
        // Get videos for the current week that are not yet unlocked
        $videoQuery = "SELECT v.id, v.week_number
                      FROM course_videos v
                      LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
                      WHERE v.course_id = ? AND v.week_number = ? AND (p.is_unlocked IS NULL OR p.is_unlocked = 0)";
        $videoStmt = $conn->prepare($videoQuery);
        $videoStmt->bind_param("iii", $userId, $courseId, $currentWeek);
        $videoStmt->execute();
        $videoResult = $videoStmt->get_result();
        
        $videosToUnlock = $videoResult->num_rows;
        logMessage("Found $videosToUnlock videos to unlock for week $currentWeek");
        
        while ($video = $videoResult->fetch_assoc()) {
            $videoId = $video['id'];
            
            // Calculate the correct unlock date based on week number
            $daysToAdd = ($video['week_number'] - 1) * 7;
            $unlockDate = date('Y-m-d', strtotime($startDate . ' + ' . $daysToAdd . ' days'));
            
            // Check if progress record exists
            $checkQuery = "SELECT id FROM user_video_progress WHERE user_id = ? AND video_id = ?";
            $checkStmt = $conn->prepare($checkQuery);
            $checkStmt->bind_param("ii", $userId, $videoId);
            $checkStmt->execute();
            $checkResult = $checkStmt->get_result();
            
            if ($checkResult->num_rows > 0) {
                // Update existing record
                $progressId = $checkResult->fetch_assoc()['id'];
                $updateQuery = "UPDATE user_video_progress 
                               SET is_unlocked = 1, unlock_date = ?
                               WHERE id = ?";
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->bind_param("si", $unlockDate, $progressId);
                $updateStmt->execute();
            } else {
                // Insert new record
                $insertQuery = "INSERT INTO user_video_progress 
                              (user_id, video_id, is_unlocked, unlock_date)
                              VALUES (?, ?, 1, ?)";
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bind_param("iis", $userId, $videoId, $unlockDate);
                $insertStmt->execute();
            }
            
            $updatedCount++;
            logMessage("Unlocked video ID: $videoId for user ID: $userId");
        }
    } else {
        logMessage("User has completed the course duration ($durationWeeks weeks)");
    }
}

logMessage("Completed weekly video unlock process. Unlocked $updatedCount videos.");

// Close database connection
$conn->close();
?>
