<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - KFT Admin</title>
    <meta name="theme-color" content="#27ae60">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 40px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            background: #27ae60;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            font-size: 32px;
        }
        
        .offline-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #333;
        }
        
        .offline-message {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        
        .offline-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #27ae60;
            color: white;
        }
        
        .btn-primary:hover {
            background: #219150;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .offline-features {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #eee;
        }
        
        .features-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .features-list {
            list-style: none;
            text-align: left;
            max-width: 300px;
            margin: 0 auto;
        }
        
        .features-list li {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 14px;
            color: #555;
        }
        
        .features-list li::before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            font-size: 16px;
        }
        
        .connection-status {
            margin-top: 24px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-offline {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-online {
            background: #d1eddb;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                margin: 20px;
                padding: 32px 16px;
            }
            
            .offline-title {
                font-size: 24px;
            }
            
            .offline-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            Don't worry! You can still access some features of KFT Admin while offline. 
            Your data will sync automatically when you're back online.
        </p>
        
        <div class="offline-actions">
            <button class="btn btn-primary" onclick="tryReconnect()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
                Try Again
            </button>
            
            <a href="/" class="btn btn-secondary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                Go Home
            </a>
        </div>
        
        <div class="offline-features">
            <h3 class="features-title">Available Offline</h3>
            <ul class="features-list">
                <li>View cached dashboard data</li>
                <li>Browse previously loaded users</li>
                <li>Access course information</li>
                <li>View staff performance metrics</li>
            </ul>
        </div>
        
        <div id="connection-status" class="connection-status status-offline">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            No internet connection
        </div>
    </div>

    <script>
        function tryReconnect() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="animation: spin 1s linear infinite;"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg>Checking...';
            button.disabled = true;
            
            // Try to fetch a small resource to check connectivity
            fetch('/', { method: 'HEAD', cache: 'no-cache' })
                .then(() => {
                    // Connection restored
                    window.location.reload();
                })
                .catch(() => {
                    // Still offline
                    button.innerHTML = originalText;
                    button.disabled = false;
                    
                    // Show temporary message
                    const status = document.getElementById('connection-status');
                    const originalStatus = status.innerHTML;
                    status.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>Still offline - please check your connection';
                    
                    setTimeout(() => {
                        status.innerHTML = originalStatus;
                    }, 3000);
                });
        }
        
        // Monitor connection status
        function updateConnectionStatus() {
            const status = document.getElementById('connection-status');
            
            if (navigator.onLine) {
                status.className = 'connection-status status-online';
                status.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>Connection restored! Refreshing...';
                
                // Auto-refresh when connection is restored
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                status.className = 'connection-status status-offline';
                status.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>No internet connection';
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
    </script>
    
    <style>
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
