<?php
/**
 * Create admin_permissions and admin_user_permissions tables
 */
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if admin_permissions table exists
$checkPermissionsTable = $conn->query("SHOW TABLES LIKE 'admin_permissions'");
$permissionsTableExists = $checkPermissionsTable && $checkPermissionsTable->num_rows > 0;

// Check if admin_user_permissions table exists
$checkUserPermissionsTable = $conn->query("SHOW TABLES LIKE 'admin_user_permissions'");
$userPermissionsTableExists = $checkUserPermissionsTable && $checkUserPermissionsTable->num_rows > 0;

// Create admin_permissions table if it doesn't exist
if (!$permissionsTableExists) {
    $createPermissionsTable = "
    CREATE TABLE `admin_permissions` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `slug` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    if ($conn->query($createPermissionsTable)) {
        echo "Table 'admin_permissions' created successfully.<br>";
        
        // Insert default permissions
        $defaultPermissions = [
            ['name' => 'Manage Users', 'slug' => 'manage_users', 'description' => 'Create, edit, and delete users'],
            ['name' => 'Manage Courses', 'slug' => 'manage_courses', 'description' => 'Create, edit, and delete courses'],
            ['name' => 'Manage Workouts', 'slug' => 'manage_workouts', 'description' => 'Create, edit, and delete workouts'],
            ['name' => 'Manage Quotes', 'slug' => 'manage_quotes', 'description' => 'Create, edit, and delete motivational quotes'],
            ['name' => 'View Reports', 'slug' => 'view_reports', 'description' => 'View system reports and analytics'],
            ['name' => 'Manage Settings', 'slug' => 'manage_settings', 'description' => 'Modify system settings'],
            ['name' => 'Manage Staff', 'slug' => 'manage_staff', 'description' => 'Create, edit, and delete staff accounts'],
            ['name' => 'Chat with Users', 'slug' => 'chat_with_users', 'description' => 'Send and receive messages from users'],
            ['name' => 'Manage Food Database', 'slug' => 'manage_food_database', 'description' => 'Add, edit, and delete food items in the calorie tracker database'],
            ['name' => 'Assign Users', 'slug' => 'assign_users', 'description' => 'Assign users to staff members']
        ];
        
        $insertPermissionQuery = "INSERT INTO admin_permissions (name, slug, description) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($insertPermissionQuery);
        
        foreach ($defaultPermissions as $permission) {
            $stmt->bind_param("sss", $permission['name'], $permission['slug'], $permission['description']);
            if ($stmt->execute()) {
                echo "Added permission: {$permission['name']}<br>";
            } else {
                echo "Error adding permission {$permission['name']}: " . $conn->error . "<br>";
            }
        }
    } else {
        echo "Error creating table 'admin_permissions': " . $conn->error . "<br>";
    }
} else {
    echo "Table 'admin_permissions' already exists.<br>";
}

// Create admin_user_permissions table if it doesn't exist
if (!$userPermissionsTableExists) {
    $createUserPermissionsTable = "
    CREATE TABLE `admin_user_permissions` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `admin_user_id` bigint unsigned NOT NULL,
        `permission_id` bigint unsigned NOT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `admin_user_permission` (`admin_user_id`, `permission_id`),
        CONSTRAINT `fk_admin_user_permissions_admin_user_id` FOREIGN KEY (`admin_user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `fk_admin_user_permissions_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `admin_permissions` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    if ($conn->query($createUserPermissionsTable)) {
        echo "Table 'admin_user_permissions' created successfully.<br>";
        
        // Grant all permissions to existing admin users
        $adminUsersQuery = "SELECT id FROM admin_users WHERE role IN ('admin', 'super_admin')";
        $adminUsersResult = $conn->query($adminUsersQuery);
        
        if ($adminUsersResult && $adminUsersResult->num_rows > 0) {
            $permissionsQuery = "SELECT id FROM admin_permissions";
            $permissionsResult = $conn->query($permissionsQuery);
            $permissions = [];
            
            if ($permissionsResult && $permissionsResult->num_rows > 0) {
                while ($row = $permissionsResult->fetch_assoc()) {
                    $permissions[] = $row['id'];
                }
                
                $insertUserPermissionQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
                $stmt = $conn->prepare($insertUserPermissionQuery);
                
                while ($adminUser = $adminUsersResult->fetch_assoc()) {
                    foreach ($permissions as $permissionId) {
                        $stmt->bind_param("ii", $adminUser['id'], $permissionId);
                        $stmt->execute();
                    }
                    echo "Granted all permissions to admin user ID: {$adminUser['id']}<br>";
                }
            }
        }
    } else {
        echo "Error creating table 'admin_user_permissions': " . $conn->error . "<br>";
    }
} else {
    echo "Table 'admin_user_permissions' already exists.<br>";
}

echo "Admin permissions setup completed!";
?>
