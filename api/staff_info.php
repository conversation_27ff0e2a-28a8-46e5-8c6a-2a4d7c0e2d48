<?php
// staff_info.php: Return staff WhatsApp/phone number by staff ID
require_once 'config.php';

header('Content-Type: application/json; charset=UTF-8');

// Get staff_id from GET or POST
$staffId = isset($_GET['staff_id']) ? intval($_GET['staff_id']) : (isset($_POST['staff_id']) ? intval($_POST['staff_id']) : 0);
if ($staffId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Missing or invalid staff_id']);
    exit;
}

$conn = getDbConnection();
$stmt = $conn->prepare('SELECT id, name, phone FROM admin_users WHERE id = ? AND role = "staff" LIMIT 1');
$stmt->bind_param('i', $staffId);
$stmt->execute();
$result = $stmt->get_result();
if ($result && $result->num_rows > 0) {
    $staff = $result->fetch_assoc();
    echo json_encode([
        'success' => true,
        'staff_id' => $staff['id'],
        'name' => $staff['name'],
        'phone' => $staff['phone'],
    ]);
} else {
    echo json_encode(['success' => false, 'error' => 'Staff not found']);
} 