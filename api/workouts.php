<?php
/**
 * API endpoint for workouts
 * 
 * This endpoint returns workouts for the authenticated user
 */

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Handle CORS
handleCors();

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get auth token from header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access',
        'message' => 'Authentication failed'
    ]);
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Handle different request methods
switch ($method) {
    case 'GET':
        getWorkouts($conn, $userId);
        break;
    case 'POST':
        createWorkout($conn, $userId);
        break;
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed',
            'message' => 'Only GET and POST methods are allowed'
        ]);
        break;
}

// Function to get workouts for a user
function getWorkouts($conn, $userId) {
    // Get query parameters
    $type = isset($_GET['type']) ? $_GET['type'] : null;
    $status = isset($_GET['status']) ? $_GET['status'] : null;
    $sortBy = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'start_date';
    
    // Build the query
    $query = "SELECT * FROM workouts WHERE user_id = ?";
    $params = [$userId];
    $types = "i";
    
    if ($type) {
        $query .= " AND type = ?";
        $params[] = $type;
        $types .= "s";
    }
    
    if ($status) {
        $query .= " AND status = ?";
        $params[] = $status;
        $types .= "s";
    }
    
    // Add sorting
    switch ($sortBy) {
        case 'title':
            $query .= " ORDER BY title ASC";
            break;
        case 'type':
            $query .= " ORDER BY type ASC";
            break;
        case 'status':
            $query .= " ORDER BY status ASC";
            break;
        case 'start_date_desc':
            $query .= " ORDER BY start_date DESC";
            break;
        case 'start_date':
        default:
            $query .= " ORDER BY start_date ASC";
            break;
    }
    
    // Prepare and execute the query
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $workouts = [];
    while ($row = $result->fetch_assoc()) {
        $workouts[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'workouts' => $workouts
    ]);
    
    $stmt->close();
}

// Function to create a new workout
function createWorkout($conn, $userId) {
    // Get request body
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    if (!isset($data['title']) || empty($data['title'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid request',
            'message' => 'Workout title is required'
        ]);
        return;
    }
    
    if (!isset($data['type']) || empty($data['type'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid request',
            'message' => 'Workout type is required'
        ]);
        return;
    }
    
    if (!isset($data['start_date']) || empty($data['start_date'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid request',
            'message' => 'Start date is required'
        ]);
        return;
    }
    
    if (!isset($data['end_date']) || empty($data['end_date'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid request',
            'message' => 'End date is required'
        ]);
        return;
    }
    
    // Calculate duration in days
    $startDate = new DateTime($data['start_date']);
    $endDate = new DateTime($data['end_date']);
    $durationDays = $endDate->diff($startDate)->days + 1;
    
    // Prepare and execute the query
    $stmt = $conn->prepare("INSERT INTO workouts (user_id, title, type, start_date, end_date, duration_days, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $status = 'Pending'; // Default status for new workouts
    $stmt->bind_param("isssssi", $userId, $data['title'], $data['type'], $data['start_date'], $data['end_date'], $durationDays, $status);
    
    if ($stmt->execute()) {
        $workoutId = $conn->insert_id;
        
        // Get the newly created workout
        $selectStmt = $conn->prepare("SELECT * FROM workouts WHERE id = ?");
        $selectStmt->bind_param("i", $workoutId);
        $selectStmt->execute();
        $result = $selectStmt->get_result();
        $workout = $result->fetch_assoc();
        $selectStmt->close();
        
        echo json_encode([
            'success' => true,
            'message' => 'Workout created successfully',
            'workout' => $workout
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Database error',
            'message' => 'Failed to create workout: ' . $stmt->error
        ]);
    }
    
    $stmt->close();
}
