<?php
require_once __DIR__ . '/../api/config.php';
$conn = getDbConnection();

// Check if users.id is already unsigned
$colCheck = $conn->query("SHOW COLUMNS FROM users LIKE 'id'");
$needsAlter = false;
if ($colCheck && $colCheck->num_rows > 0) {
    $col = $colCheck->fetch_assoc();
    if (strpos($col['Type'], 'unsigned') === false) {
        $needsAlter = true;
    }
}

if ($needsAlter) {
    $alter = $conn->query("ALTER TABLE users MODIFY COLUMN id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY");
    if ($alter) {
        echo "Column 'id' in 'users' table altered to UNSIGNED successfully.\n";
    } else {
        echo "Error altering column: " . $conn->error . "\n";
    }
} else {
    echo "Column 'id' in 'users' table is already UNSIGNED.\n";
}
$conn->close(); 