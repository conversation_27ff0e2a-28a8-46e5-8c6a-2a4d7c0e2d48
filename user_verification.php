<?php
require_once 'includes/header.php';

// Check if admin is logged in
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    Utilities::setFlashMessage('danger', 'You do not have permission to access this page.');
    Utilities::redirect('login.php');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('danger', 'Invalid user ID.');
    Utilities::redirect('users.php');
}

$userId = (int)$_GET['id'];

// Get user details
$userQuery = "SELECT id, username, name, phone_number, email, verification_code, verification_expires_at FROM users WHERE id = ?";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('danger', 'User not found.');
    Utilities::redirect('users.php');
}

$user = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'generate') {
        // Generate a new verification code
        $verificationCode = sprintf("%06d", mt_rand(100000, 999999));
        
        // Set expiration time (24 hours from now)
        $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        // Update user with new verification code
        $updateQuery = "UPDATE users SET verification_code = ?, verification_expires_at = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ssi", $verificationCode, $expiresAt, $userId);
        
        if ($stmt->execute()) {
            // Log the action
            $adminId = $auth->getUserId();
            $logQuery = "INSERT INTO admin_logs (admin_id, action, target_id, details, ip_address, success) 
                        VALUES (?, 'generate_verification', ?, ?, ?, 1)";
            $stmt = $conn->prepare($logQuery);
            $details = "Generated verification code for user ID: $userId";
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            $stmt->bind_param("iiss", $adminId, $userId, $details, $ipAddress);
            $stmt->execute();
            
            // Refresh user data
            $stmt = $conn->prepare($userQuery);
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            Utilities::setFlashMessage('success', 'Verification code has been generated successfully.');
        } else {
            Utilities::setFlashMessage('danger', 'Failed to generate verification code: ' . $conn->error);
        }
    } elseif ($action === 'clear') {
        // Clear verification code
        $updateQuery = "UPDATE users SET verification_code = NULL, verification_expires_at = NULL WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("i", $userId);
        
        if ($stmt->execute()) {
            // Log the action
            $adminId = $auth->getUserId();
            $logQuery = "INSERT INTO admin_logs (admin_id, action, target_id, details, ip_address, success) 
                        VALUES (?, 'clear_verification', ?, ?, ?, 1)";
            $stmt = $conn->prepare($logQuery);
            $details = "Cleared verification code for user ID: $userId";
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            $stmt->bind_param("iiss", $adminId, $userId, $details, $ipAddress);
            $stmt->execute();
            
            // Refresh user data
            $stmt = $conn->prepare($userQuery);
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            Utilities::setFlashMessage('success', 'Verification code has been cleared successfully.');
        } else {
            Utilities::setFlashMessage('danger', 'Failed to clear verification code: ' . $conn->error);
        }
    }
}

// Check if verification code is expired
$isExpired = false;
if (!empty($user['verification_expires_at'])) {
    $expiryTime = strtotime($user['verification_expires_at']);
    $currentTime = time();
    $isExpired = $currentTime > $expiryTime;
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Manage Verification Code</h1>
    <a href="users.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Users
    </a>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> You are managing verification code for <strong><?php echo htmlspecialchars($user['name']); ?></strong> (Username: <strong><?php echo htmlspecialchars($user['username']); ?></strong>).
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <h5>User Information</h5>
                <table class="table">
                    <tr>
                        <th>Name:</th>
                        <td><?php echo htmlspecialchars($user['name']); ?></td>
                    </tr>
                    <tr>
                        <th>Username:</th>
                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                    </tr>
                    <tr>
                        <th>Phone Number:</th>
                        <td><?php echo htmlspecialchars($user['phone_number'] ?? 'N/A'); ?></td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td><?php echo htmlspecialchars($user['email'] ?? 'N/A'); ?></td>
                    </tr>
                </table>
            </div>
            
            <div class="col-md-6">
                <h5>Verification Status</h5>
                <?php if (empty($user['verification_code'])): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> No verification code has been generated for this user.
                    </div>
                <?php else: ?>
                    <div class="alert <?php echo $isExpired ? 'alert-danger' : 'alert-success'; ?>">
                        <i class="fas <?php echo $isExpired ? 'fa-times-circle' : 'fa-check-circle'; ?> me-2"></i>
                        <?php if ($isExpired): ?>
                            Verification code has expired.
                        <?php else: ?>
                            Verification code is active.
                        <?php endif; ?>
                    </div>
                    
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">Verification Code</h6>
                            <div class="d-flex align-items-center">
                                <h3 class="mb-0 me-3"><?php echo htmlspecialchars($user['verification_code']); ?></h3>
                                <button class="btn btn-sm btn-outline-secondary" id="copyCode">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                            <p class="card-text mt-2 mb-0">
                                <small class="text-muted">
                                    Expires: <?php echo date('M d, Y h:i A', strtotime($user['verification_expires_at'])); ?>
                                </small>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <h5>Manage Verification Code</h5>
                <form method="post" action="user_verification.php?id=<?php echo $userId; ?>" class="d-flex gap-2">
                    <?php if (empty($user['verification_code']) || $isExpired): ?>
                        <input type="hidden" name="action" value="generate">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-2"></i> Generate New Code
                        </button>
                    <?php else: ?>
                        <input type="hidden" name="action" value="generate">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-2"></i> Regenerate Code
                        </button>
                        
                        <button type="submit" class="btn btn-outline-danger" name="action" value="clear">
                            <i class="fas fa-trash me-2"></i> Clear Code
                        </button>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <h5>Instructions for User</h5>
        <p>Provide the following instructions to the user:</p>
        
        <ol>
            <li>Open the KFT Fitness app on their mobile device</li>
            <li>Enter their phone number: <strong><?php echo htmlspecialchars($user['phone_number'] ?? 'N/A'); ?></strong></li>
            <li>When prompted for verification, enter the code: <strong><?php echo htmlspecialchars($user['verification_code'] ?? 'Not generated yet'); ?></strong></li>
            <li>The code will expire on: <strong><?php echo !empty($user['verification_expires_at']) ? date('M d, Y h:i A', strtotime($user['verification_expires_at'])) : 'N/A'; ?></strong></li>
        </ol>
        
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i> For security reasons, verification codes should be communicated to users through a secure channel.
        </div>
    </div>
</div>

<script>
// Copy verification code to clipboard
document.getElementById('copyCode')?.addEventListener('click', function() {
    const code = '<?php echo $user['verification_code'] ?? ''; ?>';
    navigator.clipboard.writeText(code).then(function() {
        // Change button text temporarily
        const button = document.getElementById('copyCode');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
