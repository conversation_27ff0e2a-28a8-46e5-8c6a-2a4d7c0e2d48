<?php
/**
 * Improved Admin Add Page
 * 
 * This page allows super admins to add new staff members with enhanced validation
 * and improved user experience.
 */

// Enable error reporting for development
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Add dashboard-new.css for consistent styling
echo '<link rel="stylesheet" href="assets/css/dashboard-new.css">';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user has permission to access this page
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to add staff members.');
    Utilities::redirect('index.php');
    exit;
}

// Get all super_admin users for parent selection
$superAdminsQuery = "SELECT id, username, name FROM admin_users WHERE role = 'super_admin'";
$superAdminsResult = $conn->query($superAdminsQuery);
$superAdmins = [];
if ($superAdminsResult && $superAdminsResult->num_rows > 0) {
    while ($row = $superAdminsResult->fetch_assoc()) {
        $superAdmins[] = $row;
    }
}

// Get all permissions
$permissionsQuery = "SELECT id, name, slug, description FROM admin_permissions ORDER BY name";
$permissionsResult = $conn->query($permissionsQuery);
$permissions = [];
if ($permissionsResult && $permissionsResult->num_rows > 0) {
    while ($row = $permissionsResult->fetch_assoc()) {
        $permissions[] = $row;
    }
}

// Group permissions by category for better organization
$permissionCategories = [
    'user_management' => ['title' => 'User Management', 'permissions' => []],
    'content_management' => ['title' => 'Content Management', 'permissions' => []],
    'system' => ['title' => 'System', 'permissions' => []],
    'other' => ['title' => 'Other', 'permissions' => []]
];

foreach ($permissions as $permission) {
    $category = 'other';
    
    // Categorize permissions based on slug
    if (strpos($permission['slug'], 'user') !== false || 
        strpos($permission['slug'], 'staff') !== false) {
        $category = 'user_management';
    } elseif (strpos($permission['slug'], 'course') !== false || 
              strpos($permission['slug'], 'video') !== false || 
              strpos($permission['slug'], 'content') !== false) {
        $category = 'content_management';
    } elseif (strpos($permission['slug'], 'setting') !== false || 
              strpos($permission['slug'], 'system') !== false || 
              strpos($permission['slug'], 'admin') !== false) {
        $category = 'system';
    }
    
    $permissionCategories[$category]['permissions'][] = $permission;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = Utilities::sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $email = Utilities::sanitizeInput($_POST['email'] ?? '');
    $name = Utilities::sanitizeInput($_POST['name'] ?? '');
    $phone = Utilities::sanitizeInput($_POST['phone'] ?? '');
    $role = isset($_POST['role']) ? $_POST['role'] : 'staff'; // Allow selection of role
    $parentAdminId = isset($_POST['parent_admin_id']) ? (int)$_POST['parent_admin_id'] : null;
    $selectedPermissions = $_POST['permissions'] ?? [];
    
    // Validate form data
    $errors = [];
    
    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (strlen($username) < 4) {
        $errors[] = 'Username must be at least 4 characters long.';
    } else {
        // Check if username already exists
        $checkUsernameQuery = "SELECT id FROM admin_users WHERE username = ?";
        $stmt = $conn->prepare($checkUsernameQuery);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $errors[] = 'Username already exists.';
        }
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    } else {
        $policyResult = validate_password_policy($password);
        if ($policyResult !== true) {
            $errors[] = $policyResult;
        }
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    } else {
        // Check if email already exists
        $checkEmailQuery = "SELECT id FROM admin_users WHERE email = ?";
        $stmt = $conn->prepare($checkEmailQuery);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $errors[] = 'Email already exists.';
        }
    }
    
    if (empty($name)) {
        $errors[] = 'Name is required.';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone is required.';
    }
    
    if ($role === 'staff' && empty($parentAdminId)) {
        $errors[] = 'Parent admin is required for staff members.';
    }
    
    // If no errors, insert the staff member
    if (empty($errors)) {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert staff member
            $insertStaffQuery = "INSERT INTO admin_users (username, password, email, name, phone, role, parent_admin_id) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertStaffQuery);
            $stmt->bind_param("ssssssi", $username, $hashedPassword, $email, $name, $phone, $role, $parentAdminId);
            $stmt->execute();
            
            // Get the new staff member ID
            $staffId = $conn->insert_id;
            
            // Insert permissions
            if (!empty($selectedPermissions)) {
                $insertPermissionQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
                $stmt = $conn->prepare($insertPermissionQuery);
                
                foreach ($selectedPermissions as $permissionId) {
                    $stmt->bind_param("ii", $staffId, $permissionId);
                    $stmt->execute();
                }
            }
            
            // Commit transaction
            $conn->commit();
            
            Utilities::setFlashMessage('success', "Staff member {$name} has been added successfully.");
            Utilities::redirect('staff_management.php');
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            Utilities::setFlashMessage('danger', 'Failed to add staff member: ' . $e->getMessage());
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Add Staff Member</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="staff_management.php">Staff Management</a></li>
        <li class="breadcrumb-item active">Add Staff Member</li>
    </ol>
    
    <div class="card mb-4 border-0 shadow-sm hover-lift">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i>Staff Information</h5>
        </div>
        <div class="card-body">
            <form method="post" action="" id="staffForm">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="username" name="username" type="text" placeholder="Enter username" required value="<?php echo isset($username) ? htmlspecialchars($username) : ''; ?>" />
                            <label for="username">Username</label>
                            <small class="text-muted">Must be at least 4 characters long</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="email" name="email" type="email" placeholder="Enter email" required value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" />
                            <label for="email">Email</label>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="password" name="password" type="password" placeholder="Enter password" required />
                            <label for="password">Password</label>
                            <small class="text-muted">Must be at least 8 characters with uppercase and number</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="confirm_password" name="confirm_password" type="password" placeholder="Confirm password" required />
                            <label for="confirm_password">Confirm Password</label>
                            <div id="password-match-feedback" class="invalid-feedback">
                                Passwords do not match
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="name" name="name" type="text" placeholder="Enter full name" required value="<?php echo isset($name) ? htmlspecialchars($name) : ''; ?>" />
                            <label for="name">Full Name</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input class="form-control" id="phone" name="phone" type="text" placeholder="Enter phone number" required value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>" />
                            <label for="phone">Phone</label>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="role" name="role" required>
                                <option value="staff" selected>Staff</option>
                                <option value="manager">Manager</option>
                                <?php if ($auth->hasRole('super_admin')): ?>
                                <option value="super_admin">Super Admin</option>
                                <?php endif; ?>
                            </select>
                            <label for="role">Role</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3" id="parent-admin-container">
                            <select class="form-select" id="parent_admin_id" name="parent_admin_id">
                                <option value="">Select Parent Admin</option>
                                <?php foreach ($superAdmins as $admin): ?>
                                <option value="<?php echo $admin['id']; ?>" <?php echo (isset($parentAdminId) && $parentAdminId == $admin['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($admin['name'] . ' (' . $admin['username'] . ')'); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <label for="parent_admin_id">Parent Admin</label>
                            <small class="text-muted">Required for staff members</small>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><i class="fas fa-key me-2"></i>Permissions</h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllPermissions">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="deselectAllPermissions">Deselect All</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="permissionsAccordion">
                            <?php foreach ($permissionCategories as $categoryKey => $category): ?>
                                <?php if (!empty($category['permissions'])): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading<?php echo ucfirst($categoryKey); ?>">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo ucfirst($categoryKey); ?>" aria-expanded="true" aria-controls="collapse<?php echo ucfirst($categoryKey); ?>">
                                            <?php echo $category['title']; ?>
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo ucfirst($categoryKey); ?>" class="accordion-collapse collapse show" aria-labelledby="heading<?php echo ucfirst($categoryKey); ?>" data-bs-parent="#permissionsAccordion">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <?php foreach ($category['permissions'] as $permission): ?>
                                                <div class="col-md-4 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input permission-checkbox" type="checkbox" id="permission_<?php echo $permission['id']; ?>" name="permissions[]" value="<?php echo $permission['id']; ?>" <?php echo (isset($selectedPermissions) && in_array($permission['id'], $selectedPermissions)) ? 'checked' : ''; ?> />
                                                        <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>">
                                                            <?php echo htmlspecialchars($permission['name']); ?>
                                                            <small class="d-block text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 mb-0">
                    <div class="d-flex justify-content-between">
                        <a href="staff_management.php" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Add Staff Member</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password matching validation
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const passwordMatchFeedback = document.getElementById('password-match-feedback');
    
    function validatePasswordMatch() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
            confirmPassword.classList.add('is-invalid');
            passwordMatchFeedback.style.display = 'block';
        } else {
            confirmPassword.setCustomValidity('');
            confirmPassword.classList.remove('is-invalid');
            passwordMatchFeedback.style.display = 'none';
        }
    }
    
    password.addEventListener('change', validatePasswordMatch);
    confirmPassword.addEventListener('keyup', validatePasswordMatch);
    
    // Role-based parent admin requirement
    const roleSelect = document.getElementById('role');
    const parentAdminContainer = document.getElementById('parent-admin-container');
    const parentAdminSelect = document.getElementById('parent_admin_id');
    
    roleSelect.addEventListener('change', function() {
        if (this.value === 'staff') {
            parentAdminContainer.style.display = 'block';
            parentAdminSelect.setAttribute('required', 'required');
        } else {
            parentAdminContainer.style.display = 'none';
            parentAdminSelect.removeAttribute('required');
        }
    });
    
    // Select/Deselect All Permissions
    const selectAllBtn = document.getElementById('selectAllPermissions');
    const deselectAllBtn = document.getElementById('deselectAllPermissions');
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    
    selectAllBtn.addEventListener('click', function() {
        permissionCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    });
    
    deselectAllBtn.addEventListener('click', function() {
        permissionCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    });
    
    // Form validation
    const form = document.getElementById('staffForm');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
