<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Test URL from the debug output
$testUrl = 'https://vimeo.com/1087487482/ae75b6e329?share=copy';

function extractVimeoPrivateHash($url) {
    if (empty($url)) return null;
    
    // Pattern for private Vimeo URLs: https://vimeo.com/123456789/abcdef1234
    if (preg_match('/vimeo\.com\/[0-9]+\/([a-zA-Z0-9]+)/', $url, $matches)) {
        return $matches[1];
    }
    
    // Pattern for embed URLs with hash: https://player.vimeo.com/video/123456789?h=abcdef1234
    if (preg_match('/player\.vimeo\.com\/video\/[0-9]+\?h=([a-zA-Z0-9]+)/', $url, $matches)) {
        return $matches[1];
    }
    
    return null;
}

function extractVimeoId($url) {
    if (empty($url)) return null;
    
    // Extract video ID from various Vimeo URL formats
    if (preg_match('/vimeo\.com\/(?:video\/)?([0-9]+)/', $url, $matches)) {
        return $matches[1];
    }
    
    return null;
}

function buildFallbackEmbedUrl($url) {
    $vimeoId = extractVimeoId($url);
    if (!$vimeoId) {
        return null;
    }
    
    // Check if this is a private video with hash
    $privateHash = extractVimeoPrivateHash($url);
    
    // Build URL with or without private hash
    $baseUrl = "https://player.vimeo.com/video/{$vimeoId}";
    $params = [
        'autoplay' => '0',
        'title' => '0',
        'byline' => '0',
        'portrait' => '0',
        'responsive' => '1',
        'dnt' => '1',
    ];
    
    // Add private hash if available
    if ($privateHash) {
        $params['h'] = $privateHash;
    }
    
    $queryString = http_build_query($params);
    return $baseUrl . '?' . $queryString;
}

$vimeoId = extractVimeoId($testUrl);
$privateHash = extractVimeoPrivateHash($testUrl);
$fallbackUrl = buildFallbackEmbedUrl($testUrl);

echo json_encode([
    'success' => true,
    'test_url' => $testUrl,
    'extracted_vimeo_id' => $vimeoId,
    'extracted_private_hash' => $privateHash,
    'fallback_embed_url' => $fallbackUrl,
    'test_results' => [
        'vimeo_id_extracted' => $vimeoId ? 'YES' : 'NO',
        'private_hash_extracted' => $privateHash ? 'YES' : 'NO',
        'fallback_url_generated' => $fallbackUrl ? 'YES' : 'NO'
    ],
    'recommendations' => [
        'step1' => 'Test the fallback URL in a browser',
        'step2' => 'If it works, the issue is in the Flutter implementation',
        'step3' => 'If it doesn\'t work, check Vimeo privacy settings'
    ]
]);
?>
