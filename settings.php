<?php
require_once 'includes/header.php';

// Check if user has admin privileges
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('warning', 'You do not have permission to access the settings page.');
    Utilities::redirect('index.php');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Initialize settings manager
$settingsManager = Settings::getInstance();

// Get active tab from URL or default to 'staff'
$activeTab = isset($_GET['tab']) ? $_GET['tab'] : 'staff';

// Load settings page specific CSS
echo '<link rel="stylesheet" href="assets/css/settings-page.css">';
// Load modern action menu CSS
echo '<link rel="stylesheet" href="assets/css/modern-action-menu.css">';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle admin user management
    if (isset($_POST['action']) && $_POST['action'] === 'add_admin') {
        $username = Utilities::sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $email = Utilities::sanitizeInput($_POST['email'] ?? '');
        $role = Utilities::sanitizeInput($_POST['role'] ?? 'editor');

        // Validate inputs
        $errors = [];

        if (empty($username)) {
            $errors[] = 'Username is required.';
        }

        if (empty($password)) {
            $errors[] = 'Password is required.';
        } elseif (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        }

        if (empty($email)) {
            $errors[] = 'Email is required.';
        } elseif (!Utilities::validateEmail($email)) {
            $errors[] = 'Please enter a valid email address.';
        }

        // Check if username or email already exists
        $checkQuery = "SELECT id FROM admin_users WHERE username = ? OR email = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ss", $username, $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errors[] = 'Username or email already exists.';
        }

        if (empty($errors)) {
            // Register new admin user
            if ($auth->register($username, $password, $email, $role)) {
                Utilities::setFlashMessage('success', 'Admin user has been added successfully.');
                Utilities::redirect('settings.php');
            } else {
                Utilities::setFlashMessage('danger', 'Failed to add admin user.');
            }
        } else {
            // Display errors
            foreach ($errors as $error) {
                Utilities::setFlashMessage('danger', $error);
            }
        }
    }

    // Handle admin user deletion
    if (isset($_POST['action']) && $_POST['action'] === 'delete_admin') {
        $adminId = (int)$_POST['admin_id'];

        // Prevent deleting your own account
        if ($adminId === (int)$_SESSION['user_id']) {
            Utilities::setFlashMessage('warning', 'You cannot delete your own account.');
            Utilities::redirect('settings.php');
        }

        $deleteQuery = "DELETE FROM admin_users WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $adminId);

        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Admin user has been deleted successfully.');
        } else {
            Utilities::setFlashMessage('danger', 'Failed to delete admin user.');
        }

        Utilities::redirect('settings.php');
    }
}

// Get all admin users
$adminQuery = "SELECT a.*,
               CASE
                   WHEN a.parent_admin_id IS NOT NULL THEN p.name
                   ELSE NULL
               END as parent_name
               FROM admin_users a
               LEFT JOIN admin_users p ON a.parent_admin_id = p.id
               ORDER BY a.role, a.created_at DESC";
$adminResult = $conn->query($adminQuery);
?>

<!-- Settings Page Header -->
<div class="settings-header">
    <div class="settings-header-icon">
        <i class="fas fa-cog"></i>
    </div>
    <h1>Platform Settings</h1>
</div>

<!-- Settings Navigation -->
<div class="settings-nav">
    <ul class="settings-nav-tabs">
        <li class="settings-nav-item">
            <a href="settings.php?tab=staff" class="settings-nav-link <?php echo $activeTab === 'staff' ? 'active' : ''; ?>">
                <i class="fas fa-user-shield settings-nav-icon"></i>
                Admin & Staff
            </a>
        </li>
        <li class="settings-nav-item">
            <a href="settings.php?tab=platform" class="settings-nav-link <?php echo $activeTab === 'platform' ? 'active' : ''; ?>">
                <i class="fas fa-sliders-h settings-nav-icon"></i>
                Platform Settings
            </a>
        </li>
        <li class="settings-nav-item">
            <a href="settings.php?tab=security" class="settings-nav-link <?php echo $activeTab === 'security' ? 'active' : ''; ?>">
                <i class="fas fa-shield-alt settings-nav-icon"></i>
                Security
            </a>
        </li>
    </ul>
</div>

<div class="row">
    <!-- Settings Content -->
    <div class="col-12 mb-4">
        <div class="settings-content">
            <?php if ($activeTab === 'staff'): ?>
            <!-- Admin & Staff Management Section -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">
                        <i class="fas fa-user-shield settings-section-icon"></i>
                        Admin & Staff Management
                    </h2>
                    <div class="settings-section-actions">
                        <div class="settings-search-container me-2">
                            <i class="fas fa-search settings-search-icon"></i>
                            <input type="text" class="form-control settings-search-input" id="adminSearchInput" placeholder="Search users...">
                        </div>
                        <div class="btn-group">
                            <a href="admin_add.php" class="btn btn-settings btn-settings-outline">
                                <i class="fas fa-user-plus me-md-2"></i><span class="d-none d-md-inline">Add Staff</span>
                            </a>
                            <button type="button" class="btn btn-settings btn-settings-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                                <i class="fas fa-plus me-md-2"></i><span class="d-none d-md-inline">Add Admin</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="admin-table-container">
                    <div class="table-responsive">
                        <table class="admin-table pixel-perfect-table" id="adminUsersTable">
                            <thead>
                                <tr>
                                    <th class="d-none d-md-table-cell" style="width: 60px;">ID</th>
                                    <th style="width: 25%;">User</th>
                                    <th class="d-none d-lg-table-cell" style="width: 25%;">Email</th>
                                    <th style="width: 15%;">Role</th>
                                    <th class="d-none d-lg-table-cell" style="width: 15%;">Reports To</th>
                                    <th class="d-none d-md-table-cell" style="width: 15%;">Created</th>
                                    <th style="width: 40px;"><i class="fas fa-ellipsis-h"></i></th>
                                </tr>
                            </thead>
                        <tbody>
                            <?php if ($adminResult && $adminResult->num_rows > 0): ?>
                                <?php while ($admin = $adminResult->fetch_assoc()): ?>
                                    <tr>
                                        <td class="d-none d-md-table-cell"><?php echo $admin['id']; ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle pixel-avatar me-3">
                                                    <?php echo strtoupper(substr($admin['username'], 0, 1)); ?>
                                                </div>
                                                <div class="user-info">
                                                    <div class="fw-bold pixel-username"><?php echo htmlspecialchars($admin['username']); ?></div>
                                                    <div class="small text-muted d-md-none"> <?php echo htmlspecialchars($admin['email']); ?> </div>
                                                    <?php if (!empty($admin['name'])): ?>
                                                    <div class="small text-muted d-none d-md-block"> <?php echo htmlspecialchars($admin['name']); ?> </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="d-none d-lg-table-cell"> <?php echo htmlspecialchars($admin['email']); ?> </td>
                                        <td>
                                            <?php
                                            $roleIcon = 'fa-user';
                                            switch ($admin['role']) {
                                                case 'super_admin': $roleIcon = 'fa-user-shield'; break;
                                                case 'admin': $roleIcon = 'fa-user-cog'; break;
                                                case 'manager': $roleIcon = 'fa-user-tie'; break;
                                                case 'staff': $roleIcon = 'fa-user-check'; break;
                                                case 'viewer': $roleIcon = 'fa-user'; break;
                                            }
                                            ?>
                                            <span class="role-badge pixel-role-badge role-badge-filled">
                                                <i class="fas <?php echo $roleIcon; ?>"></i>
                                                <?php echo ucfirst(str_replace('_', ' ', $admin['role'])); ?>
                                            </span>
                                        </td>
                                        <td class="d-none d-lg-table-cell">
                                            <?php if (!empty($admin['parent_name'])): ?>
                                                <span class="text-nowrap">
                                                    <i class="fas fa-user-friends me-1 text-muted"></i>
                                                    <?php echo htmlspecialchars($admin['parent_name']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="d-none d-md-table-cell">
                                            <span class="text-nowrap">
                                                <i class="far fa-calendar-alt me-1 text-muted"></i>
                                                <?php echo Utilities::formatDate($admin['created_at']); ?>
                                            </span>
                                        </td>
                                        <td class="actions-cell" style="position: static !important;">
                                            <?php if ((int)$admin['id'] !== (int)$_SESSION['user_id']): ?>
                                                <div class="action-menu-container">
                                                    <button class="action-btn" type="button" aria-label="Actions" title="User Actions">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="action-menu" style="min-width: 200px; max-width: 260px;">
                                                        <div class="action-menu-header">User Options</div>
                                                        <a class="action-menu-item" href="admin_view.php?id=<?php echo $admin['id']; ?>">
                                                            <i class="fas fa-eye"></i> View Profile
                                                        </a>
                                                        <?php if ($admin['role'] === 'staff'): ?>
                                                        <a class="action-menu-item" href="admin_edit.php?id=<?php echo $admin['id']; ?>">
                                                            <i class="fas fa-edit"></i> Edit User
                                                        </a>
                                                        <?php endif; ?>
                                                        <a class="action-menu-item" href="index.php?staff_id=<?php echo $admin['id']; ?>">
                                                            <i class="fas fa-chart-line"></i> View Stats
                                                        </a>
                                                        <div class="action-menu-divider"></div>
                                                        <div class="action-menu-header">Danger Zone</div>
                                                        <form method="post" onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.');">
                                                            <input type="hidden" name="action" value="delete_admin">
                                                            <input type="hidden" name="admin_id" value="<?php echo $admin['id']; ?>">
                                                            <button type="submit" class="action-menu-item danger">
                                                                <i class="fas fa-trash-alt"></i> Delete User
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-user-check"></i>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="py-5">
                                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                            <p class="mb-0">No admin users found</p>
                                            <button type="button" class="btn btn-sm btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                                                <i class="fas fa-plus me-2"></i> Add First Admin
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
                    </div>
                </div>
                <?php if ($adminResult && $adminResult->num_rows > 0): ?>
                <div class="settings-section" style="border-top: 1px solid var(--border-color);">
                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                        <div class="small text-muted mb-2 mb-md-0">
                            Showing <?php echo $adminResult->num_rows; ?> users
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="btn-group me-2 d-none d-md-flex">
                                <button type="button" class="btn btn-settings btn-settings-outline" disabled>
                                    <i class="fas fa-file-export me-1"></i> Export
                                </button>
                                <button type="button" class="btn btn-settings btn-settings-outline" disabled>
                                    <i class="fas fa-print me-1"></i> Print
                                </button>
                            </div>
                            <button type="button" class="btn btn-settings btn-settings-primary" id="refreshAdminTable">
                                <i class="fas fa-sync-alt me-1"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php elseif ($activeTab === 'platform'): ?>
            <!-- Platform Settings Section -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">
                        <i class="fas fa-sliders-h settings-section-icon"></i>
                        Platform Settings
                    </h2>
                </div>
                <p class="text-muted">Configure global platform settings and defaults.</p>

                <?php
                // Handle platform settings form submission
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['platform_settings_submit'])) {
                    $platform_name = Utilities::sanitizeInput($_POST['platform_name'] ?? '');
                    $default_language = Utilities::sanitizeInput($_POST['default_language'] ?? 'en');
                    $timezone = Utilities::sanitizeInput($_POST['timezone'] ?? 'UTC');
                    $default_user_role = Utilities::sanitizeInput($_POST['default_user_role'] ?? 'viewer');
                    $support_email = Utilities::sanitizeInput($_POST['support_email'] ?? '');

                    $settingsManager->set('platform_name', $platform_name);
                    $settingsManager->set('default_language', $default_language);
                    $settingsManager->set('timezone', $timezone);
                    $settingsManager->set('default_user_role', $default_user_role);
                    $settingsManager->set('support_email', $support_email);

                    Utilities::setFlashMessage('success', 'Platform settings updated successfully.');
                    Utilities::redirect('settings.php?tab=platform');
                }

                // Get current values
                $platform_name = $settingsManager->get('platform_name', 'KFT Fitness');
                $default_language = $settingsManager->get('default_language', 'en');
                $timezone = $settingsManager->get('timezone', 'UTC');
                $default_user_role = $settingsManager->get('default_user_role', 'viewer');
                $support_email = $settingsManager->get('support_email', '<EMAIL>');
                ?>
                <?php Utilities::displayFlashMessages(); ?>
                <form method="post" class="row g-4 mt-2">
                    <div class="col-md-6">
                        <label for="platform_name" class="form-label">Platform Name</label>
                        <input type="text" class="form-control" id="platform_name" name="platform_name" value="<?php echo htmlspecialchars($platform_name); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="support_email" class="form-label">Support Email</label>
                        <input type="email" class="form-control" id="support_email" name="support_email" value="<?php echo htmlspecialchars($support_email); ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="default_language" class="form-label">Default Language</label>
                        <select class="form-select" id="default_language" name="default_language">
                            <option value="en" <?php if ($default_language === 'en') echo 'selected'; ?>>English</option>
                            <option value="es" <?php if ($default_language === 'es') echo 'selected'; ?>>Spanish</option>
                            <option value="fr" <?php if ($default_language === 'fr') echo 'selected'; ?>>French</option>
                            <option value="de" <?php if ($default_language === 'de') echo 'selected'; ?>>German</option>
                            <option value="ar" <?php if ($default_language === 'ar') echo 'selected'; ?>>Arabic</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="timezone" class="form-label">Timezone</label>
                        <select class="form-select" id="timezone" name="timezone">
                            <?php foreach (DateTimeZone::listIdentifiers() as $tz): ?>
                                <option value="<?php echo $tz; ?>" <?php if ($timezone === $tz) echo 'selected'; ?>><?php echo $tz; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="default_user_role" class="form-label">Default User Role</label>
                        <select class="form-select" id="default_user_role" name="default_user_role">
                            <option value="viewer" <?php if ($default_user_role === 'viewer') echo 'selected'; ?>>Viewer</option>
                            <option value="staff" <?php if ($default_user_role === 'staff') echo 'selected'; ?>>Staff</option>
                            <option value="manager" <?php if ($default_user_role === 'manager') echo 'selected'; ?>>Manager</option>
                        </select>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" name="platform_settings_submit" class="btn btn-dark">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </form>
            </div>
            <?php elseif ($activeTab === 'security'): ?>
            <!-- Security Settings Section -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">
                        <i class="fas fa-shield-alt settings-section-icon"></i>
                        Security Settings
                    </h2>
                </div>
                <p class="text-muted">Configure security settings and access controls.</p>

                <?php
                // Handle security settings form submission
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['security_settings_submit'])) {
                    $password_min_length = max(6, (int)($_POST['password_min_length'] ?? 8));
                    $require_special = isset($_POST['require_special']) ? 1 : 0;
                    $require_number = isset($_POST['require_number']) ? 1 : 0;
                    $session_timeout = max(5, (int)($_POST['session_timeout'] ?? 30));
                    $allow_password_reset = isset($_POST['allow_password_reset']) ? 1 : 0;
                    $lockout_attempts = max(3, (int)($_POST['lockout_attempts'] ?? 5));
                    $enable_2fa = isset($_POST['enable_2fa']) ? 1 : 0;

                    $settingsManager->set('password_min_length', $password_min_length);
                    $settingsManager->set('require_special', $require_special);
                    $settingsManager->set('require_number', $require_number);
                    $settingsManager->set('session_timeout', $session_timeout);
                    $settingsManager->set('allow_password_reset', $allow_password_reset);
                    $settingsManager->set('lockout_attempts', $lockout_attempts);
                    $settingsManager->set('enable_2fa', $enable_2fa);

                    Utilities::setFlashMessage('success', 'Security settings updated successfully.');
                    Utilities::redirect('settings.php?tab=security');
                }

                // Get current values
                $password_min_length = $settingsManager->get('password_min_length', 8);
                $require_special = $settingsManager->get('require_special', 1);
                $require_number = $settingsManager->get('require_number', 1);
                $session_timeout = $settingsManager->get('session_timeout', 30);
                $allow_password_reset = $settingsManager->get('allow_password_reset', 1);
                $lockout_attempts = $settingsManager->get('lockout_attempts', 5);
                $enable_2fa = $settingsManager->get('enable_2fa', 0);
                ?>
                <?php Utilities::displayFlashMessages(); ?>
                <form method="post" class="row g-4 mt-2">
                    <div class="col-md-4">
                        <label for="password_min_length" class="form-label">Password Minimum Length</label>
                        <input type="number" class="form-control" id="password_min_length" name="password_min_length" min="6" max="64" value="<?php echo htmlspecialchars($password_min_length); ?>" required>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="require_special" name="require_special" value="1" <?php if ($require_special) echo 'checked'; ?>>
                            <label class="form-check-label" for="require_special">Require Special Character</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="require_number" name="require_number" value="1" <?php if ($require_number) echo 'checked'; ?>>
                            <label class="form-check-label" for="require_number">Require Number</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                        <input type="number" class="form-control" id="session_timeout" name="session_timeout" min="5" max="240" value="<?php echo htmlspecialchars($session_timeout); ?>" required>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="allow_password_reset" name="allow_password_reset" value="1" <?php if ($allow_password_reset) echo 'checked'; ?>>
                            <label class="form-check-label" for="allow_password_reset">Allow Password Reset via Email</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="lockout_attempts" class="form-label">Account Lockout After Failed Logins</label>
                        <input type="number" class="form-control" id="lockout_attempts" name="lockout_attempts" min="3" max="20" value="<?php echo htmlspecialchars($lockout_attempts); ?>" required>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="enable_2fa" name="enable_2fa" value="1" <?php if ($enable_2fa) echo 'checked'; ?>>
                            <label class="form-check-label" for="enable_2fa">Enable Two-Factor Authentication (2FA)</label>
                        </div>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" name="security_settings_submit" class="btn btn-dark">
                            <i class="fas fa-save me-2"></i>Save Security Settings
                        </button>
                    </div>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Admin User Modal -->
<div class="modal fade" id="addAdminModal" tabindex="-1" aria-labelledby="addAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addAdminModalLabel">
                    <i class="fas fa-user-plus me-2"></i>Add Admin User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body p-4">
                    <input type="hidden" name="action" value="add_admin">

                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-1">Adding a New Admin User</h6>
                                <p class="mb-0 small">Admin users have access to the dashboard and can manage platform content based on their role.</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user text-primary me-2"></i>Username
                        </label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" required>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope text-primary me-2"></i>Email
                        </label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter email address" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock text-primary me-2"></i>Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Password must be at least 8 characters long.</div>
                    </div>

                    <div class="mb-3">
                        <label for="role" class="form-label">
                            <i class="fas fa-user-tag text-primary me-2"></i>Role
                        </label>
                        <select class="form-select" id="role" name="role">
                            <option value="super_admin">Super Admin</option>
                            <option value="manager">Manager</option>
                            <option value="editor" selected>Editor</option>
                            <option value="viewer">Viewer</option>
                        </select>
                        <div class="form-text">Select the appropriate role for this user.</div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-settings btn-settings-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-settings btn-settings-primary">
                        <i class="fas fa-user-plus me-2"></i>Add User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle password visibility and initialize dropdowns
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');

    if (togglePassword && password) {
        togglePassword.addEventListener('click', function() {
            const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
            password.setAttribute('type', type);

            // Toggle eye icon
            this.querySelector('i').classList.toggle('fa-eye');
            this.querySelector('i').classList.toggle('fa-eye-slash');
        });
    }

    // Initialize all dropdowns with Bootstrap
    if (typeof bootstrap !== 'undefined') {
        // Enable all dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        dropdownElementList.forEach(function(dropdownToggleEl) {
            new bootstrap.Dropdown(dropdownToggleEl, {
                autoClose: 'outside',
                popperConfig: {
                    strategy: 'fixed',
                    modifiers: [
                        {
                            name: 'preventOverflow',
                            options: {
                                boundary: document.querySelector('.admin-table-container'),
                                padding: 8
                            }
                        }
                    ]
                }
            });
        });

        // Add click handler to prevent immediate closing
        document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
            menu.addEventListener('click', function(e) {
                // Only prevent default for clicks on dropdown items that aren't links
                if (e.target.classList.contains('dropdown-item') && !e.target.href) {
                    e.stopPropagation();
                }
            });
        });

        // Fix dropdown positioning on window resize
        window.addEventListener('resize', function() {
            document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                menu.classList.remove('show');
            });
        });
    }
});
</script>

<script src="assets/js/admin-table.js"></script>
<script src="assets/js/modern-action-menu.js"></script>
<style>
.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>
<?php require_once 'includes/footer.php'; ?>
