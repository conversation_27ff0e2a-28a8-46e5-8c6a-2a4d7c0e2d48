<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Private Vimeo Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-test {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            margin: 15px 0;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 4px;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-info p {
            margin: 5px 0;
            color: #666;
        }
        .url-display {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Private Vimeo Video Test</h1>
        <p>Testing private Vimeo video with hash parameter: <strong>1087487482/ae75b6e329</strong></p>

        <div class="highlight">
            <h4>🔍 Key Discovery</h4>
            <p>The video URL contains a private hash: <code>ae75b6e329</code></p>
            <p>This means the video is <strong>private</strong> and requires the hash parameter to play.</p>
        </div>

        <!-- Test 1: Without Hash (Should Fail) -->
        <div class="video-test">
            <div class="test-info">
                <h3>❌ Test 1: Without Private Hash (Expected to Fail)</h3>
                <p>Standard embed without the private hash parameter</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status1">Loading...</div>
        </div>

        <!-- Test 2: With Hash (Should Work) -->
        <div class="video-test">
            <div class="test-info">
                <h3>✅ Test 2: With Private Hash (Expected to Work)</h3>
                <p>Embed with the private hash parameter extracted from the URL</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&h=ae75b6e329</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&h=ae75b6e329" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status2">Loading...</div>
        </div>

        <!-- Test 3: Alternative Hash Format -->
        <div class="video-test">
            <div class="test-info">
                <h3>🔄 Test 3: Alternative Hash Format</h3>
                <p>Testing with hash as first parameter</p>
                <div class="url-display">https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1</div>
            </div>
            <div class="video-container">
                <iframe src="https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen></iframe>
            </div>
            <div class="status" id="status3">Loading...</div>
        </div>

        <!-- Instructions -->
        <div class="video-test">
            <div class="test-info">
                <h3>🔧 Next Steps</h3>
                <p><strong>If Test 2 or 3 works:</strong></p>
                <ol>
                    <li>✅ The hash extraction logic is correct</li>
                    <li>🔧 Update Flutter app to use the working URL format</li>
                    <li>🧪 Test the Flutter app again</li>
                </ol>
                <p><strong>If none work:</strong></p>
                <ol>
                    <li>🔒 Check Vimeo video privacy settings</li>
                    <li>🌐 Verify domain whitelist includes ngrok URL</li>
                    <li>📧 Contact video owner for access permissions</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Monitor iframe load events
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('iframe');
            
            iframes.forEach((iframe, index) => {
                const statusElement = document.getElementById(`status${index + 1}`);
                
                iframe.addEventListener('load', function() {
                    statusElement.textContent = '✅ Iframe loaded successfully';
                    statusElement.className = 'status success';
                });
                
                iframe.addEventListener('error', function() {
                    statusElement.textContent = '❌ Failed to load iframe';
                    statusElement.className = 'status error';
                });
                
                // Set timeout to check if video loads
                setTimeout(() => {
                    if (statusElement.textContent === 'Loading...') {
                        statusElement.textContent = '⚠️ Taking longer than expected to load';
                        statusElement.className = 'status warning';
                    }
                }, 5000);
            });
        });

        // Listen for messages from Vimeo player
        window.addEventListener('message', function(event) {
            if (event.origin !== 'https://player.vimeo.com') return;
            
            console.log('Vimeo player message:', event.data);
            
            // Handle different Vimeo player events
            if (event.data && event.data.event) {
                switch (event.data.event) {
                    case 'ready':
                        console.log('✅ Vimeo player ready');
                        break;
                    case 'error':
                        console.error('❌ Vimeo player error:', event.data);
                        break;
                    case 'loaded':
                        console.log('✅ Vimeo video loaded');
                        break;
                }
            }
        });
    </script>
</body>
</html>
