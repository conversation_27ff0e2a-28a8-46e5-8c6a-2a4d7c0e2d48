# PWA Icon Generation Instructions

## Required Icons

The following PNG icons need to be generated from the `assets/img/pwa-icon-base.svg` file:

- `pwa-icon-72x72.png` (72x72 pixels)
- `pwa-icon-96x96.png` (96x96 pixels)
- `pwa-icon-128x128.png` (128x128 pixels)
- `pwa-icon-144x144.png` (144x144 pixels)
- `pwa-icon-152x152.png` (152x152 pixels)
- `pwa-icon-192x192.png` (192x192 pixels)
- `pwa-icon-384x384.png` (384x384 pixels)
- `pwa-icon-512x512.png` (512x512 pixels)

## Generation Methods

### Method 1: Using Online Tools
1. Go to https://realfavicongenerator.net/ or https://favicon.io/
2. Upload the `pwa-icon-base.svg` file
3. Generate all required sizes
4. Download and place in `assets/img/` directory

### Method 2: Using ImageMagick (Command Line)
```bash
# Install ImageMagick if not already installed
# macOS: brew install imagemagick
# Ubuntu: sudo apt-get install imagemagick

# Convert SVG to different PNG sizes
convert assets/img/pwa-icon-base.svg -resize 72x72 assets/img/pwa-icon-72x72.png
convert assets/img/pwa-icon-base.svg -resize 96x96 assets/img/pwa-icon-96x96.png
convert assets/img/pwa-icon-base.svg -resize 128x128 assets/img/pwa-icon-128x128.png
convert assets/img/pwa-icon-base.svg -resize 144x144 assets/img/pwa-icon-144x144.png
convert assets/img/pwa-icon-base.svg -resize 152x152 assets/img/pwa-icon-152x152.png
convert assets/img/pwa-icon-base.svg -resize 192x192 assets/img/pwa-icon-192x192.png
convert assets/img/pwa-icon-base.svg -resize 384x384 assets/img/pwa-icon-384x384.png
convert assets/img/pwa-icon-base.svg -resize 512x512 assets/img/pwa-icon-512x512.png
```

### Method 3: Using Node.js Script
```javascript
const sharp = require('sharp');
const fs = require('fs');

const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
const svgBuffer = fs.readFileSync('assets/img/pwa-icon-base.svg');

sizes.forEach(size => {
  sharp(svgBuffer)
    .resize(size, size)
    .png()
    .toFile(`assets/img/pwa-icon-${size}x${size}.png`)
    .then(() => console.log(`Generated ${size}x${size} icon`))
    .catch(err => console.error(`Error generating ${size}x${size}:`, err));
});
```

## Temporary Placeholder Creation

For immediate testing, you can create simple placeholder icons using any image editor or online tool. The icons should:
- Have a green background (#27ae60)
- Include the KFT logo or text
- Be square (1:1 aspect ratio)
- Be saved as PNG format

## Verification

After generating the icons, verify they exist:
- [ ] assets/img/pwa-icon-72x72.png
- [ ] assets/img/pwa-icon-96x96.png
- [ ] assets/img/pwa-icon-128x128.png
- [ ] assets/img/pwa-icon-144x144.png
- [ ] assets/img/pwa-icon-152x152.png
- [ ] assets/img/pwa-icon-192x192.png
- [ ] assets/img/pwa-icon-384x384.png
- [ ] assets/img/pwa-icon-512x512.png
