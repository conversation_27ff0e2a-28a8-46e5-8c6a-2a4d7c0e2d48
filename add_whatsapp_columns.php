<?php
// Simple script to add WhatsApp columns to the courses table
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if whatsapp_number column exists
$checkWhatsappNumberColumn = $conn->query("SHOW COLUMNS FROM courses LIKE 'whatsapp_number'");
if ($checkWhatsappNumberColumn->num_rows === 0) {
    // Add whatsapp_number column
    $addWhatsappNumberQuery = "ALTER TABLE courses ADD COLUMN whatsapp_number VARCHAR(20) DEFAULT NULL";
    if ($conn->query($addWhatsappNumberQuery)) {
        echo "Column 'whatsapp_number' added successfully.<br>";
    } else {
        echo "Error adding column 'whatsapp_number': " . $conn->error . "<br>";
    }
} else {
    echo "Column 'whatsapp_number' already exists.<br>";
}

// Check if whatsapp_message_prefix column exists
$checkWhatsappMessagePrefixColumn = $conn->query("SHOW COLUMNS FROM courses LIKE 'whatsapp_message_prefix'");
if ($checkWhatsappMessagePrefixColumn->num_rows === 0) {
    // Add whatsapp_message_prefix column
    $addWhatsappMessagePrefixQuery = "ALTER TABLE courses ADD COLUMN whatsapp_message_prefix TEXT DEFAULT NULL";
    if ($conn->query($addWhatsappMessagePrefixQuery)) {
        echo "Column 'whatsapp_message_prefix' added successfully.<br>";
    } else {
        echo "Error adding column 'whatsapp_message_prefix': " . $conn->error . "<br>";
    }
} else {
    echo "Column 'whatsapp_message_prefix' already exists.<br>";
}

echo "Script completed.<br>";
?>
