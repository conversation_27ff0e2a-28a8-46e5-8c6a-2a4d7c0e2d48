<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if reminder ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('danger', 'Invalid reminder ID.');
    Utilities::redirect('water_reminders.php');
}

$reminderId = (int)$_GET['id'];

// Check if phone column exists in users table
$checkPhoneColumnQuery = "SHOW COLUMNS FROM users LIKE 'phone'";
$checkPhoneColumnResult = $conn->query($checkPhoneColumnQuery);
$phoneColumnExists = $checkPhoneColumnResult->num_rows > 0;

// Add phone column if it doesn't exist
if (!$phoneColumnExists) {
    $alterTableQuery = "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL";
    $conn->query($alterTableQuery);
}

// Get reminder details
$reminderQuery = "SELECT wr.*, u.name as user_name, u.username" .
                 ($phoneColumnExists ? ", u.phone" : ", '' as phone") .
                 " FROM water_reminders wr
                 JOIN users u ON wr.user_id = u.id
                 WHERE wr.id = ?";
$stmt = $conn->prepare($reminderQuery);
$stmt->bind_param("i", $reminderId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('danger', 'Water reminder not found.');
    Utilities::redirect('water_reminders.php');
}

$reminder = $result->fetch_assoc();

// Process verification
if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'verify') {
        // Simulate sending a test notification
        $phone = $reminder['phone'];
        $message = "This is a test water reminder notification from KFT Fitness App. Please drink water now!";

        // In a real application, you would integrate with a messaging service here
        // For now, we'll just simulate success
        $success = true;

        if ($success) {
            // Update verification status
            $updateQuery = "UPDATE water_reminders SET is_verified = 1, verified_at = NOW() WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("i", $reminderId);

            if ($updateStmt->execute()) {
                Utilities::setFlashMessage('success', 'Water reminder has been verified successfully. A test notification was sent to the user.', true);
            } else {
                Utilities::setFlashMessage('danger', 'Failed to update verification status: ' . $conn->error, true);
            }
        } else {
            Utilities::setFlashMessage('danger', 'Failed to send test notification. Please check the user\'s phone number.');
        }
    } elseif ($action === 'unverify') {
        // Update verification status
        $updateQuery = "UPDATE water_reminders SET is_verified = 0, verified_at = NULL WHERE id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("i", $reminderId);

        if ($updateStmt->execute()) {
            Utilities::setFlashMessage('warning', 'Water reminder verification has been reset.', true);
        } else {
            Utilities::setFlashMessage('danger', 'Failed to update verification status: ' . $conn->error, true);
        }
    }

    // Redirect to refresh the page with updated data
    Utilities::redirect("water_reminder_verify.php?id=$reminderId");
}

// Check if we need to add the is_verified column to the water_reminders table
$checkColumnQuery = "SHOW COLUMNS FROM water_reminders LIKE 'is_verified'";
$checkColumnResult = $conn->query($checkColumnQuery);

if ($checkColumnResult->num_rows === 0) {
    // Add the is_verified and verified_at columns
    $alterTableQuery = "ALTER TABLE water_reminders
                       ADD COLUMN is_verified TINYINT(1) NOT NULL DEFAULT 0,
                       ADD COLUMN verified_at DATETIME NULL";
    $conn->query($alterTableQuery);

    // Refresh the reminder data
    $stmt->execute();
    $result = $stmt->get_result();
    $reminder = $result->fetch_assoc();
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Verify Water Reminder</h1>
    <a href="water_reminders.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Water Reminders
    </a>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="alert alert-info alert-persistent">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Verification Status:</strong>
            <?php if (isset($reminder['is_verified']) && $reminder['is_verified']): ?>
                <span class="badge bg-success">Verified</span>
                <?php if (isset($reminder['verified_at']) && $reminder['verified_at']): ?>
                    <span class="ms-2 text-muted">on <?php echo date('M d, Y h:i A', strtotime($reminder['verified_at'])); ?></span>
                <?php endif; ?>
            <?php else: ?>
                <span class="badge bg-warning">Not Verified</span>
            <?php endif; ?>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="mb-3">User Information</h5>
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Name</th>
                        <td><?php echo htmlspecialchars($reminder['user_name']); ?></td>
                    </tr>
                    <tr>
                        <th>Username</th>
                        <td><?php echo htmlspecialchars($reminder['username']); ?></td>
                    </tr>
                    <tr>
                        <th>Phone</th>
                        <td><?php echo htmlspecialchars($reminder['phone'] ?? 'Not set'); ?></td>
                    </tr>
                </table>
            </div>

            <div class="col-md-6">
                <h5 class="mb-3">Reminder Settings</h5>
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Interval</th>
                        <td>Every <?php echo $reminder['interval_hours']; ?> hour<?php echo $reminder['interval_hours'] > 1 ? 's' : ''; ?></td>
                    </tr>
                    <tr>
                        <th>Start Time</th>
                        <td><?php echo date('h:i A', strtotime($reminder['start_time'])); ?></td>
                    </tr>
                    <tr>
                        <th>End Time</th>
                        <td><?php echo date('h:i A', strtotime($reminder['end_time'])); ?></td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <?php if ($reminder['is_active']): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card bg-light mb-4">
            <div class="card-body">
                <h5 class="mb-3">Verification Actions</h5>
                <p>Verifying a water reminder will send a test notification to the user's phone to ensure that reminders are working correctly.</p>

                <form method="post" class="d-inline">
                    <?php if (isset($reminder['is_verified']) && $reminder['is_verified']): ?>
                        <input type="hidden" name="action" value="unverify">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-times-circle me-2"></i> Reset Verification
                        </button>
                    <?php else: ?>
                        <input type="hidden" name="action" value="verify">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check-circle me-2"></i> Verify Reminder
                        </button>
                    <?php endif; ?>
                </form>

                <a href="water_reminder_edit.php?id=<?php echo $reminderId; ?>" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-edit me-2"></i> Edit Reminder
                </a>
            </div>
        </div>

        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Note:</strong> Verification ensures that the user will receive water reminders at the specified intervals.
            If the user is not receiving reminders, please check their phone number and notification settings.
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
