<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if motivational_quotes table exists
$tables = [
    'motivational_quotes',
    'user_quote_preferences',
    'quote_settings'
];

echo "<h1>Database Table Check</h1>";

foreach ($tables as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($query);
    
    if ($result->num_rows > 0) {
        echo "<p style='color:green'>✓ Table '$table' exists</p>";
        
        // Check row count
        $countQuery = "SELECT COUNT(*) as count FROM $table";
        $countResult = $conn->query($countQuery);
        $count = $countResult->fetch_assoc()['count'];
        echo "<p style='margin-left:20px'>- Contains $count rows</p>";
        
        // Show table structure
        echo "<p style='margin-left:20px'>- Table structure:</p>";
        $structureQuery = "DESCRIBE $table";
        $structureResult = $conn->query($structureQuery);
        
        echo "<pre style='margin-left:40px'>";
        while ($row = $structureResult->fetch_assoc()) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p style='color:red'>✗ Table '$table' does not exist</p>";
    }
}

// Check if we need to run the setup script
echo "<h2>Setup Script</h2>";
if ($result->num_rows === 0) {
    echo "<p>Tables are missing. You need to run the setup script.</p>";
    echo "<p><a href='create_motivational_quotes_tables.php' style='background-color:#4CAF50;color:white;padding:10px 15px;text-decoration:none;border-radius:4px;'>Run Setup Script</a></p>";
} else {
    echo "<p>All required tables exist.</p>";
}
