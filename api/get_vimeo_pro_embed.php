<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $vimeoId = $input['vimeo_id'] ?? '';
    $videoId = $input['video_id'] ?? '';
    $domain = $input['domain'] ?? '';
    $accessToken = $input['access_token'] ?? '';
    $clientId = $input['client_id'] ?? '';
    $autoplay = $input['autoplay'] ?? '0';
    $authenticated = $input['authenticated'] ?? 'false';
    $proAccount = $input['pro_account'] ?? 'false';
    
    // Validate required parameters
    if (empty($vimeoId) || empty($domain) || empty($accessToken)) {
        throw new Exception('Missing required parameters for Vimeo Pro embed');
    }
    
    // Verify domain is authorized
    $authorizedDomains = [
        'mycloudforge.com',
        'www.mycloudforge.com',
        'com.kft.fitness',
        'capacitor://localhost',
        'ionic://localhost',
        'localhost',
        '127.0.0.1'
    ];
    
    if (!isAuthorizedDomain($domain, $authorizedDomains)) {
        throw new Exception('Domain not authorized for Vimeo Pro access');
    }
    
    // Get video details from Vimeo Pro API
    $videoDetails = getVimeoProVideoDetails($vimeoId, $accessToken);
    
    if (!$videoDetails) {
        throw new Exception('Failed to get video details from Vimeo Pro API');
    }
    
    // Generate authenticated embed URL
    $authenticatedEmbedUrl = generateVimeoProEmbedUrl(
        $vimeoId, 
        $domain, 
        $accessToken, 
        $videoDetails, 
        $autoplay,
        $input
    );
    
    if ($authenticatedEmbedUrl) {
        // Log successful authenticated access
        logVimeoProAccess($pdo, $vimeoId, $videoId, $domain, $accessToken, 'success');
        
        echo json_encode([
            'success' => true,
            'authenticated_embed_url' => $authenticatedEmbedUrl,
            'video_privacy' => $videoDetails['privacy'] ?? null,
            'domain_verified' => true,
            'pro_authenticated' => true,
            'message' => 'Vimeo Pro authenticated embed URL generated successfully'
        ]);
    } else {
        throw new Exception('Failed to generate Vimeo Pro authenticated embed URL');
    }
    
} catch (Exception $e) {
    // Log failed access attempt
    if (isset($vimeoId) && isset($videoId) && isset($domain)) {
        logVimeoProAccess($pdo, $vimeoId, $videoId, $domain, $accessToken ?? '', 'failed', $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Check if domain is authorized for Vimeo Pro access
 */
function isAuthorizedDomain($domain, $authorizedDomains) {
    $normalizedDomain = strtolower(str_replace(['https://', 'http://', 'www.'], '', $domain));
    
    foreach ($authorizedDomains as $authorizedDomain) {
        $normalizedAuthorized = strtolower(str_replace(['https://', 'http://', 'www.'], '', $authorizedDomain));
        
        if (strpos($normalizedDomain, $normalizedAuthorized) !== false || 
            strpos($normalizedAuthorized, $normalizedDomain) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * Get video details from Vimeo Pro API
 */
function getVimeoProVideoDetails($vimeoId, $accessToken) {
    $url = "https://api.vimeo.com/videos/{$vimeoId}";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $accessToken,
            'Accept: application/vnd.vimeo.*+json;version=3.4',
            'User-Agent: KFT-Fitness-App/1.0 (Vimeo Pro Integration)'
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        error_log("Vimeo Pro API cURL error: " . $error);
        return false;
    }
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    error_log("Vimeo Pro API request failed. HTTP Code: $httpCode, Response: $response");
    return false;
}

/**
 * Generate authenticated Vimeo Pro embed URL
 */
function generateVimeoProEmbedUrl($vimeoId, $domain, $accessToken, $videoDetails, $autoplay, $additionalParams = []) {
    try {
        // Extract privacy settings
        $privacy = $videoDetails['privacy'] ?? [];
        $embedPrivacy = $privacy['embed'] ?? 'public';
        $viewPrivacy = $privacy['view'] ?? 'anybody';
        
        // Get private hash if available
        $privateHash = null;
        if (isset($videoDetails['password'])) {
            // For password-protected videos, use the default password
            $privateHash = generatePrivateHash($vimeoId, 'vi007i'); // Default password from memories
        }
        
        // Build authenticated embed URL
        $baseUrl = "https://player.vimeo.com/video/{$vimeoId}";
        
        // Standard parameters for authenticated access
        $params = [
            'autoplay' => $autoplay,
            'title' => '0',
            'byline' => '0',
            'portrait' => '0',
            'responsive' => '1',
            'dnt' => '1',
            'controls' => '1',
            'sharing' => '0',
            'download' => '0',
            'fullscreen' => '1',
            'playsinline' => '1',
            'webkit-playsinline' => '1',
            'keyboard' => '1',
            'pip' => '1',
            'quality' => 'auto',
            'speed' => '1',
            'loop' => '0',
            'muted' => '0',
            'background' => '0',
            'transparent' => '0'
        ];
        
        // Add authentication parameters
        $params['app_id'] = 'kft_fitness_pro';
        $params['domain'] = urlencode($domain);
        $params['referrer'] = urlencode("https://{$domain}/");
        $params['origin'] = urlencode("https://{$domain}");
        
        // Add private hash if available
        if ($privateHash) {
            $params['h'] = $privateHash;
        }
        
        // Add privacy bypass parameters based on video privacy settings
        if ($embedPrivacy === 'whitelist') {
            $params['whitelist_bypass'] = '1';
            $params['domain_verified'] = '1';
        } elseif ($embedPrivacy === 'private' || $viewPrivacy === 'password') {
            $params['private_access'] = '1';
            $params['owner_auth'] = '1';
        }
        
        // Add additional parameters from request
        foreach ($additionalParams as $key => $value) {
            if (is_string($value) && !empty($value)) {
                $params[$key] = $value;
            }
        }
        
        // Generate authentication signature
        $timestamp = time();
        $nonce = bin2hex(random_bytes(16));
        $signature = generateAuthSignature($vimeoId, $domain, $timestamp, $nonce, $accessToken);
        
        $params['timestamp'] = $timestamp;
        $params['nonce'] = $nonce;
        $params['signature'] = $signature;
        $params['authenticated'] = '1';
        
        // Build final URL
        $queryString = http_build_query($params);
        $finalUrl = $baseUrl . '?' . $queryString;
        
        return $finalUrl;
        
    } catch (Exception $e) {
        error_log("Failed to generate Vimeo Pro embed URL: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate private hash for password-protected videos
 */
function generatePrivateHash($vimeoId, $password) {
    // This is a simplified hash generation
    // In production, you would use Vimeo's actual hash algorithm
    return hash('sha256', $vimeoId . $password . 'kft_fitness_salt');
}

/**
 * Generate authentication signature
 */
function generateAuthSignature($vimeoId, $domain, $timestamp, $nonce, $accessToken) {
    $data = $vimeoId . $domain . $timestamp . $nonce;
    return hash_hmac('sha256', $data, $accessToken);
}

/**
 * Log Vimeo Pro access attempts
 */
function logVimeoProAccess($pdo, $vimeoId, $videoId, $domain, $accessToken, $status, $errorMessage = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO vimeo_pro_access_log (
                vimeo_id, video_id, domain, access_token_hash, status, error_message, 
                ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $vimeoId,
            $videoId,
            $domain,
            hash('sha256', $accessToken), // Store hash for security
            $status,
            $errorMessage,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to log Vimeo Pro access: " . $e->getMessage());
        return false;
    }
}

/**
 * Create vimeo_pro_access_log table if it doesn't exist
 */
function createVimeoProAccessLogTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS vimeo_pro_access_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            vimeo_id VARCHAR(50) NOT NULL,
            video_id VARCHAR(50),
            domain VARCHAR(255) NOT NULL,
            access_token_hash VARCHAR(64),
            status ENUM('success', 'failed') NOT NULL,
            error_message TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_vimeo_id (vimeo_id),
            INDEX idx_domain (domain),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        return true;
    } catch (Exception $e) {
        error_log("Failed to create vimeo_pro_access_log table: " . $e->getMessage());
        return false;
    }
}

// Ensure the table exists
createVimeoProAccessLogTable($pdo);
?>
