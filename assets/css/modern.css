/*
 * KFT Fitness Admin Dashboard - Modern Theme
 * A clean, professional, and pixel-perfect design
 */

:root {
    /* Health-focused color palette */
    --primary: #27ae60;        /* Green accent */
    --primary-light: #6ee7b7;  /* Light green */
    --primary-dark: #219150;   /* Dark green */
    --secondary: #111;         /* Black for secondary */
    --success: #27ae60;        /* Green */
    --info: #27ae60;           /* Use green for info */
    --warning: #f4d35e;        /* Subtle yellow for warning */
    --danger: #e74c3c;         /* Red for error */
    --accent: #27ae60;         /* Green accent */
    --light: #fff;             /* White */
    --dark: #111;              /* Black */
    --muted: #888;             /* Neutral gray */

    /* Neutral colors */
    --gray-100: #fff;       /* Lightest gray */
    --gray-200: #f5f5f5;       /* Light gray */
    --gray-300: #e0e0e0;       /* Gray */
    --gray-400: #bbb;       /* Medium gray */
    --gray-500: #888;       /* Medium-dark gray */
    --gray-600: #555;       /* Dark gray */
    --gray-700: #333;       /* Darker gray */
    --gray-800: #111;       /* Very dark gray */
    --gray-900: #000;       /* Almost black */

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-base: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --transition-fast: all 0.15s ease;

    /* Font */
    --font-family-sans-serif: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;
    --font-weight-bolder: 700;
}

/* Base styles */
body {
    font-family: var(--font-family-sans-serif);
    background-color: var(--gray-100);
    color: var(--gray-800);
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

/* Remove wrapper and layout styles */
#wrapper {
    display: block;
    min-height: 100vh;
    width: 100%;
    background-color: var(--gray-100);
}

/* Remove sidebar styles */
#sidebar-wrapper {
    display: none;
}

/* Remove page content wrapper styles */
#page-content-wrapper {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Remove container styles */
.container-fluid {
    width: 100%;
    padding: 0;
    margin: 0;
}

/* Desktop layout adjustments */
@media (min-width: 992px) {
    /* Always show sidebar */
    #page-content-wrapper {
        width: calc(100% - 280px) !important;
        margin-left: 280px !important;
        padding-left: 0;
    }

    /* Ensure the content doesn't overlap with the sidebar */
    .container-fluid {
        padding: var(--spacing-lg);
    }

    /* Fix for the hamburger icon */
    .navbar-toggler {
        margin-right: var(--spacing-md);
        display: none; /* Hide the hamburger icon on desktop */
    }
}

/* Mobile layout adjustments */
@media (max-width: 991px) {
    /* Always show sidebar even on mobile */
    #sidebar-wrapper {
        width: 280px;
        transform: translateX(0) !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    #page-content-wrapper {
        width: calc(100% - 280px) !important;
        margin-left: 280px !important;
    }

    /* Hide the mobile close button */
    #mobile-close {
        display: none;
    }
}

/* Top navigation */
.top-navbar {
    background-color: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    border-bottom: 2px solid #27ae60;
    position: sticky;
    top: 0;
    z-index: 999;
}

.navbar-toggler {
    border: none;
    background-color: rgba(76, 175, 80, 0.08);
    color: var(--primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    cursor: pointer;
    z-index: 1000;
    position: relative;
}

.navbar-toggler:hover {
    background-color: rgba(76, 175, 80, 0.12);
    color: var(--primary-dark);
}

.navbar-toggler:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

.navbar-user {
    display: flex;
    align-items: center;
}

.navbar-user-info {
    margin-right: var(--spacing-md);
    text-align: right;
}

.navbar-user-name {
    font-weight: var(--font-weight-medium);
    color: var(--gray-800);
    margin-bottom: 2px;
    font-size: 0.9rem;
}

.navbar-user-role {
    font-size: 0.75rem;
    color: var(--gray-600);
    letter-spacing: 0.3px;
}

.navbar-user-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6ee7b7, #27ae60);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    box-shadow: 0 2px 10px rgba(39, 174, 96, 0.2);
    border: 2px solid white;
    text-decoration: none;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.navbar-user-avatar:hover {
    transform: none;
    box-shadow: 0 2px 10px rgba(39, 174, 96, 0.2);
}

.navbar-user-avatar:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.15);
    opacity: 0;
    transition: var(--transition-fast);
}

.navbar-user-avatar:hover:after {
    opacity: 1;
}

/* User dropdown */
.user-dropdown {
    position: relative;
}

.user-dropdown-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.user-dropdown-toggle:hover {
    background-color: inherit;
}

.user-dropdown-toggle:focus {
    outline: none;
}

.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    width: 200px;
    z-index: 1000;
    overflow: hidden;
    transition: var(--transition-fast);
    border: 1px solid var(--gray-200);
}

.user-dropdown-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition-fast);
}

.user-dropdown-item:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.user-dropdown-divider {
    height: 1px;
    background-color: var(--gray-200);
    margin: 0;
}

/* Development mode badge */
.dev-mode-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning);
    padding: 0.35em 0.65em;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.04);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition-base);
    background-color: white;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5, .card-header h6 {
    margin-bottom: 0;
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    letter-spacing: 0.3px;
}

.card-header small {
    color: var(--gray-600);
    font-weight: var(--font-weight-normal);
    margin-top: 4px;
    display: block;
}

.card-body {
    padding: var(--spacing-lg);
}

/* Dashboard cards */
.dashboard-card {
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.04);
    padding: var(--spacing-lg);
    height: 100%;
    transition: var(--transition-base);
    background-color: white;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
}

.dashboard-card:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at top right, rgba(0, 0, 0, 0.03), transparent 70%);
    border-radius: 0 0 0 100%;
    opacity: 0;
    transition: var(--transition-base);
}

.dashboard-card:hover:before {
    opacity: 1;
}

.dashboard-card-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.dashboard-card-icon i {
    font-size: 1.75rem;
    color: white;
}

.dashboard-card-title {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: var(--font-weight-medium);
}

.dashboard-card-value {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0;
    color: var(--gray-900);
    line-height: 1.2;
}

.dashboard-card-trend {
    font-size: 0.8125rem;
    margin-top: var(--spacing-sm);
    display: flex;
    align-items: center;
    font-weight: var(--font-weight-medium);
}

.trend-up {
    color: var(--success);
}

.trend-down {
    color: var(--danger);
}

.dashboard-card-trend i {
    margin-right: 4px;
    font-size: 0.75rem;
}

/* Tables */
.table {
    margin-bottom: 0;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table thead th:first-child {
    border-top-left-radius: var(--border-radius-sm);
}

.table thead th:last-child {
    border-top-right-radius: var(--border-radius-sm);
}

.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.table-hover tbody tr {
    transition: var(--transition-fast);
}

.table-hover tbody tr:hover {
    background-color: rgba(76, 175, 80, 0.04);
}

.table-hover tbody tr:hover td {
    color: var(--gray-900);
}

/* Empty state for tables */
.empty-state {
    padding: var(--spacing-xl) var(--spacing-lg);
    text-align: center;
}

.empty-state-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--gray-100);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto var(--spacing-md);
}

/* Badges */
.badge {
    font-weight: var(--font-weight-medium);
    padding: 0.4em 0.8em;
    border-radius: 50px;
    font-size: 0.75rem;
    letter-spacing: 0.3px;
    display: inline-flex;
    align-items: center;
}

.badge i {
    margin-right: 0.25rem;
    font-size: 0.7rem;
}

.bg-primary {
    background-color: var(--primary) !important;
    color: white !important;
}

.bg-success {
    background-color: var(--success) !important;
    color: white !important;
}

.bg-info {
    background-color: var(--info) !important;
    color: white !important;
}

.bg-warning {
    background-color: var(--warning) !important;
    color: white !important;
}

.bg-danger {
    background-color: var(--danger) !important;
    color: white !important;
}

.bg-light {
    background-color: var(--gray-100) !important;
    color: var(--gray-700) !important;
}

.bg-dark {
    background-color: var(--gray-800) !important;
    color: white !important;
}

.badge-primary {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary);
}

.badge-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success);
}

.badge-info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info);
}

.badge-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning);
}

.badge-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger);
}

.badge-light {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.badge-dark {
    background-color: var(--gray-800);
    color: white;
}

/* Buttons */
.btn {
    font-weight: var(--font-weight-medium);
    padding: 0.6rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.btn:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: var(--transition-fast);
}

.btn:hover:after {
    opacity: 1;
}

.btn i {
    margin-right: 0.5rem;
    font-size: 0.9em;
}

.btn-sm {
    padding: 0.35rem 1rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.8rem 2rem;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
}

.btn-success {
    background-color: var(--success);
    border-color: var(--success);
    color: white;
}

.btn-success:hover, .btn-success:focus {
    background-color: #27ae60;
    border-color: #27ae60;
    color: white;
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.2);
}

.btn-info {
    background-color: var(--info);
    border-color: var(--info);
    color: white;
}

.btn-info:hover, .btn-info:focus {
    background-color: #2980b9;
    border-color: #2980b9;
    color: white;
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--warning);
    color: white;
}

.btn-warning:hover, .btn-warning:focus {
    background-color: #e67e22;
    border-color: #e67e22;
    color: white;
    box-shadow: 0 4px 10px rgba(243, 156, 18, 0.2);
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--danger);
    color: white;
}

.btn-danger:hover, .btn-danger:focus {
    background-color: #c0392b;
    border-color: #c0392b;
    color: white;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.2);
}

.btn-light {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-700);
}

.btn-light:hover {
    background-color: var(--gray-200);
    border-color: var(--gray-300);
    color: var(--gray-800);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.btn-outline-primary {
    border-color: var(--primary);
    color: var(--primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.15);
}

.btn-outline-success {
    border-color: var(--success);
    color: var(--success);
    background-color: transparent;
}

.btn-outline-success:hover {
    background-color: var(--success);
    border-color: var(--success);
    color: white;
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.15);
}

.btn-outline-info {
    border-color: var(--info);
    color: var(--info);
    background-color: transparent;
}

.btn-outline-info:hover {
    background-color: var(--info);
    border-color: var(--info);
    color: white;
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.15);
}

.btn-outline-warning {
    border-color: var(--warning);
    color: var(--warning);
    background-color: transparent;
}

.btn-outline-warning:hover {
    background-color: var(--warning);
    border-color: var(--warning);
    color: white;
    box-shadow: 0 4px 10px rgba(243, 156, 18, 0.15);
}

.btn-outline-danger {
    border-color: var(--danger);
    color: var(--danger);
    background-color: transparent;
}

.btn-outline-danger:hover {
    background-color: var(--danger);
    border-color: var(--danger);
    color: white;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.15);
}

/* Forms */
.form-label {
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
    color: var(--gray-700);
    font-size: 0.875rem;
    letter-spacing: 0.3px;
}

.form-control {
    padding: 0.6rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    font-size: 0.875rem;
    transition: var(--transition-fast);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    background-color: white;
}

.form-control:hover {
    border-color: var(--gray-400);
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
    outline: none;
}

.form-control::placeholder {
    color: var(--gray-500);
    opacity: 0.7;
}

.form-select {
    padding: 0.6rem 2.25rem 0.6rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    font-size: 0.875rem;
    transition: var(--transition-fast);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    background-color: white;
}

.form-select:hover {
    border-color: var(--gray-400);
}

.form-select:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
    outline: none;
}

.form-check-input {
    width: 1.1em;
    height: 1.1em;
    margin-top: 0.2em;
    border: 1px solid var(--gray-400);
    transition: var(--transition-fast);
}

.form-check-input:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

.form-check-input:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
    outline: none;
}

.form-check-label {
    font-size: 0.875rem;
    color: var(--gray-700);
}

.input-group-text {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
    color: var(--gray-600);
    font-size: 0.875rem;
    padding: 0.6rem 1rem;
}

.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
}

.form-floating > label {
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert:before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: var(--spacing-md);
    font-size: 1.1rem;
}

.alert-primary {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-dark);
    border-left: 4px solid var(--primary);
}

.alert-primary:before {
    content: "\f05a";
    color: var(--primary);
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border-left: 4px solid var(--success);
}

.alert-success:before {
    content: "\f058";
    color: var(--success);
}

.alert-info {
    background-color: rgba(52, 152, 219, 0.1);
    color: #2980b9;
    border-left: 4px solid var(--info);
}

.alert-info:before {
    content: "\f129";
    color: var(--info);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: #e67e22;
    border-left: 4px solid var(--warning);
}

.alert-warning:before {
    content: "\f071";
    color: var(--warning);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: #c0392b;
    border-left: 4px solid var(--danger);
}

.alert-danger:before {
    content: "\f06a";
    color: var(--danger);
}

.alert-dismissible .btn-close {
    padding: var(--spacing-md) var(--spacing-lg);
    opacity: 0.5;
}

.alert-dismissible .btn-close:hover {
    opacity: 0.75;
}

/* Chart Styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    /* Hide sidebar by default on mobile */
    #sidebar-wrapper {
        transform: translateX(-280px);
        visibility: hidden;
        opacity: 0;
    }

    /* Show sidebar when toggled on mobile */
    #wrapper.toggled #sidebar-wrapper {
        transform: translateX(0);
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        visibility: visible;
        opacity: 1;
        margin-left: 0;
    }

    /* On mobile, when sidebar is toggled, add overlay */
    #wrapper.toggled:before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* On mobile, page content doesn't shift */
    #wrapper.toggled #page-content-wrapper {
        margin-left: 0;
        width: 100%;
        padding-left: 0;
    }

    /* Ensure the toggle button is always visible and clickable */
    .navbar-toggler {
        z-index: 1060;
        position: relative;
        margin-right: 0;
    }

    /* Ensure content is not hidden behind sidebar */
    .container-fluid {
        position: relative;
        z-index: 1;
        padding: var(--spacing-md);
    }

    /* Adjust top navbar padding */
    .top-navbar {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .dashboard-card-value {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .dashboard-card {
        margin-bottom: var(--spacing-md);
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header .btn {
        margin-top: var(--spacing-sm);
        align-self: flex-start;
    }

    .navbar-user-info {
        display: none;
    }

    .chart-container {
        height: 200px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: var(--spacing-sm);
    }

    .dashboard-card-icon {
        width: 50px;
        height: 50px;
    }

    .dashboard-card-icon i {
        font-size: 1.25rem;
    }

    .dashboard-card-value {
        font-size: 1.25rem;
    }

    .btn {
        padding: 0.5rem 1rem;
    }

    .chart-container {
        height: 180px;
    }
}

/* User Avatar Large - for profile pages */
.user-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    border: 4px solid white;
    overflow: hidden;
    background-color: var(--gray-300);
    margin: 0 auto;
    position: relative;
}

/* Status Indicator */
.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicator.active {
    background-color: var(--success);
}

.status-indicator.inactive {
    background-color: var(--gray-500);
}

/* Additional Utility Classes */
.text-primary { color: var(--primary) !important; }
.text-success { color: var(--success) !important; }
.text-info { color: var(--info) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--danger) !important; }
.text-muted { color: var(--gray-600) !important; }

.bg-primary-light { background-color: rgba(76, 175, 80, 0.1) !important; }
.bg-success-light { background-color: rgba(46, 204, 113, 0.1) !important; }
.bg-info-light { background-color: rgba(52, 152, 219, 0.1) !important; }
.bg-warning-light { background-color: rgba(243, 156, 18, 0.1) !important; }
.bg-danger-light { background-color: rgba(231, 76, 60, 0.1) !important; }

.border-primary { border-color: var(--primary) !important; }
.border-success { border-color: var(--success) !important; }
.border-info { border-color: var(--info) !important; }
.border-warning { border-color: var(--warning) !important; }
.border-danger { border-color: var(--danger) !important; }

.font-weight-medium { font-weight: var(--font-weight-medium) !important; }
.font-weight-bold { font-weight: var(--font-weight-bold) !important; }
.font-weight-bolder { font-weight: var(--font-weight-bolder) !important; }

.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* Animation Utilities */
.transition-base { transition: var(--transition-base) !important; }
.transition-slow { transition: var(--transition-slow) !important; }
.transition-fast { transition: var(--transition-fast) !important; }

.hover-lift {
    transition: var(--transition-base);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.hover-shadow:hover {
    box-shadow: var(--shadow);
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border: none;
    color: var(--gray-700);
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border-radius: var(--border-radius-sm);
}

.page-link:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.page-item.active .page-link {
    background-color: var(--primary);
    color: white;
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Additional responsive adjustments */
@media (min-width: 992px) {
    /* Default state for desktop - sidebar is hidden by default */
    #sidebar-wrapper {
        transform: translateX(-280px);
    }

    /* When toggled, show the sidebar */
    #wrapper.toggled #sidebar-wrapper {
        transform: translateX(0);
        margin-left: 0;
    }

    /* Adjust top navbar padding */
    .top-navbar {
        padding-left: var(--spacing-xl);
        padding-right: var(--spacing-xl);
    }
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    background-color: white;
}

.login-logo {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-title {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

/* Development mode badge */
.dev-mode-badge {
    background-color: rgba(255, 199, 0, 0.1);
    color: var(--warning);
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 50px;
    font-weight: var(--font-weight-medium);
}

/* User dropdown */
.user-dropdown {
    position: relative;
}

.user-dropdown-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.user-dropdown-toggle:hover {
    background-color: inherit;
}

.user-dropdown-toggle:focus {
    outline: none;
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    min-width: 10rem;
    padding: var(--spacing-sm) 0;
    margin: 0.125rem 0 0;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.user-dropdown-item {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-lg);
    clear: both;
    font-weight: var(--font-weight-normal);
    color: var(--gray-700);
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    text-decoration: none;
}

.user-dropdown-item:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.user-dropdown-divider {
    height: 0;
    margin: var(--spacing-sm) 0;
    overflow: hidden;
    border-top: 1px solid var(--gray-200);
}

/* Utilities */
.font-weight-medium {
    font-weight: var(--font-weight-medium) !important;
}

.font-weight-bold {
    font-weight: var(--font-weight-bold) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-info {
    color: var(--info) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

.text-muted {
    color: var(--gray-600) !important;
}

.bg-primary {
    background-color: var(--primary) !important;
}

.bg-success {
    background-color: var(--success) !important;
}

.bg-info {
    background-color: var(--info) !important;
}

.bg-warning {
    background-color: var(--warning) !important;
}

.bg-danger {
    background-color: var(--danger) !important;
}

.bg-light {
    background-color: var(--gray-100) !important;
}

.bg-white {
    background-color: white !important;
}

.rounded {
    border-radius: var(--border-radius) !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

.shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}

.shadow {
    box-shadow: var(--shadow) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

/* Add dark mode support for top-navbar */
@media (prefers-color-scheme: dark) {
  .top-navbar {
    background-color: var(--gray-900) !important;
    color: var(--gray-100) !important;
    border-bottom: 1px solid var(--gray-800) !important;
  }
  .top-navbar .navbar-user-name,
  .top-navbar .navbar-user-role {
    color: var(--gray-100) !important;
  }
  .navbar-toggler {
    background-color: rgba(255,255,255,0.08) !important;
    color: var(--gray-100) !important;
  }
  .navbar-toggler:hover {
    background-color: rgba(255,255,255,0.15) !important;
    color: var(--primary-light) !important;
  }
}

/* Replace all .btn-* and .badge-* and .bg-* and .text-* and .alert-* color classes with black/white/gray equivalents */
.btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger, .btn-outline-primary, .btn-outline-success, .btn-outline-info, .btn-outline-warning, .btn-outline-danger {
    background-color: #111 !important;
    border-color: #111 !important;
    color: #fff !important;
}
.btn-primary:hover, .btn-success:hover, .btn-info:hover, .btn-warning:hover, .btn-danger:hover,
.btn-primary:focus, .btn-success:focus, .btn-info:focus, .btn-warning:focus, .btn-danger:focus,
.btn-outline-primary:hover, .btn-outline-success:hover, .btn-outline-info:hover, .btn-outline-warning:hover, .btn-outline-danger:hover {
    background-color: #000 !important;
    border-color: #000 !important;
    color: #fff !important;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}
.btn-light, .btn-outline-light {
    background-color: #fff !important;
    border-color: #ccc !important;
    color: #111 !important;
}
.btn-light:hover, .btn-outline-light:hover {
    background-color: #eee !important;
    border-color: #bbb !important;
    color: #000 !important;
}
.badge, .badge-primary, .badge-success, .badge-info, .badge-warning, .badge-danger, .badge-light, .badge-dark {
    background-color: #fff !important;
    color: #111 !important;
    border: 1px solid #ccc !important;
}
.bg-primary, .bg-success, .bg-info, .bg-warning, .bg-danger, .bg-dark {
    background-color: #111 !important;
    color: #fff !important;
}
.bg-light, .bg-white {
    background-color: #fff !important;
    color: #111 !important;
}
.text-primary, .text-success, .text-info, .text-warning, .text-danger {
    color: #111 !important;
}
.text-muted {
    color: #888 !important;
}
.alert, .alert-primary, .alert-success, .alert-info, .alert-warning, .alert-danger {
    background-color: #fff !important;
    color: #111 !important;
    border-left: 4px solid #111 !important;
}

.notification-bell {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: transparent;
    color: #bbb;
    border: none;
    box-shadow: none;
    position: relative;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-bell i {
    font-size: 1.1rem;
    color: #bbb;
    font-weight: 400;
    transition: none;
}

.notification-bell:hover, .notification-bell:focus {
    background: transparent;
    color: #bbb;
    box-shadow: none;
}

.notification-bell:hover i, .notification-bell:focus i {
    color: #bbb;
}
