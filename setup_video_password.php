<?php
require_once 'api/config.php';
require_once 'api/get_secure_vimeo_embed.php';

// Setup video password for Vimeo video 1087487482
function setupVideoPassword() {
    global $pdo;
    
    try {
        echo "🔧 Setting up Vimeo video password protection...\n\n";
        
        // First, check if vimeo_password column exists
        echo "1. Checking database schema...\n";
        $checkColumn = $pdo->query("SHOW COLUMNS FROM course_videos LIKE 'vimeo_password'");
        
        if ($checkColumn->rowCount() == 0) {
            echo "   ❌ vimeo_password column doesn't exist. Creating it...\n";
            $pdo->exec("ALTER TABLE course_videos ADD COLUMN vimeo_password TEXT NULL");
            echo "   ✅ vimeo_password column created successfully!\n";
        } else {
            echo "   ✅ vimeo_password column already exists.\n";
        }
        
        // Set up password for video 1087487482
        echo "\n2. Setting up password for video 1087487482...\n";
        
        $vimeoId = '1087487482';
        $password = 'vi007i'; // Default password
        
        // Encrypt the password
        $encryptedPassword = encryptPassword($password);
        echo "   🔐 Password encrypted: " . substr($encryptedPassword, 0, 20) . "...\n";
        
        // Update the video record
        $stmt = $pdo->prepare("UPDATE course_videos SET vimeo_password = ? WHERE video_id = ?");
        $result = $stmt->execute([$encryptedPassword, $vimeoId]);
        
        if ($result) {
            echo "   ✅ Password saved to database successfully!\n";
        } else {
            echo "   ❌ Failed to save password to database.\n";
            return false;
        }
        
        // Test password retrieval
        echo "\n3. Testing password retrieval...\n";
        $retrievedPassword = getVideoPassword($vimeoId);
        
        if ($retrievedPassword === $password) {
            echo "   ✅ Password retrieval test successful!\n";
            echo "   🔑 Retrieved password: {$retrievedPassword}\n";
        } else {
            echo "   ❌ Password retrieval test failed!\n";
            echo "   Expected: {$password}\n";
            echo "   Got: " . ($retrievedPassword ?: 'null') . "\n";
            return false;
        }
        
        // Test secure embed URL generation
        echo "\n4. Testing secure embed URL generation...\n";
        $embedUrl = generateSecureEmbedUrl($vimeoId, 'com.kft.fitness', 29);
        echo "   🔗 Generated embed URL: {$embedUrl}\n";
        
        if (strpos($embedUrl, 'h=') !== false) {
            echo "   ✅ Embed URL contains password hash!\n";
        } else {
            echo "   ⚠️  Embed URL doesn't contain password hash.\n";
        }
        
        echo "\n🎉 Video password setup completed successfully!\n\n";
        
        echo "📋 NEXT STEPS:\n";
        echo "1. Go to https://vimeo.com/1087487482\n";
        echo "2. Go to Settings > Privacy\n";
        echo "3. Remove any domain restrictions\n";
        echo "4. Set privacy to 'Hide this video from vimeo.com'\n";
        echo "5. Enable password protection\n";
        echo "6. Set password to: {$password}\n";
        echo "7. Save settings\n\n";
        
        echo "🔧 The Flutter app will now automatically use the password for secure playback!\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "❌ Error setting up video password: " . $e->getMessage() . "\n";
        return false;
    }
}

// Encryption functions (if not already defined)
if (!function_exists('encryptPassword')) {
    function encryptPassword($password) {
        $key = 'kft_fitness_vimeo_key_2025'; // Use a consistent key
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($password, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
}

if (!function_exists('decryptPassword')) {
    function decryptPassword($encryptedPassword) {
        $key = 'kft_fitness_vimeo_key_2025'; // Use the same key
        $data = base64_decode($encryptedPassword);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
}

// Run the setup
if (php_sapi_name() === 'cli') {
    // Command line execution
    setupVideoPassword();
} else {
    // Web execution
    header('Content-Type: text/plain');
    setupVideoPassword();
}
?>
