<?php
/**
 * KFT Fitness Backup Script
 * 
 * This script creates backups of the database and allows downloading them.
 * Only accessible to super admins.
 */

// Include required files
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is a super admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'super_admin') {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Set page title
$pageTitle = 'Database Backup';

// Include header
require_once 'includes/header.php';

// Function to create database backup
function createDatabaseBackup() {
    // Set variables
    $timestamp = date('Y-m-d_H-i-s');
    $backupDir = __DIR__ . '/backups';
    $backupFilename = "kft_fitness_db_{$timestamp}.sql";
    $backupPath = "{$backupDir}/{$backupFilename}";
    
    // Create backup directory if it doesn't exist
    if (!file_exists($backupDir)) {
        if (!mkdir($backupDir, 0755, true)) {
            return [
                'success' => false,
                'message' => 'Failed to create backup directory'
            ];
        }
    }
    
    // Check if backup directory is writable
    if (!is_writable($backupDir)) {
        return [
            'success' => false,
            'message' => 'Backup directory is not writable'
        ];
    }
    
    // Create database backup using mysqldump
    $command = sprintf(
        'mysqldump -h %s -u %s %s %s > %s',
        escapeshellarg(DB_HOST),
        escapeshellarg(DB_USER),
        !empty(DB_PASS) ? '-p' . escapeshellarg(DB_PASS) : '',
        escapeshellarg(DB_NAME),
        escapeshellarg($backupPath)
    );
    
    // Execute command
    exec($command, $output, $returnCode);
    
    // Check if backup was successful
    if ($returnCode !== 0) {
        return [
            'success' => false,
            'message' => 'Failed to create database backup. Error code: ' . $returnCode
        ];
    }
    
    // Create a compressed version of the backup
    $compressedBackupPath = "{$backupPath}.gz";
    $compressCommand = sprintf(
        'gzip -c %s > %s',
        escapeshellarg($backupPath),
        escapeshellarg($compressedBackupPath)
    );
    
    exec($compressCommand, $compressOutput, $compressReturnCode);
    
    // Check if compression was successful
    if ($compressReturnCode === 0 && file_exists($compressedBackupPath)) {
        // Remove the uncompressed backup to save space
        unlink($backupPath);
        
        return [
            'success' => true,
            'message' => 'Database backup created successfully',
            'filename' => "{$backupFilename}.gz",
            'path' => $compressedBackupPath,
            'size' => filesize($compressedBackupPath)
        ];
    } else {
        return [
            'success' => true,
            'message' => 'Database backup created successfully (without compression)',
            'filename' => $backupFilename,
            'path' => $backupPath,
            'size' => filesize($backupPath)
        ];
    }
}

// Handle backup creation
$backupResult = null;
if (isset($_POST['create_backup'])) {
    $backupResult = createDatabaseBackup();
    
    if ($backupResult['success']) {
        Utilities::setFlashMessage('success', $backupResult['message']);
    } else {
        Utilities::setFlashMessage('error', $backupResult['message']);
    }
}

// Handle backup download
if (isset($_GET['download']) && !empty($_GET['file'])) {
    $filename = basename($_GET['file']);
    $backupDir = __DIR__ . '/backups';
    $filePath = "{$backupDir}/{$filename}";
    
    // Check if file exists and is readable
    if (file_exists($filePath) && is_readable($filePath)) {
        // Set headers for download
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filePath));
        
        // Clear output buffer
        ob_clean();
        flush();
        
        // Read file and output to browser
        readfile($filePath);
        exit;
    } else {
        Utilities::setFlashMessage('error', 'Backup file not found or not readable');
    }
}

// Get list of existing backups
$backupDir = __DIR__ . '/backups';
$backups = [];

if (file_exists($backupDir) && is_dir($backupDir)) {
    $files = scandir($backupDir);
    
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && (strpos($file, 'kft_fitness_db_') === 0)) {
            $backups[] = [
                'filename' => $file,
                'path' => "{$backupDir}/{$file}",
                'size' => filesize("{$backupDir}/{$file}"),
                'date' => filemtime("{$backupDir}/{$file}")
            ];
        }
    }
    
    // Sort backups by date (newest first)
    usort($backups, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><?php echo $pageTitle; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
    </ol>
    
    <?php Utilities::displayFlashMessages(); ?>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-database me-1"></i>
                    Create Database Backup
                </div>
                <div class="card-body">
                    <p>Create a backup of the KFT Fitness database. This will generate a SQL dump file that can be used to restore the database if needed.</p>
                    
                    <form method="post" action="">
                        <button type="submit" name="create_backup" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> Create Database Backup
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-list me-1"></i>
                    Existing Backups
                </div>
                <div class="card-body">
                    <?php if (empty($backups)): ?>
                        <p>No backups found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Filename</th>
                                        <th>Size</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($backups as $backup): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($backup['filename']); ?></td>
                                            <td><?php echo Utilities::formatFileSize($backup['size']); ?></td>
                                            <td><?php echo date('Y-m-d H:i:s', $backup['date']); ?></td>
                                            <td>
                                                <a href="?download=1&file=<?php echo urlencode($backup['filename']); ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
