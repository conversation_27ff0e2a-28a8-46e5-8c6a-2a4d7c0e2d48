<?php
/**
 * Token Validation Test Script
 * 
 * This script helps diagnose issues with token validation.
 * It prints out detailed information about the token and validation process.
 */

require_once 'config.php';

// Get authorization header
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? '';

// Output debug information
echo "<h1>Token Validation Test</h1>";
echo "<h2>Request Headers</h2>";
echo "<pre>";
print_r($headers);
echo "</pre>";

echo "<h2>Authorization Header</h2>";
echo "<p>Raw: " . htmlspecialchars($authHeader) . "</p>";

// Check if token is provided
if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    echo "<p style='color: red;'>Error: Authorization token required</p>";
    echo "<p>Expected format: <code>Authorization: Bearer YOUR_TOKEN_HERE</code></p>";
    exit;
}

$token = $matches[1];
echo "<p>Extracted Token: " . htmlspecialchars($token) . "</p>";

try {
    // Connect to database
    $conn = getDbConnection();
    echo "<p style='color: green;'>Database connection successful</p>";

    // Check if token exists and is valid
    $query = "SELECT t.*, u.id as user_id, u.username, u.name, u.is_premium, u.is_active
              FROM api_tokens t
              JOIN users u ON t.user_id = u.id
              WHERE t.token = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    echo "<h2>Token Query Results</h2>";
    echo "<p>Query: " . htmlspecialchars($query) . "</p>";
    echo "<p>Token used in query: " . htmlspecialchars($token) . "</p>";
    echo "<p>Result rows: " . $result->num_rows . "</p>";

    if ($result->num_rows === 0) {
        echo "<p style='color: red;'>Error: Token not found in database</p>";
        
        // Check if token exists but is expired
        $expiredQuery = "SELECT t.*, u.id as user_id, u.username, u.name, u.is_premium, u.is_active
                      FROM api_tokens t
                      JOIN users u ON t.user_id = u.id
                      WHERE t.token = ?";
        $expiredStmt = $conn->prepare($expiredQuery);
        $expiredStmt->bind_param("s", $token);
        $expiredStmt->execute();
        $expiredResult = $expiredStmt->get_result();
        
        if ($expiredResult->num_rows > 0) {
            $tokenData = $expiredResult->fetch_assoc();
            echo "<p>Token found but expired. Expiry date: " . $tokenData['expires_at'] . "</p>";
            echo "<p>Current server time: " . date('Y-m-d H:i:s') . "</p>";
        } else {
            echo "<p>Token not found in database at all.</p>";
        }
        
        exit;
    }

    $tokenData = $result->fetch_assoc();
    
    echo "<h2>Token Data</h2>";
    echo "<pre>";
    print_r($tokenData);
    echo "</pre>";

    // Check expiration
    $now = new DateTime();
    $expiresAt = new DateTime($tokenData['expires_at']);
    
    echo "<h2>Expiration Check</h2>";
    echo "<p>Current time: " . $now->format('Y-m-d H:i:s') . "</p>";
    echo "<p>Token expires: " . $expiresAt->format('Y-m-d H:i:s') . "</p>";
    
    if ($now > $expiresAt) {
        echo "<p style='color: red;'>Error: Token has expired</p>";
        exit;
    } else {
        echo "<p style='color: green;'>Token is still valid (not expired)</p>";
    }

    // Check if user is active
    if (!$tokenData['is_active']) {
        echo "<p style='color: red;'>Error: User account is inactive</p>";
        exit;
    } else {
        echo "<p style='color: green;'>User account is active</p>";
    }

    echo "<h2>Validation Successful</h2>";
    echo "<p style='color: green;'>Token is valid and user is authenticated</p>";
    echo "<p>User ID: " . $tokenData['user_id'] . "</p>";
    echo "<p>Username: " . htmlspecialchars($tokenData['username']) . "</p>";
    echo "<p>Name: " . htmlspecialchars($tokenData['name']) . "</p>";

} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}
