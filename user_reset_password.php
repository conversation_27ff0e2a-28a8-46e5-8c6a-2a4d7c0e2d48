<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Check if admin is logged in
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    Utilities::setFlashMessage('danger', 'You do not have permission to access this page.');
    Utilities::redirect('login.php');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('danger', 'Invalid user ID.');
    Utilities::redirect('users.php');
}

$userId = (int)$_GET['id'];

// Get user details
$userQuery = "SELECT id, username, name, email FROM users WHERE id = ?";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('danger', 'User not found.');
    Utilities::redirect('users.php');
}

$user = $result->fetch_assoc();

// Log the password reset attempt
function logPasswordReset($userId, $adminId, $success) {
    global $conn;
    $logQuery = "INSERT INTO admin_logs (admin_id, action, target_id, details, ip_address, success)
                VALUES (?, 'password_reset', ?, ?, ?, ?)";
    $stmt = $conn->prepare($logQuery);
    $details = "Password reset for user ID: $userId";
    $ipAddress = $_SERVER['REMOTE_ADDR'];
    $stmt->bind_param("iissi", $adminId, $userId, $details, $ipAddress, $success);
    $stmt->execute();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $sendEmail = isset($_POST['send_email']) ? true : false;
    $resetMethod = $_POST['reset_method'] ?? 'manual';

    // Validate inputs
    $errors = [];

    if ($resetMethod === 'manual') {
        if (empty($password)) {
            $errors[] = 'Password is required.';
        } else {
            $policyResult = validate_password_policy($password);
            if ($policyResult !== true) {
                $errors[] = $policyResult;
            }
        }
        if ($password !== $confirmPassword) {
            $errors[] = 'Passwords do not match.';
        }
    }

    if (empty($errors)) {
        $adminId = $auth->getUserId();

        if ($resetMethod === 'manual') {
            // Manual password reset
            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Update user password
            $updateQuery = "UPDATE users SET password = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("si", $hashedPassword, $userId);

            if ($stmt->execute()) {
                // Log successful password reset
                logPasswordReset($userId, $adminId, 1);

                if ($sendEmail && !empty($user['email'])) {
                    // In a real application, you would send an email to the user
                    // For this implementation, we'll just show a message
                    Utilities::setFlashMessage('info', 'In a production environment, an email would be sent to the user with their new password.');
                }

                Utilities::setFlashMessage('success', 'Password has been reset successfully.');
                Utilities::redirect('users.php');
            } else {
                // Log failed password reset
                logPasswordReset($userId, $adminId, 0);

                Utilities::setFlashMessage('danger', 'Failed to reset password: ' . $conn->error);
            }
        } else {
            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry

            // Delete any existing tokens
            $deleteQuery = "DELETE FROM password_resets WHERE user_id = ?";
            $deleteStmt = $conn->prepare($deleteQuery);
            $deleteStmt->bind_param("i", $userId);
            $deleteStmt->execute();

            // Store token in database
            $insertQuery = "INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("iss", $userId, $token, $expires);

            if ($insertStmt->execute()) {
                // Log successful token generation
                logPasswordReset($userId, $adminId, 1);

                // Generate reset link
                $resetLink = "http://{$_SERVER['HTTP_HOST']}/forgot_password.php?step=reset&token=$token&email=" . urlencode($user['email']);

                if (!empty($user['email'])) {
                    // In a real application, you would send an email with the reset link
                    // For this implementation, we'll just show the link
                    Utilities::setFlashMessage('info', 'In a production environment, a password reset link would be emailed to the user.');
                    $_SESSION['reset_link'] = $resetLink;
                    $_SESSION['reset_email'] = $user['email'];
                }

                Utilities::setFlashMessage('success', 'Password reset link has been generated successfully.');
                Utilities::redirect('user_reset_confirm.php?id=' . $userId);
            } else {
                // Log failed token generation
                logPasswordReset($userId, $adminId, 0);

                Utilities::setFlashMessage('danger', 'Failed to generate reset token: ' . $conn->error);
            }
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}
?>

<style>
.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Reset Password</h1>
    <a href="users.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Users
    </a>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> You are resetting the password for <strong><?php echo htmlspecialchars($user['name']); ?></strong> (Username: <strong><?php echo htmlspecialchars($user['username']); ?></strong>).
        </div>

        <form method="post" action="user_reset_password.php?id=<?php echo $userId; ?>">
            <div class="mb-4">
                <h5>Reset Method</h5>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="reset_method" id="reset_manual" value="manual" checked onchange="toggleResetMethod()">
                    <label class="form-check-label" for="reset_manual">Set New Password</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="reset_method" id="reset_link" value="link" onchange="toggleResetMethod()">
                    <label class="form-check-label" for="reset_link">Send Reset Link</label>
                </div>
            </div>

            <div id="manual_reset_section">
                <div class="mb-3">
                    <label for="password" class="form-label">New Password <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" name="password" oninput="checkPasswordStrength()">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="form-text">Password must be at least 6 characters long.</div>
                    <div class="mt-2">
                        <div class="progress" style="height: 5px;">
                            <div id="password-strength" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small id="password-strength-text" class="form-text"></small>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="generate_password" onchange="generatePassword()">
                        <label class="form-check-label" for="generate_password">
                            Generate Strong Password
                        </label>
                    </div>
                </div>

                <?php if (!empty($user['email'])): ?>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1">
                        <label class="form-check-label" for="send_email">
                            Notify user via email (<?php echo htmlspecialchars($user['email']); ?>)
                        </label>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div id="link_reset_section" style="display: none;">
                <?php if (empty($user['email'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This user does not have an email address. Please add an email address to their profile first.
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> A password reset link will be generated and can be sent to the user's email address: <strong><?php echo htmlspecialchars($user['email']); ?></strong>
                </div>
                <?php endif; ?>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary" <?php echo empty($user['email']) ? 'id="submitBtn"' : ''; ?>>
                    <i class="fas fa-key me-2"></i> Reset Password
                </button>
                <a href="users.php" class="btn btn-outline-secondary ms-2">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
function toggleResetMethod() {
    const manualSection = document.getElementById('manual_reset_section');
    const linkSection = document.getElementById('link_reset_section');
    const isManual = document.getElementById('reset_manual').checked;
    const submitBtn = document.querySelector('button[type="submit"]');

    if (isManual) {
        manualSection.style.display = 'block';
        linkSection.style.display = 'none';
        submitBtn.innerHTML = '<i class="fas fa-key me-2"></i> Reset Password';

        // Re-enable required attributes
        document.getElementById('password').setAttribute('required', '');
        document.getElementById('confirm_password').setAttribute('required', '');
    } else {
        manualSection.style.display = 'none';
        linkSection.style.display = 'block';
        submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i> Generate Reset Link';

        // Remove required attributes
        document.getElementById('password').removeAttribute('required');
        document.getElementById('confirm_password').removeAttribute('required');
    }
}

function generatePassword() {
    if (document.getElementById('generate_password').checked) {
        // Generate a random password
        const length = 12;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-+=";
        let password = "";

        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * charset.length);
            password += charset[randomIndex];
        }

        document.getElementById('password').value = password;
        document.getElementById('confirm_password').value = password;
        document.getElementById('password').type = 'text';

        // Update password strength
        checkPasswordStrength();
    } else {
        document.getElementById('password').value = '';
        document.getElementById('confirm_password').value = '';
        document.getElementById('password').type = 'password';

        // Reset password strength
        updatePasswordStrength(0, '');
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('password').value;
    let strength = 0;
    let feedback = '';

    if (password.length === 0) {
        updatePasswordStrength(0, '');
        return;
    }

    // Length check
    if (password.length < 6) {
        updatePasswordStrength(10, 'Very weak - Too short');
        return;
    } else if (password.length >= 12) {
        strength += 25;
    } else if (password.length >= 8) {
        strength += 15;
    } else {
        strength += 5;
    }

    // Complexity checks
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /[0-9]/.test(password);
    const hasSpecialChars = /[^a-zA-Z0-9]/.test(password);

    if (hasLowercase) strength += 10;
    if (hasUppercase) strength += 15;
    if (hasNumbers) strength += 15;
    if (hasSpecialChars) strength += 20;

    // Variety check
    let variety = 0;
    if (hasLowercase) variety++;
    if (hasUppercase) variety++;
    if (hasNumbers) variety++;
    if (hasSpecialChars) variety++;

    if (variety >= 4) {
        strength += 15;
    } else if (variety === 3) {
        strength += 10;
    } else if (variety === 2) {
        strength += 5;
    }

    // Cap at 100
    strength = Math.min(strength, 100);

    // Determine feedback
    if (strength >= 80) {
        feedback = 'Very strong';
    } else if (strength >= 60) {
        feedback = 'Strong';
    } else if (strength >= 40) {
        feedback = 'Moderate';
    } else if (strength >= 20) {
        feedback = 'Weak';
    } else {
        feedback = 'Very weak';
    }

    updatePasswordStrength(strength, feedback);
}

function updatePasswordStrength(strength, feedback) {
    const progressBar = document.getElementById('password-strength');
    const feedbackText = document.getElementById('password-strength-text');

    progressBar.style.width = strength + '%';
    progressBar.setAttribute('aria-valuenow', strength);

    // Set color based on strength
    if (strength >= 80) {
        progressBar.className = 'progress-bar bg-success';
    } else if (strength >= 60) {
        progressBar.className = 'progress-bar bg-info';
    } else if (strength >= 40) {
        progressBar.className = 'progress-bar bg-primary';
    } else if (strength >= 20) {
        progressBar.className = 'progress-bar bg-warning';
    } else {
        progressBar.className = 'progress-bar bg-danger';
    }

    feedbackText.textContent = feedback;
}

// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);

    // Toggle icon
    this.querySelector('i').classList.toggle('fa-eye');
    this.querySelector('i').classList.toggle('fa-eye-slash');
});

<?php if (empty($user['email'])): ?>
// Disable link reset option if no email
document.getElementById('reset_link').disabled = true;
document.getElementById('submitBtn').addEventListener('click', function(e) {
    if (document.getElementById('reset_link').checked) {
        e.preventDefault();
        alert('This user does not have an email address. Please add an email address to their profile first.');
    }
});
<?php endif; ?>
</script>

<?php require_once 'includes/footer.php'; ?>
